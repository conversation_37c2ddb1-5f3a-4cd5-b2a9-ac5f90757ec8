namespace Shared.Models;

/// <summary>
/// Thông tin xác thực MobiFone được cache
/// </summary>
public class MobiFoneAuthenticationInfo
{
    /// <summary>
    /// Token xác thực
    /// </summary>
    public string Token { get; set; } = string.Empty;

    /// <summary>
    /// Mã đơn vị cung cấp dịch vụ
    /// </summary>
    public string MaDvcs { get; set; } = string.Empty;

    /// <summary>
    /// Thời gian hết hạn token
    /// </summary>
    public DateTime ExpiresAt { get; set; }

    /// <summary>
    /// M<PERSON> số thuế
    /// </summary>
    public string TaxCode { get; set; } = string.Empty;

    /// <summary>
    /// Kiểm tra token có còn hợp lệ không
    /// </summary>
    public bool IsValid => DateTime.UtcNow < ExpiresAt && !string.IsNullOrEmpty(Token);

    /// <summary>
    /// Thời gian còn lại trướ<PERSON> khi hết hạn (phút)
    /// </summary>
    public double MinutesUntilExpiry => (ExpiresAt - DateTime.UtcNow).TotalMinutes;
}
