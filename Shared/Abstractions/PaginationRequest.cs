﻿namespace BuildingBlocks.Abstractions;

public record PaginationRequest : Request
{
    private int _pageIndex = 1;
    public int? PageIndex
    {
        get => _pageIndex;
        set
        {
            if (value != null && value > 0)
                _pageIndex = value.Value;
            else
                _pageIndex = 1;
        }
    }

    private int _pageSize = 100;
    public int? PageSize
    {
        get => _pageSize;
        set
        {
            if (value == null)
                _pageSize = 100;
            else if (value < 1)
                _pageSize = 1;
            else if (value > 1000)
                _pageSize = 1000;
            else
                _pageSize = value.Value;
        }
    }
}
