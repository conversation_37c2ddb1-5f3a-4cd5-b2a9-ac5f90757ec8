using System.Linq.Expressions;

namespace BuildingBlocks.Abstractions;

public interface IPaginationHelper
{
    PaginationResponse<T> Paginate<T>(
        IQueryable<T> query,
        int pageIndex,
        int pageSize,
        Expression<Func<T, bool>>? filter = null,
        string? search = null,
        string[]? searchFields = null,
        string? sortField = null,
        string? sortDirection = "asc")
        where T : class;

    Task<PaginationResponse<T>> PaginateAsync<T>(
        IQueryable<T> query,
        int pageIndex,
        int pageSize,
        Expression<Func<T, bool>>? filter = null,
        string? search = null,
        string[]? searchFields = null,
        string? sortField = null,
        string? sortDirection = "asc")
        where T : class;
}
