using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Shared.Interfaces;
using Shared.Models;
using System.Collections.Concurrent;

namespace Shared.Services;

/// <summary>
/// Service quản lý cache authentication cho MobiFone API
/// Thread-safe implementation với MemoryCache
/// </summary>
public class MobiFoneAuthenticationCacheService : IMobiFoneAuthenticationCacheService
{
    private readonly IMemoryCache _memoryCache;
    private readonly ILogger<MobiFoneAuthenticationCacheService> _logger;
    private readonly IMobiFoneCredentialsProvider _credentialsProvider;
    private readonly IMobiFoneLoginService _loginService;
    private readonly ConcurrentDictionary<string, SemaphoreSlim> _loginSemaphores;
    private const string CACHE_KEY_PREFIX = "mobifone_auth_";
    private const int DEFAULT_CACHE_DURATION_MINUTES = 55; // Token thường có thời hạn 60 phút, cache 55 phút để an toàn

    public MobiFoneAuthenticationCacheService(
        IMemoryCache memoryCache,
        ILogger<MobiFoneAuthenticationCacheService> logger,
        IMobiFoneCredentialsProvider credentialsProvider,
        IMobiFoneLoginService loginService)
    {
        _memoryCache = memoryCache;
        _logger = logger;
        _credentialsProvider = credentialsProvider;
        _loginService = loginService;
        _loginSemaphores = new ConcurrentDictionary<string, SemaphoreSlim>();
    }

    public async Task<MobiFoneAuthenticationInfo?> GetAuthenticationAsync(string taxCode, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(taxCode))
        {
            _logger.LogWarning("TaxCode is null or empty");
            return null;
        }

        var cacheKey = GetCacheKey(taxCode);

        // Kiểm tra cache trước
        if (_memoryCache.TryGetValue(cacheKey, out MobiFoneAuthenticationInfo? cachedAuth) && 
            cachedAuth != null && cachedAuth.IsValid)
        {
            _logger.LogDebug("Cache hit for TaxCode: {TaxCode}, expires in {Minutes} minutes", 
                taxCode, cachedAuth.MinutesUntilExpiry);
            return cachedAuth;
        }

        _logger.LogInformation("Cache miss or expired for TaxCode: {TaxCode}, performing login", taxCode);

        // Sử dụng semaphore để tránh multiple concurrent login cho cùng tax code
        var semaphore = _loginSemaphores.GetOrAdd(taxCode, _ => new SemaphoreSlim(1, 1));

        try
        {
            await semaphore.WaitAsync(cancellationToken);

            // Double-check sau khi acquire lock
            if (_memoryCache.TryGetValue(cacheKey, out cachedAuth) && 
                cachedAuth != null && cachedAuth.IsValid)
            {
                _logger.LogDebug("Cache hit after lock for TaxCode: {TaxCode}", taxCode);
                return cachedAuth;
            }

            // Thực hiện login
            var authInfo = await PerformLoginAsync(taxCode, cancellationToken);
            
            if (authInfo != null)
            {
                // Cache với thời gian hết hạn
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(DEFAULT_CACHE_DURATION_MINUTES),
                    SlidingExpiration = null, // Không sử dụng sliding expiration
                    Priority = CacheItemPriority.High
                };

                _memoryCache.Set(cacheKey, authInfo, cacheOptions);
                
                _logger.LogInformation("Successfully cached authentication for TaxCode: {TaxCode}, expires at: {ExpiresAt}", 
                    taxCode, authInfo.ExpiresAt);
            }

            return authInfo;
        }
        finally
        {
            semaphore.Release();
        }
    }

    public void InvalidateCache(string taxCode)
    {
        if (string.IsNullOrEmpty(taxCode)) return;

        var cacheKey = GetCacheKey(taxCode);
        _memoryCache.Remove(cacheKey);
        
        _logger.LogInformation("Invalidated cache for TaxCode: {TaxCode}", taxCode);
    }

    public void ClearAllCache()
    {
        // MemoryCache không có method clear all, nhưng có thể implement nếu cần
        _logger.LogInformation("Clear all cache requested - individual invalidation required");
    }

    public bool IsCacheValid(string taxCode)
    {
        if (string.IsNullOrEmpty(taxCode)) return false;

        var cacheKey = GetCacheKey(taxCode);
        
        if (_memoryCache.TryGetValue(cacheKey, out MobiFoneAuthenticationInfo? cachedAuth))
        {
            return cachedAuth?.IsValid ?? false;
        }

        return false;
    }

    private string GetCacheKey(string taxCode) => $"{CACHE_KEY_PREFIX}{taxCode}";

    /// <summary>
    /// Thực hiện login với MobiFone API
    /// </summary>
    private async Task<MobiFoneAuthenticationInfo?> PerformLoginAsync(string taxCode, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Starting login process for TaxCode: {TaxCode}", taxCode);

            // 1. Lấy thông tin login từ credentials provider
            var credentials = await _credentialsProvider.GetCredentialsAsync(taxCode, cancellationToken);

            if (credentials == null)
            {
                _logger.LogWarning("No credentials found for TaxCode: {TaxCode}", taxCode);
                return null;
            }

            // 2. Gọi MobiFone Login API
            var authInfo = await _loginService.LoginAsync(credentials, cancellationToken);

            if (authInfo == null)
            {
                _logger.LogError("MobiFone login failed for TaxCode: {TaxCode}", taxCode);
                return null;
            }

            // 3. Set expiration time
            authInfo.ExpiresAt = DateTime.UtcNow.AddMinutes(DEFAULT_CACHE_DURATION_MINUTES);

            _logger.LogInformation("Successfully logged in for TaxCode: {TaxCode}, Token expires at: {ExpiresAt}",
                taxCode, authInfo.ExpiresAt);

            return authInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to login for TaxCode: {TaxCode}", taxCode);
            return null;
        }
    }
}
