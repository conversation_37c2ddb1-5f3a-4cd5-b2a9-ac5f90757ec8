﻿using BuildingBlocks.CQRS;
using BuildingBlocks.Abstractions;
using FluentValidation;
using MediatR;
using System.Diagnostics;

namespace BuildingBlocks.Behaviors;

public class ValidationBehavior<TRequest, TResponse>
    (IEnumerable<IValidator<TRequest>> validators)
    : IPipelineBehavior<TRequest, TResponse>
    where TRequest : notnull
{
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        // Skip validation if no validators are registered
        if (!validators.Any())
            return await next();

        var context = new ValidationContext<TRequest>(request);

        var validationResults =
            await Task.WhenAll(validators.Select(v => v.ValidateAsync(context, cancellationToken)));

        var failures =
            validationResults
            .Where(r => r.Errors.Count != 0)
            .SelectMany(r => r.Errors)
            .ToList();

        if (failures.Count != 0)
        {
            // Check if TResponse is Response<T> type
            if (typeof(TResponse).IsGenericType &&
                typeof(TResponse).GetGenericTypeDefinition() == typeof(Response<>))
            {
                var dataType = typeof(TResponse).GetGenericArguments()[0];
                var traceId = Activity.Current?.Id ?? Guid.NewGuid().ToString();

                // Create error response with validation failures
                var errorMessages = failures.Select(f => f.ErrorMessage).ToList();
                var response = Activator.CreateInstance(typeof(TResponse));

                if (response != null)
                {
                    var codeProperty = typeof(TResponse).GetProperty("Code");
                    var messageProperty = typeof(TResponse).GetProperty("Message");
                    var errorsProperty = typeof(TResponse).GetProperty("Errors");
                    var traceIdProperty = typeof(TResponse).GetProperty("TraceId");

                    codeProperty?.SetValue(response, "400");
                    messageProperty?.SetValue(response, "Validation failed");
                    errorsProperty?.SetValue(response, errorMessages);
                    traceIdProperty?.SetValue(response, traceId);

                    return (TResponse)response;
                }
            }

            // Fallback to throwing exception for non-Response types
            throw new ValidationException(failures);
        }

        return await next();
    }
}
