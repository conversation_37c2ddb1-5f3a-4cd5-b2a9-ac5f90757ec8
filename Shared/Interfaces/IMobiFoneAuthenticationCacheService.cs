using Shared.Models;

namespace Shared.Interfaces;

/// <summary>
/// Interface để lấy thông tin login credentials từ database
/// </summary>
public interface IMobiFoneCredentialsProvider
{
    /// <summary>
    /// L<PERSON>y thông tin login credentials cho tax code
    /// </summary>
    /// <param name="taxCode">Mã số thuế</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Login credentials hoặc null nếu không tìm thấy</returns>
    Task<MobiFoneLoginCredentials?> GetCredentialsAsync(string taxCode, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface để thực hiện login với MobiFone API
/// </summary>
public interface IMobiFoneLoginService
{
    /// <summary>
    /// Thực hiện login với MobiFone API
    /// </summary>
    /// <param name="credentials">Thông tin đăng nhập</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Authentication info hoặc null nếu login fail</returns>
    Task<MobiFoneAuthenticationInfo?> LoginAsync(MobiFoneLoginCredentials credentials, CancellationToken cancellationToken = default);
}

/// <summary>
/// Service quản lý cache authentication cho MobiFone API
/// </summary>
public interface IMobiFoneAuthenticationCacheService
{
    /// <summary>
    /// Lấy thông tin xác thực cho tax code
    /// Tự động login nếu cache miss hoặc token expired
    /// </summary>
    /// <param name="taxCode">Mã số thuế</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Thông tin xác thực</returns>
    Task<MobiFoneAuthenticationInfo?> GetAuthenticationAsync(string taxCode, CancellationToken cancellationToken = default);

    /// <summary>
    /// Xóa cache cho tax code (khi login fail hoặc token invalid)
    /// </summary>
    /// <param name="taxCode">Mã số thuế</param>
    void InvalidateCache(string taxCode);

    /// <summary>
    /// Xóa toàn bộ cache
    /// </summary>
    void ClearAllCache();

    /// <summary>
    /// Kiểm tra cache có tồn tại và valid cho tax code không
    /// </summary>
    /// <param name="taxCode">Mã số thuế</param>
    /// <returns>True nếu cache valid</returns>
    bool IsCacheValid(string taxCode);
}
