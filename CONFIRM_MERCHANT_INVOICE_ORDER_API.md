# API Xác Thực MerchantInvoiceOrder

## Mô tả
API này cho phép xác thực một MerchantInvoiceOrder bằng cách chuyển status từ `PENDING` sang `COMPLETED`. Chỉ những order có status `PENDING` mới có thể được xác thực.

## Endpoint
```
PATCH /zenInvoice/api/MerchantInvoiceOrder/{id}/confirm
```

## Authorization
- **Policy**: `SimpleBearerAuth`
- **Header**: `Authorization: Bearer <token>`

## Path Parameters
- `id` (Guid, required): ID của MerchantInvoiceOrder cần xác thực

## Request Body
```json
{
  "notes": "string (optional, max 500 chars)"
}
```

### Request Fields
- `notes` (string, optional): <PERSON><PERSON> chú hoặc lý do xác thực, tối đa 500 ký tự

## Response

### Success Response (200 OK)
```json
{
  "data": {
    "id": "guid",
    "status": "COMPLETED",
    "confirmedAt": "2024-01-15T10:30:00Z",
    "notes": "string"
  },
  "message": "MerchantInvoiceOrder confirmed successfully",
  "code": "000"
}
```

### Error Responses

#### 400 Bad Request
```json
{
  "message": "Only PENDING orders can be confirmed",
  "code": "400"
}
```
hoặc
```json
{
  "message": "MerchantInvoiceOrder is already completed",
  "code": "400"
}
```

#### 401 Unauthorized
```json
{
  "message": "Unauthorized access",
  "code": "401"
}
```

#### 404 Not Found
```json
{
  "message": "MerchantInvoiceOrder not found",
  "code": "404"
}
```

#### 500 Internal Server Error
```json
{
  "message": "An internal server error occurred while confirming MerchantInvoiceOrder",
  "code": "500"
}
```

## Business Logic
1. Kiểm tra quyền truy cập của partner hiện tại
2. Tìm MerchantInvoiceOrder theo ID và đảm bảo thuộc về partner hiện tại
3. Kiểm tra status hiện tại phải là `PENDING`
4. Cập nhật status thành `COMPLETED`
5. Cập nhật thời gian `UpdatedAt`
6. Trả về thông tin xác thực

## Status Enum Values
- `PENDING = 1`: Chờ xử lý
- `COMPLETED = 2`: Đã hoàn thành

## Validation Rules
- ID phải là GUID hợp lệ
- Notes không được vượt quá 500 ký tự
- Chỉ order có status `PENDING` mới có thể được xác thực
- Partner chỉ có thể xác thực order thuộc về mình

## Example Usage

### Request
```bash
curl -X PATCH "https://api.example.com/zenInvoice/api/MerchantInvoiceOrder/123e4567-e89b-12d3-a456-************/confirm" \
  -H "Authorization: Bearer your-token-here" \
  -H "Content-Type: application/json" \
  -d '{
    "notes": "Xác thực order sau khi kiểm tra đầy đủ"
  }'
```

### Response
```json
{
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "status": "COMPLETED",
    "confirmedAt": "2024-01-15T10:30:00Z",
    "notes": "Xác thực order sau khi kiểm tra đầy đủ"
  },
  "message": "MerchantInvoiceOrder confirmed successfully",
  "code": "000"
}
```
