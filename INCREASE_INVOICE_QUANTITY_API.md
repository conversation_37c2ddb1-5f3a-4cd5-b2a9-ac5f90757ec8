# API Tăng Số Lượng Hóa Đơn Khả Dụng

## <PERSON><PERSON> tả
API này cho phép tăng số lượng hóa đơn khả dụng cho một chi nhánh merchant cụ thể bằng cách cập nhật `TotalInvoiceQuantity` và `RemainingInvoiceQuantity` của MerchantInvoiceOrder đang active.

## Endpoint
```
POST /zenInvoice/api/MerchantInvoiceOrder/increase-quantity
```

## Authorization
- **Policy**: `SimpleBearerAuth`
- **Header**: `Authorization: Bearer <token>`

## Request Body
```json
{
  "merchantBranchId": "guid",
  "quantity": "integer (> 0)",
  "description": "string (optional, max 500 chars)"
}
```

### Validation Rules
- `merchantBranchId`: Bắt buộc, phải tồn tại trong `MerchantBranchInvoiceAccount`
- `quantity`: <PERSON><PERSON><PERSON> bu<PERSON>, phải > 0
- `description`: <PERSON><PERSON><PERSON>, tối đa 500 ký tự

## Response

### Success (200 OK)
```json
{
  "code": "000",
  "message": "Success",
  "data": {
    "merchantBranchId": "guid",
    "quantityAdded": "integer",
    "newRemainingQuantity": "integer",
    "newTotalQuantity": "integer", 
    "updatedOrderId": "guid",
    "updatedAt": "datetime",
    "description": "string"
  }
}
```

### Error Responses

#### 404 Not Found - MerchantBranch không tồn tại
```json
{
  "code": "404",
  "message": "MerchantBranchId must exist in MerchantBranchInvoiceAccount"
}
```

#### 404 Not Found - Không có active order
```json
{
  "code": "404", 
  "message": "No active invoice order found for this merchant branch"
}
```

#### 400 Bad Request - Validation lỗi
```json
{
  "code": "400",
  "message": "Validation error message"
}
```

#### 500 Internal Server Error
```json
{
  "code": "500",
  "message": "An error occurred while increasing invoice quantity"
}
```

## Business Logic

1. **Validation MerchantBranchId**: Kiểm tra MerchantBranchId có tồn tại trong `MerchantBranchInvoiceAccount`
2. **Tìm Active Order**: Tìm MerchantInvoiceOrder đang active (IsActive = true và EffectiveDateTo >= hiện tại)
3. **Cập nhật Số lượng**: 
   - `TotalInvoiceQuantity += quantity`
   - `RemainingInvoiceQuantity += quantity`
   - `UpdatedAt = DateTime.UtcNow`
4. **Lưu thay đổi**: Cập nhật database
5. **Trả về kết quả**: Response với thông tin đã cập nhật

## Ví dụ sử dụng

### Request
```bash
curl -X POST "https://api.example.com/zenInvoice/api/MerchantInvoiceOrder/increase-quantity" \
  -H "Authorization: Bearer your-token-here" \
  -H "Content-Type: application/json" \
  -d '{
    "merchantBranchId": "123e4567-e89b-12d3-a456-************",
    "quantity": 100,
    "description": "Tăng quota cho tháng 12"
  }'
```

### Response
```json
{
  "code": "000",
  "message": "Success",
  "data": {
    "merchantBranchId": "123e4567-e89b-12d3-a456-************",
    "quantityAdded": 100,
    "newRemainingQuantity": 250,
    "newTotalQuantity": 350,
    "updatedOrderId": "456e7890-e89b-12d3-a456-426614174001",
    "updatedAt": "2024-08-01T10:30:00Z",
    "description": "Tăng quota cho tháng 12"
  }
}
```

## Lưu ý
- Chỉ có thể tăng số lượng cho MerchantInvoiceOrder đang active
- Nếu có nhiều active orders, sẽ chọn order được tạo gần nhất (OrderByDescending CreatedAt)
- Endpoint này không tạo order mới, chỉ cập nhật order hiện có
- Để tạo order mới, sử dụng endpoint `POST /zenInvoice/api/MerchantInvoiceOrder`
