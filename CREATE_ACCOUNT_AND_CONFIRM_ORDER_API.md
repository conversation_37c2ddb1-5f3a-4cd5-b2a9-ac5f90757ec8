# API Tạo Account và Confirm MerchantInvoiceOrder

## <PERSON><PERSON> tả
API này cho phép tạo MerchantBranchInvoiceAccount mới và confirm MerchantInvoiceOrder trong một transaction duy nhất. API sẽ:
1. Tạo MerchantBranchInvoiceAccount mới
2. G<PERSON> MerchantBranchId vừa tạo vào MerchantInvoiceOrder (từ null)
3. Chuyển status của order từ `PENDING` sang `COMPLETED`

## Endpoint
```
POST /zenInvoice/api/MerchantInvoiceOrder/{orderId}/create-account-and-confirm
```

## Authorization
- **Policy**: `SimpleBearerAuth`
- **Header**: `Authorization: Bearer <token>`

## Path Parameters
- `orderId` (Guid, required): ID của MerchantInvoiceOrder cần xử lý

## Request Body
```json
{
  "taxNumber": "string (required, max 50 chars)",
  "invoiceAccountUserName": "string (required, max 100 chars)",
  "invoiceAccountPassword": "string (required, min 6, max 500 chars)",
  "effectiveDate": "2024-01-15T00:00:00Z",
  "expirationDate": "2025-01-15T00:00:00Z",
  "merchantBranchName": "string (required, max 200 chars)",
  "isActive": true,
  "confirmNotes": "string (optional, max 500 chars)"
}
```

### Request Fields
- `taxNumber` (string, required): Mã số thuế của chi nhánh merchant
- `invoiceAccountUserName` (string, required): Tên đăng nhập tài khoản hóa đơn
- `invoiceAccountPassword` (string, required): Mật khẩu tài khoản hóa đơn (tối thiểu 6 ký tự)
- `effectiveDate` (DateTime, required): Ngày bắt đầu hiệu lực
- `expirationDate` (DateTime, required): Ngày hết hạn (phải sau effectiveDate)
- `merchantBranchName` (string, required): Tên chi nhánh merchant
- `isActive` (bool, optional): Trạng thái hoạt động (mặc định true)
- `confirmNotes` (string, optional): Ghi chú cho việc confirm order

## Response

### Success Response (200 OK)
```json
{
  "data": {
    "orderId": "guid",
    "accountId": "guid",
    "merchantBranchId": "guid",
    "status": "COMPLETED",
    "processedAt": "2024-01-15T10:30:00Z",
    "confirmNotes": "string",
    "account": {
      "taxNumber": "string",
      "invoiceAccountUserName": "string",
      "merchantBranchName": "string",
      "effectiveDate": "2024-01-15T00:00:00Z",
      "expirationDate": "2025-01-15T00:00:00Z",
      "isActive": true
    }
  },
  "message": "Account created and order confirmed successfully",
  "code": "000"
}
```

### Error Responses

#### 400 Bad Request
```json
{
  "message": "TaxNumber already exists",
  "code": "400"
}
```
hoặc
```json
{
  "message": "MerchantInvoiceOrder is already completed",
  "code": "400"
}
```
hoặc
```json
{
  "message": "Only PENDING orders can be confirmed",
  "code": "400"
}
```

#### 401 Unauthorized
```json
{
  "message": "Unauthorized access",
  "code": "401"
}
```

#### 404 Not Found
```json
{
  "message": "MerchantInvoiceOrder not found",
  "code": "404"
}
```

#### 500 Internal Server Error
```json
{
  "message": "An internal server error occurred while processing the request",
  "code": "500"
}
```

## Business Logic
1. Kiểm tra quyền truy cập của partner hiện tại
2. Tìm MerchantInvoiceOrder theo ID và đảm bảo có thể truy cập
3. Kiểm tra order có thể confirm (status PENDING hoặc null)
4. Kiểm tra TaxNumber chưa tồn tại cho partner hiện tại
5. Tạo MerchantBranchInvoiceAccount mới với MerchantBranchId mới
6. Cập nhật order: gán MerchantBranchId và chuyển status thành COMPLETED
7. Commit transaction nếu thành công, rollback nếu có lỗi

## Validation Rules
- OrderId phải là GUID hợp lệ
- TaxNumber không được trùng với account khác của cùng partner
- ExpirationDate phải sau EffectiveDate
- Password tối thiểu 6 ký tự
- Chỉ order có status PENDING (hoặc null) mới có thể được xử lý
- Partner chỉ có thể xử lý order thuộc về mình hoặc order có MerchantBranchId = null

## Transaction Safety
API sử dụng database transaction để đảm bảo:
- Nếu tạo account thành công nhưng confirm order thất bại → rollback toàn bộ
- Nếu có lỗi bất kỳ → rollback toàn bộ
- Chỉ commit khi cả hai thao tác đều thành công

## Example Usage

### Request
```bash
curl -X POST "https://api.example.com/zenInvoice/api/MerchantInvoiceOrder/123e4567-e89b-12d3-a456-************/create-account-and-confirm" \
  -H "Authorization: Bearer your-token-here" \
  -H "Content-Type: application/json" \
  -d '{
    "taxNumber": "**********",
    "invoiceAccountUserName": "merchant_user",
    "invoiceAccountPassword": "secure_password123",
    "effectiveDate": "2024-01-15T00:00:00Z",
    "expirationDate": "2025-01-15T00:00:00Z",
    "merchantBranchName": "Chi nhánh Hà Nội",
    "isActive": true,
    "confirmNotes": "Tạo account và confirm order cho chi nhánh mới"
  }'
```

### Response
```json
{
  "data": {
    "orderId": "123e4567-e89b-12d3-a456-************",
    "accountId": "987fcdeb-51a2-43d7-b890-123456789abc",
    "merchantBranchId": "456789ab-cdef-1234-5678-90abcdef1234",
    "status": "COMPLETED",
    "processedAt": "2024-01-15T10:30:00Z",
    "confirmNotes": "Tạo account và confirm order cho chi nhánh mới",
    "account": {
      "taxNumber": "**********",
      "invoiceAccountUserName": "merchant_user",
      "merchantBranchName": "Chi nhánh Hà Nội",
      "effectiveDate": "2024-01-15T00:00:00Z",
      "expirationDate": "2025-01-15T00:00:00Z",
      "isActive": true
    }
  },
  "message": "Account created and order confirmed successfully",
  "code": "000"
}
```
