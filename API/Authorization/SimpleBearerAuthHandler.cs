using Applications.Interfaces.Services.Authentication;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

namespace API.Authorization;

/// <summary>
/// Authorization handler cho Simple Bearer Token authentication
/// Bypass các yêu cầu B2B authentication phức tạp
/// </summary>
public class SimpleBearerAuthHandler : AuthorizationHandler<SimpleBearerAuthRequirement>
{
    private readonly ITokenService _tokenService;
    private readonly ILogger<SimpleBearerAuthHandler> _logger;

    public SimpleBearerAuthHandler(
        ITokenService tokenService,
        ILogger<SimpleBearerAuthHandler> logger)
    {
        _tokenService = tokenService;
        _logger = logger;
    }

    protected override async Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        SimpleBearerAuthRequirement requirement)
    {
        try
        {
            var httpContext = context.Resource as HttpContext;
            if (httpContext == null)
            {
                _logger.LogWarning("HttpContext not available in authorization context");
                context.Fail();
                return;
            }

            _logger.LogDebug("Processing Simple Bearer Auth for {Path}", httpContext.Request.Path);

            // Lấy Authorization header
            var authHeader = httpContext.Request.Headers["Authorization"].FirstOrDefault();
            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                if (!requirement.RequireToken)
                {
                    // Token không bắt buộc, cho phép tiếp tục
                    _logger.LogDebug("Token not required, allowing access to {Path}", httpContext.Request.Path);
                    context.Succeed(requirement);
                    return;
                }

                _logger.LogWarning("Missing or invalid Authorization header for {Path}", httpContext.Request.Path);
                context.Fail();
                return;
            }

            var token = authHeader.Substring("Bearer ".Length).Trim();
            _logger.LogDebug("Token extracted for {Path}, length: {Length}", httpContext.Request.Path, token.Length);

            // Validate token
            var validationResult = await _tokenService.ValidateTokenAsync(token);
            if (!validationResult.IsValid)
            {
                _logger.LogWarning("Token validation failed for {Path}: {ErrorMessage}", 
                    httpContext.Request.Path, validationResult.ErrorMessage);
                context.Fail();
                return;
            }

            // Kiểm tra token expiration nếu được yêu cầu
            if (requirement.ValidateExpiration && validationResult.ExpiresAt.HasValue && 
                validationResult.ExpiresAt.Value <= DateTime.UtcNow)
            {
                _logger.LogWarning("Token expired for {Path}", httpContext.Request.Path);
                context.Fail();
                return;
            }

            // Extract claims và set user context
            var claims = _tokenService.ExtractClaims(token);
            if (claims == null)
            {
                _logger.LogWarning("Unable to extract token claims for {Path}", httpContext.Request.Path);
                context.Fail();
                return;
            }

            var partnerId = _tokenService.GetPartnerIdFromClaims(claims);
            if (partnerId == null)
            {
                _logger.LogWarning("Invalid partner ID in token for {Path}", httpContext.Request.Path);
                context.Fail();
                return;
            }

            // Set user context
            httpContext.User = claims;
            httpContext.Items["PartnerId"] = partnerId.Value;
            httpContext.Items["PartnerName"] = validationResult.PartnerName;
            httpContext.Items["TokenExpires"] = validationResult.ExpiresAt;
            httpContext.Items["IsSimpleBearerAuth"] = true;

            _logger.LogDebug("Simple Bearer Auth successful for partner {PartnerId} on {Path}", 
                partnerId, httpContext.Request.Path);

            context.Succeed(requirement);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during Simple Bearer Auth authorization");
            context.Fail();
        }
    }
}

/// <summary>
/// Authorization requirement cho Simple Bearer Token authentication
/// </summary>
public class SimpleBearerAuthRequirement : IAuthorizationRequirement
{
    /// <summary>
    /// Có yêu cầu token không (mặc định là true)
    /// </summary>
    public bool RequireToken { get; set; } = true;

    /// <summary>
    /// Có validate token expiration không (mặc định là true)
    /// </summary>
    public bool ValidateExpiration { get; set; } = true;

    /// <summary>
    /// Custom error message khi authentication thất bại
    /// </summary>
    public string? CustomErrorMessage { get; set; }

    public SimpleBearerAuthRequirement(bool requireToken = true, bool validateExpiration = true)
    {
        RequireToken = requireToken;
        ValidateExpiration = validateExpiration;
    }
}
