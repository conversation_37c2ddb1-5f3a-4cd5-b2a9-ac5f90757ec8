using Applications.DTOs.MerchantInvoiceOrder;
using Applications.DTOs.MerchantInvoiceOrder.GetInvoiceStatistics;
using Applications.Features.MerchantInvoiceOrder.Commands;
using Applications.Features.MerchantInvoiceOrder.Queries;
using BuildingBlocks.Abstractions;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Shared.Constants;

namespace API.Controllers;

/// <summary>
/// Controller cho quản lý MerchantInvoiceOrder
/// Uses Simple Bearer Token authentication - bypasses B2B authentication complexity
/// </summary>
[Authorize(Policy = "SimpleBearerAuth")]
public class MerchantInvoiceOrderController(IMediator mediator, ILogger<MerchantInvoiceOrderController> logger) : BaseApiController
{
    /// <summary>
    /// API lấy số lượng hóa đơn đã sử dụng và số lượng hóa đơn có sẵn của một MerchantBranchId
    /// </summary>
    /// <param name="merchantBranchId">ID của MerchantBranch</param>
    /// <returns>Thống kê số lượng hóa đơn</returns>
    [HttpGet("statistics/{merchantBranchId:guid}")]
    public async Task<IActionResult> GetInvoiceStatisticsAsync([FromRoute] Guid merchantBranchId)
    {
        try
        {
            logger.LogInformation("Getting invoice statistics for MerchantBranchId: {MerchantBranchId}", merchantBranchId);

            var request = new GetInvoiceStatisticsRequest { MerchantBranchId = merchantBranchId };
            var query = new GetInvoiceStatisticsQuery(request);
            var result = await mediator.Send(query);

            logger.LogInformation("Successfully retrieved invoice statistics for MerchantBranchId: {MerchantBranchId}", merchantBranchId);
            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while retrieving invoice statistics for MerchantBranchId: {MerchantBranchId}", merchantBranchId);
            return StatusCode(500, new Response<object>
            {
                Message = "An internal server error occurred while retrieving invoice statistics",
                Code = ErrorCodes.INTERNAL_SERVER_ERROR
            });
        }
    }

    /// <summary>
    /// Get all merchant invoice orders
    /// </summary>
    /// <returns>List of merchant invoice orders</returns>
    [HttpGet]
    public async Task<IActionResult> GetAllMerchantInvoiceOrders()
    {
        try
        {
            logger.LogInformation("Retrieving all merchant invoice orders");

            var query = new GetAllMerchantInvoiceOrdersQuery();
            var result = await mediator.Send(query);

            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while retrieving MerchantInvoiceOrders");
            return StatusCode(500, new Response<object>
            {
                Message = "An internal server error occurred while retrieving MerchantInvoiceOrders",
                Code = ErrorCodes.INTERNAL_SERVER_ERROR
            });
        }
    }

    /// <summary>
    /// Create a new merchant invoice order
    /// </summary>
    /// <param name="request">Merchant invoice order creation request</param>
    /// <returns>Created merchant invoice order ID</returns>
    [HttpPost]
    public async Task<IActionResult> CreateMerchantInvoiceOrder([FromBody] MerchantInvoiceOrderDto request)
    {
        try
        {
            logger.LogInformation("Creating merchant invoice order for MerchantBranchId: {MerchantBranchId}", request.MerchantBranchId);

            if (request == null)
            {
                logger.LogWarning("CreateMerchantInvoiceOrder called with null request");
                return BadRequest(new Response<object>
                {
                    Message = "Request cannot be null",
                    Code = ErrorCodes.BAD_REQUEST_ERROR
                });
            }

            var command = new CreateMerchantInvoiceOrderCommand(request);
            var result = await mediator.Send(command);

            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while creating MerchantInvoiceOrder");
            return StatusCode(500, new Response<object>
            {
                Message = "An internal server error occurred while creating MerchantInvoiceOrder",
                Code = ErrorCodes.INTERNAL_SERVER_ERROR
            });
        }
    }

    /// <summary>
    /// Get a merchant invoice order by ID
    /// </summary>
    /// <param name="id">The ID of the merchant invoice order</param>
    /// <returns>Merchant invoice order details</returns>
    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetMerchantInvoiceOrderById(Guid id)
    {
        try
        {
            logger.LogInformation("Retrieving merchant invoice order with Id: {Id}", id);

            var query = new GetMerchantInvoiceOrderByIdQuery(id);
            var result = await mediator.Send(query);

            if (result.IsSuccess)
            {
                logger.LogInformation("Successfully retrieved merchant invoice order with Id: {Id}", id);
                return Ok(result);
            }

            if (result.Code == ErrorCodes.NOT_FOUND_DATA)
            {
                logger.LogWarning("Merchant invoice order with Id {Id} not found", id);
                return NotFound(result);
            }

            logger.LogWarning("Failed to retrieve merchant invoice order with Id {Id}: {Message}", id, result.Message);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while retrieving MerchantInvoiceOrder detail for Id: {Id}", id);
            return StatusCode(500, new Response<object>
            {
                Message = "An internal server error occurred while retrieving MerchantInvoiceOrder detail",
                Code = ErrorCodes.INTERNAL_SERVER_ERROR
            });
        }
    }

    /// <summary>
    /// Update an existing merchant invoice order
    /// </summary>
    /// <param name="id">The ID of the merchant invoice order to update</param>
    /// <param name="request">Updated merchant invoice order data</param>
    /// <returns>Updated merchant invoice order</returns>
    [HttpPut("{id:guid}")]
    public async Task<IActionResult> UpdateMerchantInvoiceOrder(Guid id, [FromBody] MerchantInvoiceOrderDto request)
    {
        try
        {
            logger.LogInformation("Updating merchant invoice order with Id: {Id}", id);

            if (request == null)
            {
                logger.LogWarning("UpdateMerchantInvoiceOrder called with null request");
                return BadRequest(new Response<object>
                {
                    Message = "Request cannot be null",
                    Code = ErrorCodes.BAD_REQUEST_ERROR
                });
            }

            var command = new UpdateMerchantInvoiceOrderCommand(id, request);
            var result = await mediator.Send(command);

            return Ok(result);

            // if (result.IsSuccess)
            // {
            //     logger.LogInformation("Successfully updated merchant invoice order with Id: {Id}", id);
            //     return Ok(result);
            // }

            // if (result.Code == ErrorCodes.NOT_FOUND_DATA)
            // {
            //     logger.LogWarning("Merchant invoice order with Id {Id} not found for update", id);
            //     return NotFound(result);
            // }

            // logger.LogWarning("Failed to update merchant invoice order with Id {Id}: {Message}", id, result.Message);
            // return BadRequest(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating merchant invoice order with Id: {Id}", id);
            return StatusCode(500, new Response<object>
            {
                Message = "Internal server error",
                Code = ErrorCodes.INTERNAL_SERVER_ERROR
            });
        }
    }

    /// <summary>
    /// API xóa một MerchantInvoiceOrder by Id
    /// </summary>
    /// <param name="id">ID của MerchantInvoiceOrder cần xóa</param>
    /// <returns>Kết quả xóa</returns>
    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> DeleteMerchantInvoiceOrderAsync([FromRoute] Guid id)
    {
        try
        {
            logger.LogInformation("Deleting MerchantInvoiceOrder with Id: {Id}", id);

            var command = new DeleteMerchantInvoiceOrderCommand(id);
            var result = await mediator.Send(command);

            if (result.Code == "000" && result.Data == true)
            {
                logger.LogInformation("Successfully deleted MerchantInvoiceOrder with Id: {Id}", id);
                return Ok(result);
            }

            return BadRequest(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while deleting MerchantInvoiceOrder with Id: {Id}", id);
            return StatusCode(500, new Response<object>
            {
                Message = "An internal server error occurred while deleting MerchantInvoiceOrder",
                Code = ErrorCodes.INTERNAL_SERVER_ERROR
            });
        }
    }

    /// <summary>
    /// API tăng số lượng hóa đơn khả dụng cho một chi nhánh merchant
    /// </summary>
    /// <param name="request">Thông tin tăng số lượng hóa đơn</param>
    /// <returns>Kết quả tăng số lượng hóa đơn</returns>
    [HttpPost("increase-quantity")]
    public async Task<IActionResult> IncreaseInvoiceQuantityAsync([FromBody] IncreaseInvoiceQuantityCommand request)
    {
        try
        {
            // logger.LogInformation("Increasing invoice quantity for MerchantBranchId: {MerchantBranchId}, Quantity: {Quantity}",
            //     request.MerchantBranchId, request.Quantity);

            // var command = new IncreaseInvoiceQuantityCommand(request);
            var result = await mediator.Send(request);
            return Ok(result);
            // if (result.Code == "000" && result.Data != null)
            // {
            //     logger.LogInformation("Successfully increased invoice quantity for MerchantBranchId: {MerchantBranchId}. " +
            //                          "Added: {Added}, New Total: {NewTotal}, New Remaining: {NewRemaining}",
            //         request.MerchantBranchId, result.Data.QuantityAdded, result.Data.NewTotalQuantity, result.Data.NewRemainingQuantity);
            //     return Ok(result);
            // }

            // if (result.Code == "404")
            // {
            //     return NotFound(result);
            // }

            // return BadRequest(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while increasing invoice quantity");
            return StatusCode(500, new Response<object>
            {
                Message = "An internal server error occurred while increasing invoice quantity",
                Code = ErrorCodes.INTERNAL_SERVER_ERROR
            });
        }
    }

    // TODO: Uncomment when DeactivateMerchantInvoiceOrderCommand is properly recognized
    // /// <summary>
    // /// Deactivate một MerchantInvoiceOrder (set IsActive = false)
    // /// </summary>
    // /// <param name="id">ID của MerchantInvoiceOrder cần deactivate</param>
    // /// <returns>Kết quả deactivate</returns>
    // [HttpPatch("{id:guid}/deactivate")]
    // [Microsoft.AspNetCore.Authorization.AllowAnonymous]
    // public async Task<IActionResult> DeactivateMerchantInvoiceOrderAsync(Guid id)
    // {
    //     try
    //     {
    //         logger.LogInformation("Deactivating MerchantInvoiceOrder with Id: {Id}", id);

    //         var command = new Applications.Features.MerchantInvoiceOrder.Commands.DeactivateMerchantInvoiceOrderCommand(id);
    //         var result = await mediator.Send(command);

    //         if (result.Code == "000" && result.Data == true)
    //         {
    //             logger.LogInformation("Successfully deactivated MerchantInvoiceOrder with Id: {Id}", id);
    //             return Ok(result);
    //         }

    //         if (result.Code == "404")
    //         {
    //             return NotFound(result);
    //         }

    //         return BadRequest(result);
    //     }
    //     catch (Exception ex)
    //     {
    //         logger.LogError(ex, "Error occurred while deactivating MerchantInvoiceOrder with Id: {Id}", id);
    //         return StatusCode(500, new Response<object>
    //         {
    //             Message = "An internal server error occurred while deactivating MerchantInvoiceOrder",
    //             Code = ErrorCodes.INTERNAL_SERVER_ERROR
    //         });
    //     }
    // }
}
