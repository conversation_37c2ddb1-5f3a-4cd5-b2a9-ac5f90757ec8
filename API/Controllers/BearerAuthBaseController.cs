using Applications.Interfaces.Services;
using Core.Entities.Authentication;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers;

/// <summary>
/// Base controller for endpoints that use simple Bearer Token Authentication
/// Provides easy access to current user information
/// </summary>
[ApiController]
[Route("zenInvoice/api/[controller]")]
public abstract class BearerAuthBaseController : ControllerBase
{
    protected readonly ICurrentUserService _currentUserService;

    protected BearerAuthBaseController(ICurrentUserService currentUserService)
    {
        _currentUserService = currentUserService;
    }

    /// <summary>
    /// Get current authenticated partner ID
    /// </summary>
    protected Guid? CurrentPartnerId => _currentUserService.GetCurrentPartnerId();

    /// <summary>
    /// Get current authenticated partner name
    /// </summary>
    protected string? CurrentPartnerName => _currentUserService.GetCurrentPartnerName();

    /// <summary>
    /// Check if current request is authenticated
    /// </summary>
    protected bool IsAuthenticated => _currentUserService.IsAuthenticated();

    /// <summary>
    /// Get current partner information from database
    /// </summary>
    /// <returns>Partner entity if authenticated and found</returns>
    protected async Task<Partner?> GetCurrentPartnerAsync()
    {
        return await _currentUserService.GetCurrentPartnerAsync();
    }

    /// <summary>
    /// Get current partner information with related data
    /// </summary>
    /// <returns>Partner entity with navigation properties loaded</returns>
    protected async Task<Partner?> GetCurrentPartnerWithDetailsAsync()
    {
        return await _currentUserService.GetCurrentPartnerWithDetailsAsync();
    }

    /// <summary>
    /// Get token expiration time for current user
    /// </summary>
    protected DateTime? TokenExpiration => _currentUserService.GetTokenExpiration();

    /// <summary>
    /// Check if current authentication is simple Bearer token
    /// </summary>
    protected bool IsSimpleBearerAuth => _currentUserService.IsSimpleBearerAuth();

    /// <summary>
    /// Get current user's scopes/permissions
    /// </summary>
    protected string[] CurrentUserScopes => _currentUserService.GetCurrentUserScopes();

    /// <summary>
    /// Ensure user is authenticated, throw UnauthorizedAccessException if not
    /// </summary>
    protected void EnsureAuthenticated()
    {
        if (!IsAuthenticated)
        {
            throw new UnauthorizedAccessException("User is not authenticated");
        }
    }

    /// <summary>
    /// Get current partner or throw exception if not found
    /// </summary>
    /// <returns>Current partner entity</returns>
    /// <exception cref="UnauthorizedAccessException">When user is not authenticated</exception>
    /// <exception cref="InvalidOperationException">When partner is not found</exception>
    protected async Task<Partner> GetCurrentPartnerOrThrowAsync()
    {
        EnsureAuthenticated();

        var partner = await GetCurrentPartnerAsync();
        if (partner == null)
        {
            throw new InvalidOperationException("Current partner not found");
        }

        return partner;
    }
}