using API.Attributes;
using Applications.DTOs.MobiFoneInvoice.GetDataReferences;
using Applications.DTOs.MobiFoneInvoice.GetListCertificatesFile68;
using Applications.DTOs.MobiFoneInvoice.Login;
using Applications.DTOs.MobiFoneInvoice.CreateInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateSalesInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreatePublicAssetSalesInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateNationalReserveSalesInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateOtherInvoice.Raws;
using Applications.Features.MobiFoneInvoice.Commands;
using Applications.DTOs.MobiFoneInvoice.CreatePXKVCNBInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreatePXKDLInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateInvoiceMTT.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateSalesInvoiceMTT.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateOtherInvoiceMTT.Raws;
using Applications.DTOs.MobiFoneInvoice.SaveAndSignHoadon78.Raws;
using Applications.DTOs.MobiFoneInvoice.SignInvoiceCertFile68.Raws;
using Applications.DTOs.MobiFoneInvoice.SendInvoiceToCQT68.Raws;
using Applications.DTOs.MobiFoneInvoice.SignAndSendInvoiceToCQT68.Raws;
using Applications.DTOs.MobiFoneInvoice.SendInvoiceByEmail.Raws;
using Applications.DTOs.MobiFoneInvoice.DownloadInvoicePDF.Raws;
using Applications.DTOs.MobiFoneInvoice.PrintMultipleInvoices.Raws;
using Applications.DTOs.MobiFoneInvoice.DeleteUnsignedInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CancelInvoiceWithoutCode.Raws;
using Applications.DTOs.MobiFoneInvoice.GetInvoiceById.Raws;
using Applications.DTOs.MobiFoneInvoice.GetHoadonFkey.Raws;
using Applications.DTOs.MobiFoneInvoice.GetInvoiceByTimeAndUnit.Raws;
using Applications.DTOs.MobiFoneInvoice.GetInvoiceFromdateTodate.Raws;
using Applications.Features.MobiFoneInvoice.Queries;
using Applications.DTOs.MobiFoneInvoice.GetHistoryInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.ExportXMLHoadon.Raws;
using Applications.DTOs.MobiFoneInvoice.ExportInvoiceXmlPretreatment.Raws;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers;

/// <summary>
/// Controller cho tích hợp MobiFone Invoice API
/// </summary>
// [Microsoft.AspNetCore.Authorization.AllowAnonymous] // TẮT HOÀN TOÀN authentication/authorization cho tất cả MobiFone APIs
public class MobiFoneInvoiceController(IMediator mediator) : BaseApiController
{

    /// <summary>
    /// Đăng nhập vào hệ thống MobiFone Invoice
    /// </summary>
    /// <param name="request">Thông tin đăng nhập</param>
    /// <returns>Token và thông tin user</returns>
    [HttpPost("login")]
    [RequirePermission("MOBIFONE_API", "EXECUTE")]
    public async Task<IActionResult> LoginAsync([FromBody] LoginRequest request)
    {
        var command = new LoginCommand(request);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Lấy thông tin dải ký hiệu mẫu số hóa đơn - Chỉ sử dụng Auto Authentication
    ///
    /// Hệ thống tự động lấy token và maDvcs từ cache dựa trên taxCode
    /// </summary>
    /// <param name="request">Thông tin request</param>
    /// <param name="taxCode">Mã số thuế để tự động authentication</param>
    /// <returns>Danh sách thông tin dải ký hiệu</returns>
    [HttpGet("data-references")]
    [RequirePermission("MOBIFONE_API", "READ")]
    [ValidateTaxCode]
    public async Task<IActionResult> GetDataReferencesAsync(
        [FromQuery] GetDataReferencesRequest request,
        [FromQuery] string taxCode)
    {
        var query = new GetDataReferencesQuery(request, taxCode);
        var result = await mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Lấy thông tin CKS (chứng thư số) - Chỉ sử dụng Auto Authentication
    ///
    /// Hệ thống tự động lấy token và maDvcs từ cache dựa trên taxCode
    /// </summary>
    /// <param name="taxCode">Mã số thuế để tự động authentication</param>
    /// <returns>Danh sách thông tin CKS</returns>
    [HttpGet("certificates")]
    [ValidateTaxCode]
    public async Task<IActionResult> GetListCertificatesFile68Async(
        [FromQuery] string taxCode)
    {
        var query = new GetListCertificatesFile68Query(taxCode);
        var result = await mediator.Send(query);
        return Ok(result);
    }

    #region Tạo mới hóa đơn quy trình thường (tất cả hình thức HĐ)

    /// <summary>
    /// a. Hóa đơn Giá trị gia tăng - Chỉ sử dụng Auto Authentication
    ///
    /// Hệ thống tự động lấy token và maDvcs từ cache dựa trên taxCode
    /// </summary>
    /// <param name="request">Thông tin hóa đơn cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="taxCode">Mã số thuế để tự động authentication</param>
    /// <returns>Thông tin hóa đơn đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("tao-moi-hoa-don-gtgt-quy-trinh-thuong")]
    [RequirePermission("INVOICE_CREATE", "CREATE")]
    [ValidateTaxCode]
    public async Task<IActionResult> TaoMoiHoaDonGTGTQuyTrinhThuongAsync(
        [FromBody] SaveListHoadon78Request request,
        [FromQuery] string taxCode)
    {
        // Sử dụng CreateInvoiceCommand với auto authentication
        var command = new CreateInvoiceCommand(request, taxCode);
        var result = await mediator.Send(command);
        return Ok(result);
    }


    /// <summary>
    /// b. Tạo mới Hóa đơn Bán hàng - Chỉ sử dụng Auto Authentication
    ///
    /// Hệ thống tự động lấy token và maDvcs từ cache dựa trên taxCode
    /// </summary>
    /// <param name="request">Thông tin hóa đơn bán hàng cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="taxCode">Mã số thuế để tự động authentication</param>
    /// <returns>Thông tin hóa đơn bán hàng đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("tao-moi-hoa-don-ban-hang-quy-trinh-thuong")]
    [RequirePermission("INVOICE_CREATE", "CREATE")]
    [ValidateTaxCode]
    public async Task<IActionResult> TaoMoiHoaDonBanHangQuyTrinhThuongAsync(
        [FromBody] SaveListHoadonBanHangRequest request,
        [FromQuery] string taxCode)
    {
        var command = new CreateSalesInvoiceAutoCommand(request, taxCode);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// c. Tạo mới Hóa đơn Bán tài sản công
    /// </summary>
    /// <param name="request">Thông tin hóa đơn bán tài sản công cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin hóa đơn bán tài sản công đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("tao-moi-hoa-don-ban-tai-san-cong-quy-trinh-thuong")]
    public async Task<IActionResult> TaoMoiHoaDonBanTaiSanCongQuyTrinhThuongAsync(
        [FromBody] SaveListHoadonBanTaiSanCongRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new TaoMoiHoaDonBanTaiSanCongQuyTrinhThuongCommand(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// d. Tạo mới Hóa đơn Bán hàng dự trữ quốc gia
    /// </summary>
    /// <param name="request">Thông tin hóa đơn bán hàng dự trữ quốc gia cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin hóa đơn bán hàng dự trữ quốc gia đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("tao-moi-hoa-don-ban-hang-du-tru-quoc-gia-quy-trinh-thuong")]
    public async Task<IActionResult> TaoMoiHoaDonBanHangDuTruQuocGiaQuyTrinhThuongAsync(
        [FromBody] SaveListHoadonBanHangDuTruQuocGiaRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new TaoMoiHoaDonBanHangDuTruQuocGiaQuyTrinhThuongCommand(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// e. Tạo mới Hóa đơn khác (Tem, vé, thẻ, phiếu...)
    /// </summary>
    /// <param name="request">Thông tin hóa đơn khác cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin hóa đơn khác đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("tao-moi-hoa-don-khac-quy-trinh-thuong")]
    public async Task<IActionResult> TaoMoiHoaDonKhacQuyTrinhThuongAsync(
        [FromBody] SaveListHoaDonKhacRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new TaoMoiHoaDonKhacQuyTrinhThuongCommand(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// g. Tạo mới Phiếu xuất kho kiêm vận chuyển nội bộ (PXKVCNB)
    /// </summary>
    /// <param name="request">Thông tin phiếu xuất kho kiêm vận chuyển nội bộ cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin phiếu xuất kho kiêm vận chuyển nội bộ đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("tao-moi-phieu-xuat-kho-kiem-van-chuyen-noi-bo-quy-trinh-thuong")]
    public async Task<IActionResult> TaoMoiPhieuXuatKhoKiemVanChuyenNoiBoQuyTrinhThuongAsync(
        [FromBody] SaveListHoadonPXKVCNBRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new TaoMoiHoaDonPXKVCNBQuyTrinhThuongCommand(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// h. Tạo mới Phiếu xuất kho hàng gửi bán đại lý (PXKDL)
    /// </summary>
    /// <param name="request">Thông tin phiếu xuất kho hàng gửi bán đại lý cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin phiếu xuất kho hàng gửi bán đại lý đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("tao-moi-phieu-xuat-kho-hang-gui-ban-dai-ly-quy-trinh-thuong")]
    public async Task<IActionResult> TaoMoiPhieuXuatKhoHangGuiBanDaiLyQuyTrinhThuongAsync(
        [FromBody] SaveListHoadonPXKDLRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new TaoMoiHoaDonPXKDLQuyTrinhThuongCommand(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    #endregion

    #region Tạo mới hóa đơn máy tính tiền sinh mã (SaveListHoadon78MTT)

    /// <summary>
    /// a. Hóa đơn Giá trị gia tăng máy tính tiền sinh mã
    /// </summary>
    /// <param name="request">Thông tin hóa đơn cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin hóa đơn đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("tao-moi-hoa-don-gtgt-may-tinh-tien-sinh-ma")]
    public async Task<IActionResult> TaoMoiHoaDonGTGTMayTinhTienSinhMaAsync(
        [FromBody] SaveListHoadon78MTTRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new TaoMoiHoaDonGTGTMayTinhTienSinhMaCommand(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// b. Hóa đơn Bán hàng máy tính tiền sinh mã
    /// </summary>
    /// <param name="request">Thông tin hóa đơn bán hàng cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin hóa đơn bán hàng đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("tao-moi-hoa-don-ban-hang-may-tinh-tien-sinh-ma")]
    public async Task<IActionResult> TaoMoiHoaDonBanHangMayTinhTienSinhMaAsync(
        [FromBody] SaveListHoadonBanHangMTTRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new TaoMoiHoaDonBanHangMayTinhTienSinhMaCommand(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// c. Hóa đơn khác (Tem, vé, thẻ, phiếu...) máy tính tiền sinh mã
    /// </summary>
    /// <param name="request">Thông tin hóa đơn khác cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin hóa đơn khác đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("tao-moi-hoa-don-khac-may-tinh-tien-sinh-ma")]
    public async Task<IActionResult> TaoMoiHoaDonKhacMayTinhTienSinhMaAsync(
        [FromBody] SaveListHoaDonKhacMTTRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new TaoMoiHoaDonKhacMayTinhTienSinhMaCommand(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    #endregion

    #region API 4.4: Save And Sign Hoadon78

    /// <summary>
    /// API 4.4: Hàm tạo mới và ký gửi hóa đơn bằng HSM nhà cung cấp khác, file mềm, Sim PKI - Chỉ sử dụng Auto Authentication
    ///
    /// Hệ thống tự động lấy token và maDvcs từ cache dựa trên taxCode
    /// </summary>
    /// <param name="request">Thông tin hóa đơn cần tạo và ký gửi (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="taxCode">Mã số thuế để tự động authentication</param>
    /// <returns>Thông tin hóa đơn đã tạo và ký gửi (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("save-and-sign-hoadon78")]
    [ValidateTaxCode]
    public async Task<IActionResult> SaveAndSignHoadon78Async(
        [FromBody] SaveAndSignHoadon78Request request,
        [FromQuery] string taxCode)
    {
        var command = new SaveAndSignHoadon78Command(request, taxCode);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    #endregion

    #region API 4.5: Sign Invoice Cert File 68

    /// <summary>
    /// API 4.5: Ký chờ xử lý hóa đơn (bằng file mềm, SIM) - Chỉ sử dụng Auto Authentication
    ///
    /// Hệ thống tự động lấy token và maDvcs từ cache dựa trên taxCode
    /// </summary>
    /// <param name="request">Thông tin hóa đơn cần ký (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="taxCode">Mã số thuế để tự động authentication</param>
    /// <returns>Thông tin kết quả ký hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("sign-invoice-cert-file68")]
    public async Task<IActionResult> SignInvoiceCertFile68Async(
        [FromBody] SignInvoiceCertFile68Request request,
        [FromQuery] string taxCode)
    {
        var command = new SignInvoiceCertFile68Command(request, taxCode);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    #endregion

    #region API 4.6: Send Invoice To CQT 68

    /// <summary>
    /// API 4.6: Gửi hóa đơn đã ký lên Cơ quan thuế - Chỉ sử dụng Auto Authentication
    ///
    /// Hệ thống tự động lấy token và maDvcs từ cache dựa trên taxCode
    /// </summary>
    /// <param name="request">Thông tin hóa đơn cần gửi CQT (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="taxCode">Mã số thuế để tự động authentication</param>
    /// <returns>Thông tin kết quả gửi hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("send-invoice-to-cqt68")]
    public async Task<IActionResult> SendInvoiceToCQT68Async(
        [FromBody] SendInvoiceToCQT68Request request,
        [FromQuery] string taxCode)
    {
        var command = new SendInvoiceToCQT68Command(request, taxCode);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    #endregion

    #region API 4.7: Sign And Send Invoice To CQT 68

    /// <summary>
    /// API 4.7: Ký và gửi hóa đơn tới CQT (Chỉ dành cho file mềm, SIM) - Chỉ sử dụng Auto Authentication
    ///
    /// Hệ thống tự động lấy token và maDvcs từ cache dựa trên taxCode
    /// </summary>
    /// <param name="request">Thông tin hóa đơn cần ký và gửi CQT (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="taxCode">Mã số thuế để tự động authentication</param>
    /// <returns>Thông tin kết quả ký và gửi hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("sign-and-send-invoice-to-cqt68")]
    public async Task<IActionResult> SignAndSendInvoiceToCQT68Async(
        [FromBody] SignAndSendInvoiceToCQT68Request request,
        [FromQuery] string taxCode)
    {
        var command = new SignAndSendInvoiceToCQT68Command(request, taxCode);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    #endregion

    #region API 4.20: Get History Invoice

    /// <summary>
    /// API 4.20: Lấy danh sách lịch sử hóa đơn theo ID - Auto Authentication
    /// URL: {{base_url}}/api/Invoice68/GetHistoryInvoice?id={hdon_id}
    /// Method: GET
    /// Mô tả: Hàm này cho phép lấy toàn bộ lịch sử truyền nhận dữ liệu của hóa đơn với Tổng cục thuế
    /// Hệ thống tự động lấy token và maDvcs từ cache dựa trên taxCode
    /// </summary>
    /// <param name="id">ID của hóa đơn cần lấy lịch sử</param>
    /// <param name="taxCode">Mã số thuế để tự động authentication</param>
    /// <returns>Danh sách lịch sử hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpGet("get-history-invoice")]
    public async Task<IActionResult> GetHistoryInvoiceAsync(
        [FromQuery] string id,
        [FromQuery] string taxCode)
    {
        var query = new GetHistoryInvoiceAutoQuery(id, taxCode);
        var result = await mediator.Send(query);
        return Ok(result);
    }

    #endregion

    #region API 4.8: Send Invoice By Email

    /// <summary>
    /// API 4.8: Gửi mail phát hành hóa đơn cho người mua - Auto Authentication
    /// URL: {{base_url}}/api/Invoice68/AutoSendInvoiceByEmail
    /// Method: POST
    /// Mô tả: API này cho phép người dùng gửi email thông báo xuất hóa đơn cho người mua
    /// Hệ thống tự động lấy token và maDvcs từ cache dựa trên taxCode
    /// </summary>
    /// <param name="request">Thông tin gửi email hóa đơn (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="taxCode">Mã số thuế để tự động authentication</param>
    /// <returns>Kết quả gửi email hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("send-invoice-by-email")]
    public async Task<IActionResult> SendInvoiceByEmailAsync(
        [FromBody] SendInvoiceByEmailRequest request,
        [FromQuery] string taxCode)
    {
        var command = new SendInvoiceByEmailAutoCommand(request, taxCode);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    #endregion

    #region API 4.9: Download Invoice PDF

    /// <summary>
    /// API 4.9: Tải hóa đơn File .PDF - Auto Authentication
    /// URL: {{base_url}}/api/Invoice68/inHoadon?id={hdon_id}&type=PDF&inchuyendoi=false
    /// Method: GET
    /// Mô tả: Hàm này cho phép người dùng tải file hóa đơn dạng PDF về máy
    /// Hệ thống tự động lấy token và maDvcs từ cache dựa trên taxCode
    /// </summary>
    /// <param name="id">ID của hóa đơn cần tải về máy</param>
    /// <param name="taxCode">Mã số thuế để tự động authentication</param>
    /// <returns>File PDF dưới dạng byte array</returns>
    [HttpGet("download-invoice-pdf")]
    public async Task<IActionResult> DownloadInvoicePDFAsync(
        [FromQuery] string id,
        [FromQuery] string taxCode)
    {
        var query = new DownloadInvoicePDFAutoQuery(id, taxCode);
        var result = await mediator.Send(query);

        if (result.Code == "000" && result.Data != null)
        {
            return File(result.Data, "application/pdf", $"invoice_{id}.pdf");
        }

        return Ok(result);
    }

    #endregion

    #region API 4.10: Print Multiple Invoices

    /// <summary>
    /// API 4.10: In nhiều hóa đơn - Auto Authentication
    /// URL: {{base_url}}/api/Invoice68/InDanhSachHoaDon
    /// Method: POST
    /// Mô tả: API này cho phép người dùng in nhiều hóa đơn dưới định dạng PDF
    /// Hệ thống tự động lấy token và maDvcs từ cache dựa trên taxCode
    /// </summary>
    /// <param name="request">Thông tin in nhiều hóa đơn (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="taxCode">Mã số thuế để tự động authentication</param>
    /// <returns>File PDF chứa nhiều hóa đơn dưới dạng byte array</returns>
    [HttpPost("print-multiple-invoices")]
    public async Task<IActionResult> PrintMultipleInvoicesAsync(
        [FromBody] PrintMultipleInvoicesRequest request,
        [FromQuery] string taxCode)
    {
        var command = new PrintMultipleInvoicesAutoCommand(request, taxCode);
        var result = await mediator.Send(command);

        if (result.Code == "000" && result.Data != null)
        {
            return File(result.Data, "application/pdf", "multiple_invoices.pdf");
        }

        return Ok(result);
    }

    #endregion

    #region API 4.11: Delete Unsigned Invoice

    /// <summary>
    /// API 4.11: Xóa hóa đơn chưa ký gửi - Auto Authentication
    /// URL: {{base_url}}/api/Invoice68/hoadonXoaNhieu
    /// Method: POST
    /// Mô tả: API này cho phép người dùng xóa hóa đơn chưa ký gửi
    /// Hệ thống tự động lấy token và maDvcs từ cache dựa trên taxCode
    /// </summary>
    /// <param name="request">Thông tin xóa hóa đơn (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="taxCode">Mã số thuế để tự động authentication</param>
    /// <returns>Kết quả xóa hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("delete-unsigned-invoice")]
    public async Task<IActionResult> DeleteUnsignedInvoiceAsync(
        [FromBody] DeleteUnsignedInvoiceRequest request,
        [FromQuery] string taxCode)
    {
        var command = new DeleteUnsignedInvoiceAutoCommand(request, taxCode);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    #endregion

    #region API 4.12: Cancel Invoice Without Code

    /// <summary>
    /// API 4.12: Hủy hóa đơn không mã - Auto Authentication
    /// URL: {{base_url}}/api/Invoice68/uploadCanceledInv?id={}
    /// Method: GET
    /// Mô tả: API này cho phép người dùng hủy các hóa đơn không mã đã lập nhưng chưa được gửi lên Cơ quan Thuế (CQT)
    /// Hệ thống tự động lấy token và maDvcs từ cache dựa trên taxCode
    /// </summary>
    /// <param name="id">ID của hóa đơn cần hủy</param>
    /// <param name="taxCode">Mã số thuế để tự động authentication</param>
    /// <returns>Kết quả hủy hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpGet("cancel-invoice-without-code")]
    public async Task<IActionResult> CancelInvoiceWithoutCodeAsync(
        [FromQuery] string id,
        [FromQuery] string taxCode)
    {
        var request = new CancelInvoiceWithoutCodeRequest { id = id };
        var command = new CancelInvoiceWithoutCodeAutoCommand(request, taxCode);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    #endregion

    #region API 4.13: Get Invoice By ID

    /// <summary>
    /// API 4.13: Lấy thông tin hóa đơn theo ID - Chỉ sử dụng Auto Authentication
    ///
    /// Hệ thống tự động lấy token và maDvcs từ cache dựa trên taxCode
    /// </summary>
    /// <param name="id">ID của hóa đơn cần lấy thông tin</param>
    /// <param name="taxCode">Mã số thuế để tự động authentication</param>
    /// <returns>Thông tin chi tiết hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpGet("get-invoice-by-id")]
    [ValidateTaxCode]
    public async Task<IActionResult> GetInvoiceByIdAsync(
        [FromQuery] string id,
        [FromQuery] string taxCode)
    {
        var request = new GetInvoiceByIdRequest { id = id };
        var query = new GetInvoiceByIdQuery(request, taxCode);
        var result = await mediator.Send(query);
        return Ok(result);
    }

    #endregion

    #region API 4.14: Get Hoadon Fkey

    /// <summary>
    /// API 4.14: Lấy danh sách hóa đơn theo FKEY hoặc Khoảng thời gian - Auto Authentication
    /// URL: {{base_url}}/api/Invoice68/GetHoadonFkey
    /// Method: POST
    /// Mô tả: API này cho phép người dùng lấy toàn bộ danh sách thông tin của hóa đơn theo mã FKEY hoặc theo khoảng thời gian lập hóa đơn
    /// Hệ thống tự động lấy token và maDvcs từ cache dựa trên taxCode
    /// </summary>
    /// <param name="request">Thông tin lấy danh sách hóa đơn (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="taxCode">Mã số thuế để tự động authentication</param>
    /// <returns>Danh sách hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("get-hoadon-fkey")]
    public async Task<IActionResult> GetHoadonFkeyAsync(
        [FromBody] GetHoadonFkeyRequest request,
        [FromQuery] string taxCode)
    {
        var query = new GetHoadonFkeyAutoQuery(request, taxCode);
        var result = await mediator.Send(query);
        return Ok(result);
    }

    #endregion

    #region API 4.17: Get Invoice By Time And Unit

    /// <summary>
    /// API 4.17: Lấy danh sách hoá đơn theo thời gian, đơn vị và trạng thái - Auto Authentication
    /// URL: {{base_url}}/api/Invoice68/GetInvoiceByTimeAndUnit
    /// Method: POST
    /// Mô tả: Hàm này cho phép người dùng lấy danh sách hoá đơn thoả mãn khoảng thời gian cụ thể, đơn vị của người sử dụng và nằm trong các trạng thái cụ thể (Đã cấp mã, Chấp nhận TBSS, CQT đã nhận)
    /// Hệ thống tự động lấy token và maDvcs từ cache dựa trên taxCode
    /// </summary>
    /// <param name="request">Thông tin lấy danh sách hóa đơn theo thời gian và đơn vị (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="taxCode">Mã số thuế để tự động authentication</param>
    /// <returns>Danh sách hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("get-invoice-by-time-and-unit")]
    public async Task<IActionResult> GetInvoiceByTimeAndUnitAsync(
        [FromBody] GetInvoiceByTimeAndUnitRequest request,
        [FromQuery] string taxCode)
    {
        var query = new GetInvoiceByTimeAndUnitAutoQuery(request, taxCode);
        var result = await mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// 4.15. Lấy thông tin XML hóa đơn - Auto Authentication
    /// </summary>
    /// <param name="id">ID của hóa đơn (Guid)</param>
    /// <param name="taxCode">Mã số thuế để tự động authentication</param>
    /// <returns>Thông tin XML hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpGet("export-xml-hoadon")]
    public async Task<IActionResult> ExportXMLHoadonAsync(
        [FromQuery] string id,
        [FromQuery] string taxCode)
    {
        var command = new ExportXMLHoadonAutoCommand(id, taxCode);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// API 4.22: Xuất XML hóa đơn trước khi ký số bằng usb token qua Plugin - Auto Authentication
    /// URL: {{base_url}}/api/Invoice68/ExportInvoiceXmlPretreatment?id={hdon_id}
    /// Method: GET
    /// Mô tả: Hàm này cho phép người dùng xuất XML hóa đơn trước khi ký bằng Plugin
    /// Hệ thống tự động lấy token và maDvcs từ cache dựa trên taxCode
    /// </summary>
    /// <param name="id">ID của hóa đơn (Guid)</param>
    /// <param name="taxCode">Mã số thuế để tự động authentication</param>
    /// <returns>Thông tin XML hóa đơn trước khi ký (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpGet("export-invoice-xml-pretreatment")]
    public async Task<IActionResult> ExportInvoiceXmlPretreatmentAsync(
        [FromQuery] string id,
        [FromQuery] string taxCode)
    {
        var command = new ExportInvoiceXmlPretreatmentAutoCommand(id, taxCode);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// 4.16. Lấy danh sách hóa đơn theo khoảng thời gian - Auto Authentication
    /// </summary>
    /// <param name="request">Thông tin khoảng thời gian tìm kiếm hóa đơn (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="taxCode">Mã số thuế để tự động authentication</param>
    /// <returns>Danh sách hóa đơn (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("get-invoice-from-date-to-date")]
    public async Task<IActionResult> GetInvoiceFromdateTodateAsync(
        [FromBody] GetInvoiceFromdateTodateRequest request,
        [FromQuery] string taxCode)
    {
        var query = new GetInvoiceFromdateTodateAutoQuery(request, taxCode);
        var result = await mediator.Send(query);
        return Ok(result);
    }

    #endregion
}
