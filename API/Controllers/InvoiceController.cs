using API.Attributes;
using Applications.DTOs.InvoiceInfo.GetInvoices;
using Applications.DTOs.InvoiceInfo.GetInvoicesByMerchantBranch;
using Applications.DTOs.InvoiceInfo.UpdateInvoiceStatus;
using Applications.DTOs.InvoiceInfo.CreateInvoice;
using Applications.Features.InvoiceInfo.Queries;
using Applications.Features.InvoiceInfo.Commands;
using BuildingBlocks.Abstractions;
using Core.Enumerables;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Shared.Constants;
using System.ComponentModel.DataAnnotations;

namespace API.Controllers;

/// <summary>
/// Controller cho quản lý thông tin hóa đơn
/// Public endpoints - no authentication required
/// </summary>
[Microsoft.AspNetCore.Authorization.AllowAnonymous]
public class InvoiceController(IMediator mediator, ILogger<InvoiceController> logger) : BaseApiController
{
    /// <summary>
    /// L<PERSON>y danh sách hóa đơn với khả năng lọc theo MerchantInvoiceOrder Id,
    /// trạng thái cqt hóa đơn, trạng thái hóa đơn, từ ngày, đến ngày
    /// </summary>
    /// <param name="request">Thông tin lọc và phân trang</param>
    /// <returns>Danh sách hóa đơn có phân trang với thống kê tổng hợp</returns>
    [HttpGet]
    [Microsoft.AspNetCore.Authorization.AllowAnonymous]
    public async Task<IActionResult> GetInvoicesAsync([FromQuery] GetInvoicesRequest request)
    {
        try
        {
            logger.LogInformation("Getting invoices with filters - " +
                                 "MerchantBranchId: {MerchantBranchId}, MerchantInvoiceOrderId: {MerchantInvoiceOrderId}, " +
                                 "CqtStatus: {CqtStatus}, InvoiceStatus: {InvoiceStatus}, PageIndex: {PageIndex}, PageSize: {PageSize}",
                request.MerchantBranchId, request.MerchantInvoiceOrderId, request.CqtInvoiceStatus,
                request.InvoiceStatus, request.PageIndex, request.PageSize);

            // Validate date range
            if (request.FromDate.HasValue && request.ToDate.HasValue && request.FromDate.Value > request.ToDate.Value)
            {
                logger.LogWarning("Invalid date range: FromDate {FromDate} is greater than ToDate {ToDate}",
                    request.FromDate.Value, request.ToDate.Value);
                return BadRequest(new Response<object>
                {
                    Message = "FromDate cannot be greater than ToDate",
                    Code = ErrorCodes.BAD_REQUEST_ERROR
                });
            }



            var query = new GetInvoicesQuery(request);
            var result = await mediator.Send(query);

            logger.LogInformation("Successfully retrieved {Count} invoices out of {Total} total for page {PageIndex}",
                result.Data?.Count() ?? 0, result.Total, request.PageIndex);
            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while retrieving invoices");
            return StatusCode(500, new Response<object>
            {
                Message = "An internal server error occurred while retrieving invoices",
                Code = ErrorCodes.INTERNAL_SERVER_ERROR
            });
        }
    }

    /// <summary>
    /// Lấy danh sách hóa đơn theo MerchantBranchId với phân trang và filter
    /// </summary>
    /// <param name="request">Thông tin lọc và phân trang</param>
    /// <returns>Danh sách hóa đơn có phân trang</returns>
    [HttpGet("by-merchant-branch")]
    [Microsoft.AspNetCore.Authorization.AllowAnonymous]
    public async Task<IActionResult> GetInvoicesByMerchantBranchAsync([FromQuery] GetInvoicesByMerchantBranchRequest request)
    {
        try
        {
            logger.LogInformation("Getting invoices by MerchantBranchId: {MerchantBranchId}, PageIndex: {PageIndex}, PageSize: {PageSize}",
                request.MerchantBranchId, request.PageIndex, request.PageSize);

            // Validate date range
            if (request.FromDate.HasValue && request.ToDate.HasValue && request.FromDate.Value > request.ToDate.Value)
            {
                logger.LogWarning("Invalid date range: FromDate {FromDate} is greater than ToDate {ToDate}",
                    request.FromDate.Value, request.ToDate.Value);
                return BadRequest(new Response<object>
                {
                    Message = "FromDate cannot be greater than ToDate",
                    Code = ErrorCodes.BAD_REQUEST_ERROR
                });
            }

            var query = new GetInvoicesByMerchantBranchQuery(request);
            var result = await mediator.Send(query);

            if (result.Code == "000")
            {
                logger.LogInformation("Successfully retrieved {Count} invoices out of {Total} total for MerchantBranchId {MerchantBranchId} page {PageIndex}",
                    result.Data?.Count() ?? 0, result.Total, request.MerchantBranchId, request.PageIndex);
                return Ok(result);
            }

            return BadRequest(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while retrieving invoices by MerchantBranchId: {MerchantBranchId}", request.MerchantBranchId);
            return StatusCode(500, new Response<object>
            {
                Message = "An internal server error occurred while retrieving invoices by MerchantBranchId",
                Code = ErrorCodes.INTERNAL_SERVER_ERROR
            });
        }
    }

    /// <summary>
    /// Cập nhật trạng thái CQT và trạng thái hóa đơn theo InvoiceInfo Id
    /// </summary>
    /// <param name="id">ID của InvoiceInfo</param>
    /// <param name="request">Thông tin cập nhật trạng thái</param>
    /// <returns>Thông tin hóa đơn sau khi cập nhật</returns>
    [HttpPut("{id:guid}/status")]
    [Microsoft.AspNetCore.Authorization.AllowAnonymous]
    public async Task<IActionResult> UpdateInvoiceStatusAsync(Guid id, [FromBody] UpdateInvoiceStatusRequest request)
    {
        try
        {
            logger.LogInformation("Updating invoice status for Id: {Id}", id);

            var command = new UpdateInvoiceStatusCommand(id, request);
            var result = await mediator.Send(command);

            if (result.Code == "000" && result.Data != null)
            {
                logger.LogInformation("Successfully updated invoice status for Id: {Id}", id);
                return Ok(result);
            }

            if (result.Code == "404")
            {
                return NotFound(result);
            }

            return BadRequest(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while updating invoice status for Id: {Id}", id);
            return StatusCode(500, new Response<object>
            {
                Message = "An internal server error occurred while updating invoice status",
                Code = ErrorCodes.INTERNAL_SERVER_ERROR
            });
        }
    }

    /// <summary>
    /// Tạo một InvoiceInfo mới
    /// </summary>
    /// <param name="request">Thông tin tạo hóa đơn</param>
    /// <returns>Thông tin hóa đơn vừa tạo</returns>
    [HttpPost]
    [Microsoft.AspNetCore.Authorization.AllowAnonymous]
    public async Task<IActionResult> CreateInvoiceAsync([FromBody] CreateInvoiceRequest request)
    {
        try
        {
            logger.LogInformation("Creating invoice for InvoiceId: {InvoiceId}", request.InvoiceId);

            var command = new CreateInvoiceCommand(request);
            var result = await mediator.Send(command);

            if (result.Code == "000" && result.Data != null)
            {
                logger.LogInformation("Successfully created invoice with Id: {Id} for InvoiceId: {InvoiceId}",
                    result.Data.Id, request.InvoiceId);
                return Created($"/zenInvoice/api/invoice/{result.Data.Id}", result);
            }

            if (result.Code == "409")
            {
                return Conflict(result);
            }

            if (result.Code == "404")
            {
                return NotFound(result);
            }

            return BadRequest(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while creating invoice for InvoiceId: {InvoiceId}", request.InvoiceId);
            return StatusCode(500, new Response<object>
            {
                Message = "An internal server error occurred while creating invoice",
                Code = ErrorCodes.INTERNAL_SERVER_ERROR
            });
        }
    }
}
