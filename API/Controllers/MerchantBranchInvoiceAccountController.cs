﻿using Applications.DTOs.MerchantBranchInvoiceAccount;
using Applications.Features.MerchantBranchInvoiceAccount.Commands;
using Applications.Features.MerchantBranchInvoiceAccount.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Shared.Constants;
using BuildingBlocks.Abstractions;

namespace API.Controllers;

/// <summary>
/// Controller for managing merchant branch invoice accounts
/// Uses Simple Bearer Token authentication - bypasses B2B authentication complexity
/// </summary>
[Authorize(Policy = "SimpleBearerAuth")]
public class MerchantBranchInvoiceAccountController(IMediator mediator, ILogger<MerchantBranchInvoiceAccountController> logger) : BaseApiController
{
    /// <summary>
    /// Create a new merchant branch invoice account
    /// </summary>
    /// <param name="request">Merchant branch invoice account creation request</param>
    /// <returns>Created merchant branch invoice account</returns>
    [HttpPost]
    // [AllowAnonymous]
    public async Task<IActionResult> CreateMerchantBranchInvoiceAccount([FromBody] MerchantBranchInvoiceAccountDto request)
    {
        try
        {
            logger.LogInformation("Creating merchant branch invoice account for TaxNumber: {TaxNumber}", request?.TaxNumber);

            if (request == null)
            {
                logger.LogWarning("CreateMerchantBranchInvoiceAccount called with null request");
                return BadRequest(new Response<object>
                {
                    Message = "Request cannot be null",
                    Code = ErrorCodes.BAD_REQUEST_ERROR
                });
            }

            var command = new CreateMerchantBranchInvoiceAccountCommand(request);
            var result = await mediator.Send(command);
            return Ok(result);
            // if (result.IsSuccess)
            // {
            //     logger.LogInformation("Successfully created merchant branch invoice account with Id: {Id}", result.Data.Id);
            //     return Ok(result);
            // }

            // logger.LogWarning("Failed to create merchant branch invoice account: {Message}", result.Message);
            // return BadRequest(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error creating merchant branch invoice account");
            return StatusCode(500, new Response<object>
            {
                Message = "Internal server error",
                Code = ErrorCodes.INTERNAL_SERVER_ERROR
            });
        }
    }

    /// <summary>
    /// Get all merchant branch invoice accounts
    /// </summary>
    /// <returns>List of all merchant branch invoice accounts</returns>
    [HttpGet]
    // [AllowAnonymous]
    public async Task<IActionResult> GetAllMerchantBranchInvoiceAccounts()
    {
        try
        {
            logger.LogInformation("Retrieving all merchant branch invoice accounts");

            var query = new GetAllMerchantBranchInvoiceAccountsQuery();
            var result = await mediator.Send(query);

            if (result.IsSuccess)
            {
                logger.LogInformation("Successfully retrieved {Count} merchant branch invoice accounts", result.Data?.Count ?? 0);
                return Ok(result);
            }

            logger.LogWarning("Failed to retrieve merchant branch invoice accounts: {Message}", result.Message);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving merchant branch invoice accounts");
            return StatusCode(500, new Response<object>
            {
                Message = "Internal server error",
                Code = ErrorCodes.INTERNAL_SERVER_ERROR
            });
        }
    }

    /// <summary>
    /// Get a merchant branch invoice account by ID
    /// </summary>
    /// <param name="id">The ID of the merchant branch invoice account</param>
    /// <returns>Merchant branch invoice account details</returns>
    [HttpGet("{id}")]
    public async Task<IActionResult> GetMerchantBranchInvoiceAccountById(Guid id)
    {
        try
        {
            logger.LogInformation("Retrieving merchant branch invoice account with Id: {Id}", id);

            var query = new GetMerchantBranchInvoiceAccountByIdQuery(id);
            var result = await mediator.Send(query);

            if (result.IsSuccess)
            {
                logger.LogInformation("Successfully retrieved merchant branch invoice account with Id: {Id}", id);
                return Ok(result);
            }

            if (result.Code == ErrorCodes.NOT_FOUND_DATA)
            {
                logger.LogWarning("Merchant branch invoice account with Id {Id} not found", id);
                return NotFound(result);
            }

            logger.LogWarning("Failed to retrieve merchant branch invoice account with Id {Id}: {Message}", id, result.Message);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving merchant branch invoice account with Id: {Id}", id);
            return StatusCode(500, new Response<object>
            {
                Message = "Internal server error",
                Code = ErrorCodes.INTERNAL_SERVER_ERROR
            });
        }
    }

    /// <summary>
    /// Update an existing merchant branch invoice account
    /// </summary>
    /// <param name="id">The ID of the merchant branch invoice account to update</param>
    /// <param name="request">Updated merchant branch invoice account data</param>
    /// <returns>Updated merchant branch invoice account</returns>
    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateMerchantBranchInvoiceAccount(Guid id, [FromBody] MerchantBranchInvoiceAccountDto request)
    {
        try
        {
            logger.LogInformation("Updating merchant branch invoice account with Id: {Id}", id);

            if (request == null)
            {
                logger.LogWarning("UpdateMerchantBranchInvoiceAccount called with null request");
                return BadRequest(new Response<object>
                {
                    Message = "Request cannot be null",
                    Code = ErrorCodes.BAD_REQUEST_ERROR
                });
            }

            var command = new UpdateMerchantBranchInvoiceAccountCommand(id, request);
            var result = await mediator.Send(command);

            if (result.IsSuccess)
            {
                logger.LogInformation("Successfully updated merchant branch invoice account with Id: {Id}", id);
                return Ok(result);
            }

            if (result.Code == ErrorCodes.NOT_FOUND_DATA)
            {
                logger.LogWarning("Merchant branch invoice account with Id {Id} not found for update", id);
                return NotFound(result);
            }

            logger.LogWarning("Failed to update merchant branch invoice account with Id {Id}: {Message}", id, result.Message);
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating merchant branch invoice account with Id: {Id}", id);
            return StatusCode(500, new Response<object>
            {
                Message = "Internal server error",
                Code = ErrorCodes.INTERNAL_SERVER_ERROR
            });
        }
    }
}

