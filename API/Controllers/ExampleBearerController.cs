using API.Attributes;
using Applications.Interfaces.Services;
using BuildingBlocks.Abstractions;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers;

/// <summary>
/// Example controller demonstrating simple Bearer Token Authentication
/// </summary>
[ApiController]
[Route("zenInvoice/api/[controller]")]
public class ExampleBearerController : BearerAuthBaseController
{
    private readonly ILogger<ExampleBearerController> _logger;

    public ExampleBearerController(
        ICurrentUserService currentUserService,
        ILogger<ExampleBearerController> logger)
        : base(currentUserService)
    {
        _logger = logger;
    }

    /// <summary>
    /// Public endpoint - no authentication required
    /// </summary>
    /// <returns>Public information</returns>
    [HttpGet("public")]
    public async Task<IActionResult> GetPublicInfo()
    {
        _logger.LogInformation("Public endpoint accessed");

        return Ok(new Response<object>
        {
            Code = "000",
            Message = "Success",
            Data = new
            {
                Message = "This is a public endpoint",
                Timestamp = DateTime.UtcNow,
                IsAuthenticated = IsAuthenticated,
                CurrentUser = IsAuthenticated ? CurrentPartnerName : "Anonymous"
            }
        });
    }

    /// <summary>
    /// Protected endpoint - requires Bearer token authentication
    /// </summary>
    /// <returns>Current user information</returns>
    [HttpGet("protected")]
    [BearerTokenAuth]
    public async Task<IActionResult> GetProtectedInfo()
    {
        _logger.LogInformation("Protected endpoint accessed by partner {PartnerId}", CurrentPartnerId);

        var partner = await GetCurrentPartnerAsync();

        return Ok(new Response<object>
        {
            Code = "000",
            Message = "Success",
            Data = new
            {
                Message = "This is a protected endpoint",
                Timestamp = DateTime.UtcNow,
                CurrentUser = new
                {
                    PartnerId = CurrentPartnerId,
                    PartnerName = CurrentPartnerName,
                    TokenExpiration = TokenExpiration,
                    IsSimpleBearerAuth = IsSimpleBearerAuth,
                    Scopes = CurrentUserScopes,
                    Partner = partner != null ? new
                    {
                        partner.Id,
                        partner.Name,
                        partner.ClientId,
                        partner.ContactEmail,
                        partner.IsActive,
                        partner.ApiRateLimitPerHour,
                        partner.MonthlyInvoiceLimit,
                        partner.CurrentMonthUsage
                    } : null
                }
            }
        });
    }

    /// <summary>
    /// Get current user profile with detailed information
    /// </summary>
    /// <returns>Detailed user profile</returns>
    [HttpGet("profile")]
    [BearerTokenAuth]
    public async Task<IActionResult> GetUserProfile()
    {
        _logger.LogInformation("User profile requested by partner {PartnerId}", CurrentPartnerId);

        try
        {
            var partner = await GetCurrentPartnerOrThrowAsync();
            var partnerWithDetails = await GetCurrentPartnerWithDetailsAsync();

            return Ok(new Response<object>
            {
                Code = "000",
                Message = "Success",
                Data = new
                {
                    Profile = new
                    {
                        partner.Id,
                        partner.Name,
                        partner.ClientId,
                        partner.ContactEmail,
                        partner.ContactPhone,
                        partner.Description,
                        partner.IsActive,
                        partner.ApiRateLimitPerHour,
                        partner.MonthlyInvoiceLimit,
                        partner.CurrentMonthUsage,
                        partner.EnableIpWhitelist,
                        partner.CreatedAt,
                        partner.UpdatedAt
                    },
                    ActiveTokens = partnerWithDetails?.PartnerTokens?.Count(t => t.IsValid) ?? 0,
                    Roles = partnerWithDetails?.RoleAssignments?.Where(ra => ra.IsValid)
                        .Select(ra => new
                        {
                            RoleName = ra.Role.Name,
                            RoleDescription = ra.Role.Description,
                            ra.AssignedAt,
                            ra.ExpiresAt
                        }).ToArray() ?? Array.Empty<object>(),
                    Permissions = partnerWithDetails?.FunctionPermissions?.Where(fp => !fp.IsDeleted)
                        .Select(fp => new
                        {
                            FunctionName = fp.Function.Name,
                            FunctionDescription = fp.Function.Description,
                            PermissionName = fp.Permission.Name,
                            PermissionDescription = fp.Permission.Description
                        }).ToArray() ?? Array.Empty<object>(),
                    AuthenticationInfo = new
                    {
                        TokenExpiration = TokenExpiration,
                        IsSimpleBearerAuth = IsSimpleBearerAuth,
                        Scopes = CurrentUserScopes
                    }
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user profile for partner {PartnerId}", CurrentPartnerId);
            return StatusCode(500, new Response<object>
            {
                Code = "500",
                Message = "Internal server error while getting user profile"
            });
        }
    }

    /// <summary>
    /// Update current user's basic information
    /// </summary>
    /// <param name="request">Update request</param>
    /// <returns>Updated user information</returns>
    [HttpPut("profile")]
    [BearerTokenAuth]
    public async Task<IActionResult> UpdateProfile([FromBody] UpdateProfileRequest request)
    {
        _logger.LogInformation("Profile update requested by partner {PartnerId}", CurrentPartnerId);

        try
        {
            var partner = await GetCurrentPartnerOrThrowAsync();

            // Update allowed fields
            if (!string.IsNullOrWhiteSpace(request.ContactEmail))
                partner.ContactEmail = request.ContactEmail;

            if (!string.IsNullOrWhiteSpace(request.ContactPhone))
                partner.ContactPhone = request.ContactPhone;

            if (!string.IsNullOrWhiteSpace(request.Description))
                partner.Description = request.Description;

            // Note: In a real implementation, you would save changes to database here
            // For this example, we'll just return the updated data

            return Ok(new Response<object>
            {
                Code = "000",
                Message = "Profile updated successfully",
                Data = new
                {
                    partner.Id,
                    partner.Name,
                    partner.ContactEmail,
                    partner.ContactPhone,
                    partner.Description,
                    UpdatedAt = DateTime.UtcNow
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating profile for partner {PartnerId}", CurrentPartnerId);
            return StatusCode(500, new Response<object>
            {
                Code = "500",
                Message = "Internal server error while updating profile"
            });
        }
    }
}

/// <summary>
/// Request model for updating user profile
/// </summary>
public class UpdateProfileRequest
{
    public string? ContactEmail { get; set; }
    public string? ContactPhone { get; set; }
    public string? Description { get; set; }
}