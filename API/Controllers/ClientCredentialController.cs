using API.Attributes;
using Applications.Features.ClientCredentials.Commands;
using Applications.Features.ClientCredentials.Queries;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers;

/// <summary>
/// Controller quản lý thông tin xác thực đối tác
/// </summary>
[Route("api/[controller]")]
[ApiController]
public class ClientCredentialController : ControllerBase
{
    private readonly IMediator _mediator;

    public ClientCredentialController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// Tạo mới client credential
    /// </summary>
    /// <param name="command">Thông tin client credential mới</param>
    /// <returns>Thông tin client credential đã tạo</returns>
    [HttpPost]
    [RequirePermission("ADMIN_MANAGE", "CREATE")]
    public async Task<IActionResult> Create(CreateClientCredentialCommand command) => Ok(await _mediator.Send(command));

    /// <summary>
    /// Cập nhật client credential
    /// </summary>
    /// <param name="command">Thông tin cập nhật</param>
    /// <returns>Kết quả cập nhật</returns>
    [HttpPut]
    [RequirePermission("ADMIN_MANAGE", "UPDATE")]
    public async Task<IActionResult> Update(UpdateClientCredentialCommand command) => Ok(await _mediator.Send(command));

    /// <summary>
    /// Xóa client credential
    /// </summary>
    /// <param name="id">ID của client credential cần xóa</param>
    /// <returns>Kết quả xóa</returns>
    [HttpDelete("{id}")]
    [RequirePermission("ADMIN_MANAGE", "DELETE")]
    public async Task<IActionResult> Delete(Guid id) => Ok(await _mediator.Send(new DeleteClientCredentialCommand { Id = id }));

    /// <summary>
    /// Reset secret cho client credential
    /// </summary>
    /// <param name="id">ID của client credential cần reset</param>
    /// <returns>Secret mới</returns>
    [HttpPost("{id}/reset-secret")]
    [RequirePermission("ADMIN_MANAGE", "UPDATE")]
    public async Task<IActionResult> ResetSecret(Guid id) => Ok(await _mediator.Send(new ResetClientSecretCommand { Id = id }));

    /// <summary>
    /// Lấy thông tin client credential theo ID
    /// </summary>
    /// <param name="id">ID của client credential</param>
    /// <returns>Thông tin client credential</returns>
    [HttpGet("{id}")]
    [RequirePermission("ADMIN_MANAGE", "READ")]
    public async Task<IActionResult> GetById(Guid id) => Ok(await _mediator.Send(new GetClientCredentialByIdQuery { Id = id }));

    /// <summary>
    /// Lấy danh sách client credentials
    /// </summary>
    /// <returns>Danh sách client credentials</returns>
    [HttpGet]
    [RequirePermission("ADMIN_MANAGE", "READ")]
    public async Task<IActionResult> GetList() => Ok(await _mediator.Send(new GetClientCredentialListQuery()));
}
