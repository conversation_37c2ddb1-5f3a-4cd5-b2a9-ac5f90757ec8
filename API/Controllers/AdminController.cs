﻿using API.Attributes;
using Applications.DTOs.Partners;
using Applications.Features.Partners.Commands;
using Applications.Interfaces.Services.Authorization;
using Infrastructure.Services.Security;
using Mapster;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Shared.Constants;
using BuildingBlocks.Abstractions;
using System.Security.Claims;

namespace API.Controllers;

/// <summary>
/// Controller quáº£n trá»‹ há»‡ thá»‘ng authentication/authorization
// /// </summary>
// [ApiController]
// [Route("api/[controller]")]
// [Produces("application/json")]
[AllowAnonymous]
public class AdminController(
    IPermissionService permissionService,
    IRoleService roleService,
    IConstraintService constraintService,
    IUsageTrackingService usageTrackingService,
    IMediator mediator,
    SecretMigrationService secretMigrationService,
    ILogger<AdminController> logger) : BaseApiController
{

    #region Partner Management

    /// <summary>
    /// Táº¡o partner má»›i
    /// </summary>
    /// <param name="request">ThÃ´ng tin partner cáº§n táº¡o</param>
    /// <returns>ThÃ´ng tin partner Ä‘Ã£ táº¡o</returns>
    [HttpPost("partners")]
    // [RequirePermission("ADMIN_MANAGE", "CREATE")]
    public async Task<IActionResult> CreatePartner([FromBody] CreatePartnerRequest request)
    {
        try
        {
            // âœ… Validate request
            if (request == null)
            {
                logger.LogWarning("CreatePartner called with null request");
                return BadRequest(new Response<object> { Message = 
                    "Request cannot be null", Code = ErrorCodes.BAD_REQUEST_ERROR });
            }

            logger.LogInformation("Creating new partner with ClientId: {ClientId}", request.ClientId);

            // âœ… Validate mediator
            if (mediator == null)
            {
                logger.LogError("Mediator is null - dependency injection failed");
                return StatusCode(500, new Response<object> { Message = 
                    "Internal server error - mediator not available", Code = ErrorCodes.INTERNAL_SERVER_ERROR });
            }

            // Get current user ID from JWT token
            var currentUserId = GetCurrentUserId();
            logger.LogDebug("Current user ID: {UserId}", currentUserId);

            // Map request to command
            // var command = request.Adapt<CreatePartnerCommand>();
            // command.CreatedBy = currentUserId;

            var command = new CreatePartnerCommand
            {
                ClientId = request.ClientId,
                ClientSecret = request.ClientSecret,
                HmacSecret = request.HmacSecret,
                Name = request.Name,
                ContactEmail = request.ContactEmail,
                ContactPhone = request.ContactPhone,
                Description = request.Description,
                IpWhitelist = request.IpWhitelist,
                EnableIpWhitelist = request.EnableIpWhitelist,
                IsActive = request.IsActive,
                ApiRateLimitPerHour = request.ApiRateLimitPerHour,
                MonthlyInvoiceLimit = request.MonthlyInvoiceLimit,
                CreatedBy = currentUserId 
            };

            // âœ… Validate command

            logger.LogDebug("Sending command to mediator: {@Command}", new
            {
                command.ClientId,
                command.Name,
                command.CreatedBy,
                HasClientSecret = !string.IsNullOrEmpty(command.ClientSecret),
                HasHmacSecret = !string.IsNullOrEmpty(command.HmacSecret)
            });

            // Execute command
            var result = await mediator.Send(command);

            logger.LogDebug("Mediator returned result: {@Result}", new
            {
                result.IsSuccess,
                result.Message,
                HasData = result.Data != null
            });

            if (!result.IsSuccess)
            {
                logger.LogWarning("Failed to create partner {ClientId}: {Error}", request.ClientId, result.Message);
                return BadRequest(result);
            }

            logger.LogInformation("Partner created successfully with ClientId: {ClientId}", request.ClientId);
            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error creating partner with ClientId: {ClientId}", request.ClientId);
            return StatusCode(500, new Response<object> { Message = 
                "Internal server error while creating partner", Code = ErrorCodes.INTERNAL_SERVER_ERROR });
        }
    }

    /// <summary>
    /// Láº¥y thÃ´ng tin quyá»n háº¡n cá»§a Ä‘á»‘i tÃ¡c
    /// </summary>
    /// <param name="partnerId">ID cá»§a Ä‘á»‘i tÃ¡c</param>
    /// <returns>ThÃ´ng tin quyá»n háº¡n</returns>
    [HttpGet("partners/{partnerId}/permissions")]
    [RequirePermission("ADMIN_MANAGE", "READ")]
    public async Task<IActionResult> GetPartnerPermissions(Guid partnerId)
    {
        try
        {
            logger.LogInformation("Getting permissions for partner {PartnerId}", partnerId);

            var result = await permissionService.GetPartnerPermissionsAsync(partnerId);

            if (!result.IsSuccess)
            {
                return BadRequest(new Response<object> { Message = result.Message, Code = ErrorCodes.BAD_REQUEST_ERROR });
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting partner permissions for {PartnerId}", partnerId);
            return StatusCode(500, new Response<object> { Message = 
                "Internal server error", Code = ErrorCodes.INTERNAL_SERVER_ERROR });
        }
    }

    /// <summary>
    /// Cáº¥p quyá»n cho Ä‘á»‘i tÃ¡c
    /// </summary>
    /// <param name="partnerId">ID cá»§a Ä‘á»‘i tÃ¡c</param>
    /// <param name="request">ThÃ´ng tin quyá»n cáº§n cáº¥p</param>
    /// <returns>Káº¿t quáº£ cáº¥p quyá»n</returns>
    [HttpPost("partners/{partnerId}/permissions")]
    [RequirePermission("ADMIN_MANAGE", "CREATE")]
    public async Task<IActionResult> GrantPartnerPermission(Guid partnerId, [FromBody] GrantPermissionRequest request)
    {
        try
        {
            logger.LogInformation("Granting permission {FunctionCode}:{PermissionCode} to partner {PartnerId}",
                request.FunctionCode, request.PermissionCode, partnerId);

            var currentUserId = GetCurrentUserId(); // Implement this method to get current user from JWT

            var result = await permissionService.GrantPermissionAsync(
                partnerId,
                request.FunctionCode,
                request.PermissionCode,
                currentUserId,
                request.Reason,
                request.ExpiresAt);

            if (!result.IsSuccess)
            {
                return BadRequest(new Response<object> { Message = result.Message, Code = ErrorCodes.BAD_REQUEST_ERROR });
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error granting permission to partner {PartnerId}", partnerId);
            return StatusCode(500, new Response<object> { Message = 
                "Internal server error", Code = ErrorCodes.INTERNAL_SERVER_ERROR });
        }
    }

    /// <summary>
    /// Thu há»“i quyá»n cá»§a Ä‘á»‘i tÃ¡c
    /// </summary>
    /// <param name="partnerId">ID cá»§a Ä‘á»‘i tÃ¡c</param>
    /// <param name="request">ThÃ´ng tin quyá»n cáº§n thu há»“i</param>
    /// <returns>Káº¿t quáº£ thu há»“i quyá»n</returns>
    [HttpDelete("partners/{partnerId}/permissions")]
    [RequirePermission("ADMIN_MANAGE", "DELETE")]
    public async Task<IActionResult> RevokePartnerPermission(Guid partnerId, [FromBody] RevokePermissionRequest request)
    {
        try
        {
            logger.LogInformation("Revoking permission {FunctionCode}:{PermissionCode} from partner {PartnerId}",
                request.FunctionCode, request.PermissionCode, partnerId);

            var currentUserId = GetCurrentUserId();

            var result = await permissionService.RevokePermissionAsync(
                partnerId,
                request.FunctionCode,
                request.PermissionCode,
                currentUserId,
                request.Reason);

            if (!result.IsSuccess)
            {
                return BadRequest(new Response<object> { Message = result.Message, Code = ErrorCodes.BAD_REQUEST_ERROR });
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error revoking permission from partner {PartnerId}", partnerId);
            return StatusCode(500, new Response<object> { Message = 
                "Internal server error", Code = ErrorCodes.INTERNAL_SERVER_ERROR });
        }
    }

    #endregion

    #region Role Management

    /// <summary>
    /// Láº¥y danh sÃ¡ch táº¥t cáº£ vai trÃ²
    /// </summary>
    /// <returns>Danh sÃ¡ch vai trÃ²</returns>
    [HttpGet("roles")]
    [RequirePermission("ADMIN_MANAGE", "READ")]
    public async Task<IActionResult> GetAllRoles()
    {
        try
        {
            logger.LogInformation("Getting all roles");

            var result = await roleService.GetAllRolesAsync();

            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting all roles");
            return StatusCode(500, new Response<object> { Message = 
                "Internal server error", Code = ErrorCodes.INTERNAL_SERVER_ERROR });
        }
    }

    /// <summary>
    /// GÃ¡n vai trÃ² cho Ä‘á»‘i tÃ¡c
    /// </summary>
    /// <param name="partnerId">ID cá»§a Ä‘á»‘i tÃ¡c</param>
    /// <param name="request">ThÃ´ng tin vai trÃ² cáº§n gÃ¡n</param>
    /// <returns>Káº¿t quáº£ gÃ¡n vai trÃ²</returns>
    [HttpPost("partners/{partnerId}/roles")]
    [RequirePermission("ADMIN_MANAGE", "CREATE")]
    public async Task<IActionResult> AssignRoleToPartner(Guid partnerId, [FromBody] AssignRoleRequest request)
    {
        try
        {
            logger.LogInformation("Assigning role {RoleCode} to partner {PartnerId}", request.RoleCode, partnerId);

            var currentUserId = GetCurrentUserId();

            var result = await roleService.AssignRoleAsync(
                partnerId,
                request.RoleCode,
                currentUserId,
                request.Reason,
                request.ExpiresAt);

            if (!result.IsSuccess)
            {
                return BadRequest(new Response<object> { Message = result.Message, Code = ErrorCodes.BAD_REQUEST_ERROR });
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error assigning role to partner {PartnerId}", partnerId);
            return StatusCode(500, new Response<object> { Message = 
                "Internal server error", Code = ErrorCodes.INTERNAL_SERVER_ERROR });
        }
    }

    #endregion

    #region Constraint Management

    /// <summary>
    /// Láº¥y thÃ´ng tin rÃ ng buộc cá»§a Ä‘á»‘i tÃ¡c
    /// </summary>
    /// <param name="partnerId">ID cá»§a Ä‘á»‘i tÃ¡c</param>
    /// <returns>ThÃ´ng tin rÃ ng buộc</returns>
    [HttpGet("partners/{partnerId}/constraints")]
    [RequirePermission("ADMIN_MANAGE", "READ")]
    public async Task<IActionResult> GetPartnerConstraints(Guid partnerId)
    {
        try
        {
            logger.LogInformation("Getting constraints for partner {PartnerId}", partnerId);

            var result = await constraintService.GetPartnerConstraintsAsync(partnerId);

            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting partner constraints for {PartnerId}", partnerId);
            return StatusCode(500, new Response<object> { Message = 
                "Internal server error", Code = ErrorCodes.INTERNAL_SERVER_ERROR });
        }
    }

    /// <summary>
    /// Thiáº¿t láº­p rÃ ng buộc cho Ä‘á»‘i tÃ¡c
    /// </summary>
    /// <param name="partnerId">ID cá»§a Ä‘á»‘i tÃ¡c</param>
    /// <param name="request">ThÃ´ng tin rÃ ng buộc</param>
    /// <returns>Káº¿t quáº£ thiáº¿t láº­p</returns>
    [HttpPost("partners/{partnerId}/constraints")]
    [RequirePermission("ADMIN_MANAGE", "CREATE")]
    public async Task<IActionResult> SetPartnerConstraint(Guid partnerId, [FromBody] SetConstraintRequest request)
    {
        try
        {
            logger.LogInformation("Setting constraint {ConstraintType} for partner {PartnerId}",
                request.ConstraintType, partnerId);

            var currentUserId = GetCurrentUserId();

            var result = await constraintService.SetConstraintAsync(
                partnerId,
                request.ConstraintType,
                request.ConstraintValue,
                request.ValidFrom,
                request.ValidTo,
                currentUserId,
                request.Description,
                request.Priority);

            if (!result.IsSuccess)
            {
                return BadRequest(new Response<object> { Message = result.Message, Code = ErrorCodes.BAD_REQUEST_ERROR });
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error setting constraint for partner {PartnerId}", partnerId);
            return StatusCode(500, new Response<object> { Message = 
                "Internal server error", Code = ErrorCodes.INTERNAL_SERVER_ERROR });
        }
    }

    #endregion

    #region Usage Tracking

    /// <summary>
    /// Láº¥y thá»‘ng kÃª sá»­ dá»¥ng cá»§a Ä‘á»‘i tÃ¡c
    /// </summary>
    /// <param name="partnerId">ID cá»§a Ä‘á»‘i tÃ¡c</param>
    /// <param name="period">Ká»³ thá»‘ng kÃª (vÃ­ dá»¥: "2024-01")</param>
    /// <returns>Thá»‘ng kÃª sá»­ dá»¥ng</returns>
    [HttpGet("partners/{partnerId}/usage")]
    [RequirePermission("ADMIN_MANAGE", "READ")]
    public async Task<IActionResult> GetPartnerUsage(Guid partnerId, [FromQuery] string period)
    {
        try
        {
            logger.LogInformation("Getting usage statistics for partner {PartnerId}, period {Period}",
                partnerId, period);

            var result = await usageTrackingService.GetUsageStatisticsAsync(partnerId, period);

            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting usage statistics for partner {PartnerId}", partnerId);
            return StatusCode(500, new Response<object> { Message = 
                "Internal server error", Code = ErrorCodes.INTERNAL_SERVER_ERROR });
        }
    }

    #endregion

    #region Security Management

    /// <summary>
    /// Migrate existing plaintext HMAC secrets to encrypted format
    /// </summary>
    /// <returns>Migration result</returns>
    [HttpPost("migrate-hmac-secrets")]
    [RequirePermission("ADMIN_MANAGE", "EXECUTE")]
    public async Task<IActionResult> MigrateHmacSecrets()
    {
        try
        {
            logger.LogInformation("Starting HMAC secret migration");

            var migratedCount = await secretMigrationService.MigrateHmacSecretsAsync();

            logger.LogInformation("HMAC secret migration completed. Migrated {Count} secrets", migratedCount);

            return Ok(new Response<object>(new { MigratedCount = migratedCount }, $"Successfully migrated {migratedCount} HMAC secrets"));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during HMAC secret migration");
            return StatusCode(500, new Response<object> { Message = 
                "Internal server error during migration", Code = ErrorCodes.INTERNAL_SERVER_ERROR });
        }
    }

    /// <summary>
    /// Verify that all encrypted HMAC secrets can be decrypted
    /// </summary>
    /// <returns>Verification result</returns>
    [HttpPost("verify-hmac-secrets")]
    [RequirePermission("ADMIN_MANAGE", "READ")]
    public async Task<IActionResult> VerifyHmacSecrets()
    {
        try
        {
            logger.LogInformation("Starting HMAC secret verification");

            var isValid = await secretMigrationService.VerifyEncryptedSecretsAsync();

            logger.LogInformation("HMAC secret verification completed. Result: {IsValid}", isValid);

            return Ok(new Response<object>(new { IsValid = isValid }, isValid ? "All HMAC secrets verified successfully" : "Some HMAC secrets failed verification"));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during HMAC secret verification");
            return StatusCode(500, new Response<object> { Message = 
                "Internal server error during verification", Code = ErrorCodes.INTERNAL_SERVER_ERROR });
        }
    }

    #endregion

    #region Helper Methods

    /// <summary>
    /// Get current user ID from JWT token
    /// </summary>
    /// <returns>Current user ID</returns>
    private Guid GetCurrentUserId()
    {
        // For now, return a default admin user ID
        // TODO: Implement proper JWT token parsing
        return Guid.Parse("*************-5555-5555-************"); // Admin user from seed data
    }

    #endregion
}

#region DTOs

/// <summary>
/// Request Ä‘á»ƒ cáº¥p quyá»n
/// </summary>
public class GrantPermissionRequest
{
    public string FunctionCode { get; set; } = null!;
    public string PermissionCode { get; set; } = null!;
    public string Reason { get; set; } = null!;
    public DateTime? ExpiresAt { get; set; }
}

/// <summary>
/// Request Ä‘á»ƒ thu há»“i quyá»n
/// </summary>
public class RevokePermissionRequest
{
    public string FunctionCode { get; set; } = null!;
    public string PermissionCode { get; set; } = null!;
    public string Reason { get; set; } = null!;
}

/// <summary>
/// Request Ä‘á»ƒ gÃ¡n vai trÃ²
/// </summary>
public class AssignRoleRequest
{
    public string RoleCode { get; set; } = null!;
    public string Reason { get; set; } = null!;
    public DateTime? ExpiresAt { get; set; }
}

/// <summary>
/// Request Ä‘á»ƒ thiáº¿t láº­p rÃ ng buộc
/// </summary>
public class SetConstraintRequest
{
    public string ConstraintType { get; set; } = null!;
    public string ConstraintValue { get; set; } = null!;
    public DateTime ValidFrom { get; set; } = DateTime.UtcNow;
    public DateTime? ValidTo { get; set; }
    public string? Description { get; set; }
    public int Priority { get; set; } = 100;
}

#endregion

