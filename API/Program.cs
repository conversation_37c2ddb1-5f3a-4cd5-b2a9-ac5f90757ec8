//using API.Conventions;
//using API.Extensions;
//using API.Middlewares;
//using Applications;
//using Applications.Behaviors;
//using Applications.Features.ClientCredentials.Commands;
//using Applications.Interfaces.Repositories;
//using Applications.Interfaces.Services;
//using Applications.MappingProfiles;
//using Applications.Services.Interfaces;
//using Core.Interfaces;
//using FluentValidation;
//using Infrastructure.DependencyInjections;
//using Infrastructure.Helpers;
//using Infrastructure.Identity;
//using Infrastructure.Persistences;
//using Infrastructure.Persistences.Repositories;
//using Infrastructure.Services;
//using MediatR;
//using Microsoft.AspNetCore.Mvc.ApplicationModels;
//using Microsoft.EntityFrameworkCore;
//using Serilog;
//using Shared.Interfaces;

//var builder = WebApplication.CreateBuilder(args);

//// =============================
//// 📁 Load cấu hình theo môi trường
//// =============================
//builder.Configuration
//    .SetBasePath(Directory.GetCurrentDirectory())
//    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
//    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true, reloadOnChange: true)
//    .AddEnvironmentVariables();

//// =============================
//// 🔧 Logging: Serilog
//// =============================
//builder.Host.UseSerilog((context, services, configuration) =>
//{
//    configuration
//        .ReadFrom.Configuration(context.Configuration)
//        .ReadFrom.Services(services)
//        .Enrich.WithProperty("Application", "ZenPay");
//});

//// =============================
//// 📦 Đăng ký các dịch vụ
//// =============================
//var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
//builder.Services.AddInfrastructureServices(connectionString);
//builder.Services.AddRepositories();

//builder.Services.AddMediatR(cfg =>
//{
//    cfg.RegisterServicesFromAssembly(typeof(ApplicationAssembly).Assembly);
//});
//builder.Services.AddAutoMapper(typeof(MappingProfile).Assembly);

//// External Configuration & Services
//builder.Services.AddScoped<IJwtTokenGenerator, JwtTokenGenerator>();
//builder.Services.AddScoped<IPaginationHelper, PaginationHelper>();
//builder.Services.AddScoped<IPasswordHasher, PasswordHasher>();
//builder.Services.AddScoped<IClientCredentialRepository, ClientCredentialRepository>();
//builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();
//builder.Services.AddHttpContextAccessor();
//builder.Services.AddValidatorsFromAssembly(typeof(CreateClientCredentialCommand).Assembly);
//builder.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
//builder.Services.AddScoped<INotificationService, NotificationService>();
//builder.Services.AddScoped<IMBFSmsBranchnameService, MBFSmsBranchnameService>();
//builder.Services.AddScoped<ISmsLogRepository, SmsLogRepository>();
//builder.Services.AddScoped<ISmsRetryQueueRepository, SmsRetryQueueRepository>();
//// =============================
//// ⚙️ Cấu hình Controller & JSON
//// =============================
//builder.Services.AddControllers(options =>
//{
//    // ✅ Đặt RouteTokenTransformer tại đây (Controller-level)
//    options.Conventions.Add(new RouteTokenTransformerConvention(new SlugifyParameterTransformer()));
//})
//.AddJsonOptions(options =>
//{
//    // ✅ Đặt camelCase cho JSON (Property & Dictionary keys)
//    options.JsonSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
//    options.JsonSerializerOptions.DictionaryKeyPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
//});

//// =============================
//// 🌐 CORS
//// =============================
//builder.Services.AddCors(options =>
//{
//    options.AddPolicy("AllowAll", policy =>
//    {
//        policy.AllowAnyOrigin().AllowAnyHeader().AllowAnyMethod();
//    });
//});

//// =============================
//// 🔐 JWT Authentication
//// =============================
//builder.Services.AddJwtAuthentication(builder.Configuration);

//// =============================
//// 🧪 Swagger
//// =============================
//builder.Services.AddEndpointsApiExplorer();
//builder.Services.AddSwaggerGen(c =>
//{
//    // Thêm schema xác thực dạng Bearer
//    c.AddSecurityDefinition("Bearer", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
//    {
//        Name = "Authorization",
//        Type = Microsoft.OpenApi.Models.SecuritySchemeType.Http,
//        Scheme = "bearer",
//        BearerFormat = "JWT",
//        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
//        Description = "Nhập token dạng: **Bearer your-token-here**"
//    });

//    // Áp dụng schema này cho tất cả các API
//    c.AddSecurityRequirement(new Microsoft.OpenApi.Models.OpenApiSecurityRequirement
//    {
//        {
//            new Microsoft.OpenApi.Models.OpenApiSecurityScheme
//            {
//                Reference = new Microsoft.OpenApi.Models.OpenApiReference
//                {
//                    Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
//                    Id = "Bearer"
//                }
//            },
//            Array.Empty<string>()
//        }
//    });
//});

//var app = builder.Build();
//// ✅ Đặt TraceIdMiddleware đầu tiên trong middleware pipeline
//app.UseMiddleware<TraceIdMiddleware>();  // Đây là middleware sẽ tạo TraceId cho mỗi request
//// =============================
//// 🌱 Seed database nếu cần
//// =============================
//await app.MigrateAndSeedAsync();

//// =============================
//// 🧱 Middleware pipeline
//// =============================
////if (app.Environment.IsDevelopment())
////{
////    app.UseSwagger();
////    app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "API V1"));
////}
//// ✅ Luôn bật Swagger bất kể môi trường
//app.UseSwagger();
//app.UseSwaggerUI(c =>
//{
//    c.SwaggerEndpoint("/swagger/v1/swagger.json", "API V1");
//});
//app.UseMiddleware<ExceptionHandlingMiddleware>();
//app.UseHttpsRedirection();
//app.UseCors("AllowAll");
//app.UseAuthentication();
//app.UseAuthorization();

//app.MapControllers();
//app.UseMiddleware<SerilogEnrichMiddleware>();

//app.Run();

using API.Conventions;
using API.Extensions;
using API.Filters;
using API.Middlewares;
using Applications;
using BuildingBlocks.Behaviors;
using Applications.Features.ClientCredentials.Commands;
using Applications.Interfaces.Repositories;
using Applications.Interfaces.Services.Authentication;
using Applications.Interfaces.Services.Authorization;
using Microsoft.AspNetCore.Authorization;
using Applications.Interfaces.Services.Security;
using Applications.MappingProfiles;
using Core.Interfaces;
using FluentValidation;
using Infrastructure.DependencyInjections;
using Infrastructure.Identity;
using Infrastructure.Persistences;
using Infrastructure.Persistences.Repositories;
using Infrastructure.Services.Security;
using Infrastructure.Services.Authentication;
using Infrastructure.Services.Authorization;
using MediatR;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.EntityFrameworkCore;
using Serilog;
using Applications.Behaviors;
using BuildingBlocks.Exceptions.Handler;


var builder = WebApplication.CreateBuilder(args);

// =============================
// 📁 Load cấu hình theo môi trường
// =============================

builder.Configuration
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true, reloadOnChange: true)
    .AddEnvironmentVariables();

// =============================
// 🔧 Logging: Serilog
// =============================

builder.Host.UseSerilog((context, services, configuration) =>
{
    configuration
        .ReadFrom.Configuration(context.Configuration)
        .WriteTo.Console()  // Ghi log vào console
        .WriteTo.File("logs/ZenInvoice.log", rollingInterval: RollingInterval.Day);  // Ghi log vào file
});

// =============================
// 📦 Đăng ký các dịch vụ
// =============================

var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
builder.Services.AddExceptionHandler<CustomExceptionHandler>();
builder.Services.AddInfrastructureServices(connectionString);
builder.Services.AddRepositories();

builder.Services.AddMediatR(cfg =>
{
    cfg.RegisterServicesFromAssembly(typeof(ApplicationAssembly).Assembly);
});
builder.Services.AddAutoMapper(typeof(MappingProfile).Assembly);

// External Configuration & Services
builder.Services.AddScoped<IJwtTokenGenerator, JwtTokenGenerator>();
builder.Services.AddScoped<IPasswordHasher, PasswordHasher>();
builder.Services.AddScoped<IClientCredentialRepository, ClientCredentialRepository>();
builder.Services.AddScoped<IPartnerRepository, PartnerRepository>();
builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();

// Security services
builder.Services.AddScoped<ISecretEncryptionService, AesSecretEncryptionService>();
builder.Services.AddScoped<SecretMigrationService>();
builder.Services.AddHttpContextAccessor();
builder.Services.AddValidatorsFromAssembly(typeof(CreateClientCredentialCommand).Assembly);
builder.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
builder.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(TraceIdBehavior<,>));

// Memory Cache for MobiFone Authentication Cache Service
builder.Services.AddMemoryCache();

// Trong Program.cs hoặc ServiceRegistration.cs


builder.Services.AddScoped<ISmsLogRepository, SmsLogRepository>();
builder.Services.AddScoped<ISmsRetryQueueRepository, SmsRetryQueueRepository>();

// MobiFone Invoice Configuration & Services
builder.Services.Configure<Infrastructure.Configurations.MobiFoneInvoiceConfiguration>(
    builder.Configuration.GetSection("MobiFoneInvoice"));
builder.Services.AddHttpClient<Applications.Interfaces.Services.IMobiFoneInvoiceService, Infrastructure.Services.MobiFoneInvoiceService>(client =>
{
    client.Timeout = TimeSpan.FromSeconds(30);
    client.DefaultRequestHeaders.Add("User-Agent", "ZenInvoice/1.0");
});

// =============================
// 🔐 Authentication & Authorization Services
// =============================

// Configuration options - FULL SECURITY ENABLED
builder.Services.Configure<API.Middlewares.AuthenticationOptions>(options =>
{
    options.TokenExpirationMinutes = 120;
    options.SignatureToleranceSeconds = 300; // 5 minutes tolerance for signature timestamp
    options.EnableIpWhitelist = true; // ✅ BẬT IP Whitelist
    options.DefaultHashAlgorithm = "HMAC-SHA256";
    options.EnableUsageTracking = true; // ✅ BẬT Usage Tracking
    options.EnableTimestampValidation = true; // ✅ BẬT Timestamp Validation
    options.SkipAuthenticationPaths = [
        "/api/authentication/token",
        "/api/authentication/validate",
        "/api/authentication/revoke",
        "/api/authentication/create-test-partner",
        "/api/admin/partners",
        // "/zeninvoice/api/mobi-fone-invoice/login", // ✅ Skip JWT for login endpoint (but keep HMAC)
        // "/swagger",
        "/health",
        "/zeninvoice/api/merchant-branch-invoice-account", // ✅ Skip all authentication for MerchantBranchInvoiceAccount endpoints (kebab-case)
        "/zeninvoice/api/invoice", // ✅ Skip all authentication for Invoice endpoints
        "/zeninvoice/api/merchant-invoice-order", // ✅ Skip all authentication for MerchantInvoiceOrder endpoints
        "/zeninvoice/api/example-bearer/public" // ✅ Skip authentication for public example endpoint
    ];
});

// Authentication Services
builder.Services.AddScoped<IAuthenticationService, AuthenticationService>();
builder.Services.AddScoped<ITokenService, TokenService>();
builder.Services.AddScoped<ISignatureService, SignatureService>();
builder.Services.AddScoped<IIpWhitelistService, IpWhitelistService>();

// Authorization Services
builder.Services.AddScoped<IPermissionService, PermissionService>();
builder.Services.AddScoped<IConstraintService, ConstraintService>();
builder.Services.AddScoped<IRoleService, RoleService>();
builder.Services.AddScoped<IUsageTrackingService, UsageTrackingService>();

// Current User Service
builder.Services.AddScoped<Applications.Interfaces.Services.ICurrentUserService, Infrastructure.Services.CurrentUserService>();

// =============================
// ⚙️ Cấu hình Controller & JSON
// =============================

builder.Services.AddControllers(options =>
{
    // ✅ Đặt RouteTokenTransformer tại đây (Controller-level)
    options.Conventions.Add(new RouteTokenTransformerConvention(new SlugifyParameterTransformer()));

    // ✅ Đăng ký Tax Code Validation Action Filter
    options.Filters.Add<ValidateTaxCodeActionFilter>();
})
.AddJsonOptions(options =>
{
    // ✅ Đặt camelCase cho JSON (Property & Dictionary keys)
    options.JsonSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
    options.JsonSerializerOptions.DictionaryKeyPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
});

// =============================
// 🌐 CORS
// =============================

builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin().AllowAnyHeader().AllowAnyMethod();
    });
});

// =============================
// 🔐 JWT Authentication
// =============================

builder.Services.AddJwtAuthentication(builder.Configuration);

// =============================
// 🔐 Authorization Policies
// =============================

builder.Services.AddAuthorization(options =>
{
    // Simple Bearer Auth policy - bypass B2B authentication complexity
    options.AddPolicy("SimpleBearerAuth", policy =>
    {
        policy.Requirements.Add(new API.Authorization.SimpleBearerAuthRequirement(
            requireToken: true,
            validateExpiration: true));
    });

    // Optional Simple Bearer Auth policy - token not required
    options.AddPolicy("SimpleBearerAuthOptional", policy =>
    {
        policy.Requirements.Add(new API.Authorization.SimpleBearerAuthRequirement(
            requireToken: false,
            validateExpiration: true));
    });
});

// Register authorization handlers
builder.Services.AddScoped<IAuthorizationHandler, API.Authorization.SimpleBearerAuthHandler>();

// =============================
// 🧪 Swagger
// =============================

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    // Thêm schema xác thực dạng Bearer
    c.AddSecurityDefinition("Bearer", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
    {
        Name = "Authorization",
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.Http,
        Scheme = "bearer",
        BearerFormat = "JWT",
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Description = "Nhập token dạng: **Bearer your-token-here**"
    });

    // Thêm các headers cho HMAC authentication
    c.AddSecurityDefinition("HMAC", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
    {
        Name = "X-Signature",
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Description = "HMAC-SHA256 signature của request"
    });

    c.AddSecurityDefinition("Timestamp", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
    {
        Name = "X-Timestamp",
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Description = "Unix timestamp của request (±5 minutes validity)"
    });

    c.AddSecurityDefinition("ClientId", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
    {
        Name = "X-Client-Id",
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Description = "Client ID cho HMAC signature"
    });

    // Thêm global parameters cho MobiFone APIs
    c.OperationFilter<AddMobiFoneHeadersOperationFilter>();

    // Filter để loại bỏ Authorization cho APIs có [AllowAnonymous]
    c.OperationFilter<API.Filters.SwaggerAuthOperationFilter>();

    // Áp dụng Bearer token cho tất cả APIs (trừ những API có [AllowAnonymous])
    c.AddSecurityRequirement(new Microsoft.OpenApi.Models.OpenApiSecurityRequirement
    {
        {
            new Microsoft.OpenApi.Models.OpenApiSecurityScheme
            {
                Reference = new Microsoft.OpenApi.Models.OpenApiReference
                {
                    Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });

    // Thêm XML comments nếu có
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

var app = builder.Build();

// ✅ Đặt TraceIdMiddleware đầu tiên trong middleware pipeline
// app.UseMiddleware<TraceIdMiddleware>();  // Đây là middleware sẽ tạo TraceId cho mỗi request

// =============================
// 🔐 Authentication & Authorization Middleware Pipeline
// =============================

// Add authentication and authorization
app.UseAuthentication();
app.UseAuthorization();

// Order is important: IP → JWT → Signature → Permissions → Usage Tracking (B2B authentication)
// Note: Simple Bearer Auth is handled by Authorization Policy, not middleware
app.UseMiddleware<IpWhitelistMiddleware>();
app.UseMiddleware<JwtAuthenticationMiddleware>();
app.UseMiddleware<SignatureValidationMiddleware>();
app.UseMiddleware<PermissionAuthorizationMiddleware>();
app.UseMiddleware<UsageTrackingMiddleware>();

// =============================
// 🌱 Seed database nếu cần
// =============================

// TODO: Fix database connection issue
// await app.MigrateAndSeedAsync();

// =============================
// 🧱 Middleware pipeline
// =============================

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "API V1"));
}

// ✅ Luôn bật Swagger bất kể môi trường
app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "API V1");
});

app.UseMiddleware<ExceptionHandlingMiddleware>();
app.UseHttpsRedirection();
app.UseCors("AllowAll");
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();
app.UseMiddleware<SerilogEnrichMiddleware>();

app.Run();
