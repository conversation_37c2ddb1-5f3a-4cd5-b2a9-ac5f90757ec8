[2025-07-31 20:38:03 INF] JwtAuthenticationMiddleware checking path: /swagger/index.html
[2025-07-31 20:38:03 INF] Skipping authentication for path: /swagger/index.html (hardcoded rule)
[2025-07-31 20:38:03 INF] AES Secret Encryption Service initialized
[2025-07-31 20:38:03 INF] JwtAuthenticationMiddleware checking path: /swagger/swagger-ui.css
[2025-07-31 20:38:03 INF] JwtAuthenticationMiddleware checking path: /swagger/swagger-ui-bundle.js
[2025-07-31 20:38:03 INF] JwtAuthenticationMiddleware checking path: /swagger/index.js
[2025-07-31 20:38:03 INF] Skipping authentication for path: /swagger/swagger-ui-bundle.js (hardcoded rule)
[2025-07-31 20:38:03 INF] JwtAuthenticationMiddleware checking path: /swagger/index.css
[2025-07-31 20:38:03 INF] JwtAuthenticationMiddleware checking path: /swagger/swagger-ui-standalone-preset.js
[2025-07-31 20:38:03 INF] Skipping authentication for path: /swagger/swagger-ui.css (hardcoded rule)
[2025-07-31 20:38:03 INF] Skipping authentication for path: /swagger/index.js (hardcoded rule)
[2025-07-31 20:38:03 INF] AES Secret Encryption Service initialized
[2025-07-31 20:38:03 INF] Skipping authentication for path: /swagger/index.css (hardcoded rule)
[2025-07-31 20:38:03 INF] AES Secret Encryption Service initialized
[2025-07-31 20:38:03 INF] Skipping authentication for path: /swagger/swagger-ui-standalone-preset.js (hardcoded rule)
[2025-07-31 20:38:03 INF] AES Secret Encryption Service initialized
[2025-07-31 20:38:03 INF] AES Secret Encryption Service initialized
[2025-07-31 20:38:03 INF] AES Secret Encryption Service initialized
[2025-07-31 20:38:04 INF] JwtAuthenticationMiddleware checking path: /swagger/favicon-32x32.png
[2025-07-31 20:38:04 INF] Skipping authentication for path: /swagger/favicon-32x32.png (hardcoded rule)
[2025-07-31 20:38:04 INF] AES Secret Encryption Service initialized
[2025-07-31 20:38:04 INF] JwtAuthenticationMiddleware checking path: /swagger/v1/swagger.json
[2025-07-31 20:38:04 INF] Skipping authentication for path: /swagger/v1/swagger.json (hardcoded rule)
[2025-07-31 20:38:04 INF] AES Secret Encryption Service initialized
[2025-07-31 20:38:41 INF] JwtAuthenticationMiddleware checking path: /api/authentication/token
[2025-07-31 20:38:41 INF] Skipping authentication for path: /api/authentication/token (matched: /api/authentication/token)
[2025-07-31 20:38:41 INF] AES Secret Encryption Service initialized
[2025-07-31 20:38:41 WRN] Failed to determine the https port for redirect.
[2025-07-31 20:38:41 INF] Token request from client zenshop_client, IP 127.0.0.1
[2025-07-31 20:38:41 INF] Authentication attempt for client zenshop_client from 127.0.0.1
[2025-07-31 20:38:42 INF] Generating token for partner "55555555-5555-5555-5555-555555555552"
[2025-07-31 20:38:42 INF] Authentication successful for client zenshop_client
[2025-07-31 20:38:42 INF] Token issued successfully for client zenshop_client
