[2025-08-01 10:04:54 INF] JwtAuthenticationMiddleware checking path: /swagger
[2025-08-01 10:04:55 INF] Skipping authentication for path: /swagger (hardcoded rule)
[2025-08-01 10:04:55 INF] AES Secret Encryption Service initialized
[2025-08-01 10:04:55 INF] JwtAuthenticationMiddleware checking path: /swagger/index.html
[2025-08-01 10:04:55 INF] Skipping authentication for path: /swagger/index.html (hardcoded rule)
[2025-08-01 10:04:55 INF] AES Secret Encryption Service initialized
[2025-08-01 10:04:55 INF] JwtAuthenticationMiddleware checking path: /swagger/swagger-ui.css
[2025-08-01 10:04:55 INF] JwtAuthenticationMiddleware checking path: /swagger/swagger-ui-standalone-preset.js
[2025-08-01 10:04:55 INF] JwtAuthenticationMiddleware checking path: /swagger/swagger-ui-bundle.js
[2025-08-01 10:04:55 INF] JwtAuthenticationMiddleware checking path: /swagger/index.css
[2025-08-01 10:04:55 INF] Skipping authentication for path: /swagger/swagger-ui-bundle.js (hardcoded rule)
[2025-08-01 10:04:55 INF] Skipping authentication for path: /swagger/swagger-ui.css (hardcoded rule)
[2025-08-01 10:04:55 INF] Skipping authentication for path: /swagger/index.css (hardcoded rule)
[2025-08-01 10:04:55 INF] JwtAuthenticationMiddleware checking path: /swagger/index.js
[2025-08-01 10:04:55 INF] Skipping authentication for path: /swagger/swagger-ui-standalone-preset.js (hardcoded rule)
[2025-08-01 10:04:55 INF] Skipping authentication for path: /swagger/index.js (hardcoded rule)
[2025-08-01 10:04:55 INF] AES Secret Encryption Service initialized
[2025-08-01 10:04:55 INF] AES Secret Encryption Service initialized
[2025-08-01 10:04:55 INF] AES Secret Encryption Service initialized
[2025-08-01 10:04:55 INF] AES Secret Encryption Service initialized
[2025-08-01 10:04:55 INF] AES Secret Encryption Service initialized
[2025-08-01 10:04:55 INF] JwtAuthenticationMiddleware checking path: /swagger/v1/swagger.json
[2025-08-01 10:04:55 INF] Skipping authentication for path: /swagger/v1/swagger.json (hardcoded rule)
[2025-08-01 10:04:55 INF] AES Secret Encryption Service initialized
[2025-08-01 10:04:55 INF] JwtAuthenticationMiddleware checking path: /swagger/favicon-32x32.png
[2025-08-01 10:04:55 INF] Skipping authentication for path: /swagger/favicon-32x32.png (hardcoded rule)
[2025-08-01 10:04:55 INF] AES Secret Encryption Service initialized
[2025-08-01 10:05:07 INF] JwtAuthenticationMiddleware checking path: //api/authentication/token
[2025-08-01 10:05:07 INF] Skipping authentication for path: //api/authentication/token (matched: /api/authentication/token)
[2025-08-01 10:05:07 INF] AES Secret Encryption Service initialized
[2025-08-01 10:05:07 WRN] Failed to determine the https port for redirect.
[2025-08-01 10:05:17 INF] JwtAuthenticationMiddleware checking path: /api/authentication/token
[2025-08-01 10:05:17 INF] Skipping authentication for path: /api/authentication/token (matched: /api/authentication/token)
[2025-08-01 10:05:17 INF] AES Secret Encryption Service initialized
[2025-08-01 10:05:17 WRN] Invalid token request from ::1
[2025-08-01 10:05:30 INF] JwtAuthenticationMiddleware checking path: /api/authentication/token
[2025-08-01 10:05:30 INF] Skipping authentication for path: /api/authentication/token (matched: /api/authentication/token)
[2025-08-01 10:05:30 INF] AES Secret Encryption Service initialized
[2025-08-01 10:05:30 INF] Token request from client zenshop_client, IP ::1
[2025-08-01 10:05:30 INF] Authentication attempt for client zenshop_client from ::1
[2025-08-01 10:05:31 INF] Generating token for partner "55555555-5555-5555-5555-555555555552"
[2025-08-01 10:05:31 INF] Authentication successful for client zenshop_client
[2025-08-01 10:05:31 INF] Token issued successfully for client zenshop_client
[2025-08-01 10:05:36 INF] JwtAuthenticationMiddleware checking path: //api/authentication/token
[2025-08-01 10:05:36 INF] Skipping authentication for path: //api/authentication/token (matched: /api/authentication/token)
[2025-08-01 10:05:36 INF] AES Secret Encryption Service initialized
[2025-08-01 10:05:51 INF] JwtAuthenticationMiddleware checking path: //api/authentication/token
[2025-08-01 10:05:51 INF] Skipping authentication for path: //api/authentication/token (matched: /api/authentication/token)
[2025-08-01 10:05:51 INF] AES Secret Encryption Service initialized
[2025-08-01 10:06:26 INF] JwtAuthenticationMiddleware checking path: /api/authentication/token
[2025-08-01 10:06:26 INF] Skipping authentication for path: /api/authentication/token (matched: /api/authentication/token)
[2025-08-01 10:06:26 INF] AES Secret Encryption Service initialized
[2025-08-01 10:06:26 INF] Token request from client zenshop_client, IP ::1
[2025-08-01 10:06:26 INF] Authentication attempt for client zenshop_client from ::1
[2025-08-01 10:06:26 INF] Generating token for partner "55555555-5555-5555-5555-555555555552"
[2025-08-01 10:06:26 INF] Authentication successful for client zenshop_client
[2025-08-01 10:06:26 INF] Token issued successfully for client zenshop_client
[2025-08-01 10:10:13 INF] IP validation prepared for ::1 on /zenInvoice/api/mobi-fone-invoice/data-references
[2025-08-01 10:10:13 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/mobi-fone-invoice/data-references
[2025-08-01 10:10:13 INF] Validating JWT token
[2025-08-01 10:10:13 INF] Token extracted, length: 379
[2025-08-01 10:10:13 INF] Validating JWT token
[2025-08-01 10:10:13 INF] Token validated successfully for partner "55555555-5555-5555-5555-555555555552"
[2025-08-01 10:10:13 INF] Token validation result: IsValid=true, ErrorMessage=null
[2025-08-01 10:10:13 INF] Token validated successfully for partner ZenShop System
[2025-08-01 10:10:13 INF] Extracting claims from JWT token
[2025-08-01 10:10:13 INF] Claims extracted successfully for partner "55555555-5555-5555-5555-555555555552"
[2025-08-01 10:10:13 INF] Extracting claims from JWT token
[2025-08-01 10:10:13 INF] Partner ID extracted successfully: "55555555-5555-5555-5555-555555555552"
[2025-08-01 10:10:13 INF] Claims extracted successfully for partner "55555555-5555-5555-5555-555555555552"
[2025-08-01 10:10:13 INF] Partner ID extracted successfully: "55555555-5555-5555-5555-555555555552"
[2025-08-01 10:10:13 INF] Checking IP whitelist for partner "55555555-5555-5555-5555-555555555552", IP ::1
[2025-08-01 10:10:13 INF] Checking IP whitelist for partner "55555555-5555-5555-5555-555555555552", IP ::1
[2025-08-01 10:10:13 INF] IP whitelist disabled for partner "55555555-5555-5555-5555-555555555552"
[2025-08-01 10:10:13 INF] IP whitelist validation passed for partner "55555555-5555-5555-5555-555555555552"
[2025-08-01 10:10:13 INF] Partner "55555555-5555-5555-5555-555555555552" authenticated successfully for /zenInvoice/api/mobi-fone-invoice/data-references
[2025-08-01 10:10:13 INF] AES Secret Encryption Service initialized
[2025-08-01 10:10:13 INF] Validating timestamp 1753838720
[2025-08-01 10:10:13 WRN] Invalid timestamp 1753838720 from client zenshop_client
[2025-08-01 10:12:20 INF] IP validation prepared for ::1 on /zenInvoice/api/mobi-fone-invoice/save-and-sign-hoadon78
[2025-08-01 10:12:20 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/mobi-fone-invoice/save-and-sign-hoadon78
[2025-08-01 10:12:20 INF] Validating JWT token
[2025-08-01 10:12:20 INF] Token extracted, length: 379
[2025-08-01 10:12:20 INF] Validating JWT token
[2025-08-01 10:12:20 INF] Token validated successfully for partner "55555555-5555-5555-5555-555555555552"
[2025-08-01 10:12:20 INF] Token validation result: IsValid=true, ErrorMessage=null
[2025-08-01 10:12:20 INF] Token validated successfully for partner ZenShop System
[2025-08-01 10:12:20 INF] Extracting claims from JWT token
[2025-08-01 10:12:20 INF] Claims extracted successfully for partner "55555555-5555-5555-5555-555555555552"
[2025-08-01 10:12:20 INF] Extracting claims from JWT token
[2025-08-01 10:12:20 INF] Partner ID extracted successfully: "55555555-5555-5555-5555-555555555552"
[2025-08-01 10:12:20 INF] Claims extracted successfully for partner "55555555-5555-5555-5555-555555555552"
[2025-08-01 10:12:20 INF] Partner ID extracted successfully: "55555555-5555-5555-5555-555555555552"
[2025-08-01 10:12:20 INF] Checking IP whitelist for partner "55555555-5555-5555-5555-555555555552", IP ::1
[2025-08-01 10:12:20 INF] Checking IP whitelist for partner "55555555-5555-5555-5555-555555555552", IP ::1
[2025-08-01 10:12:20 INF] IP whitelist disabled for partner "55555555-5555-5555-5555-555555555552"
[2025-08-01 10:12:20 INF] IP whitelist validation passed for partner "55555555-5555-5555-5555-555555555552"
[2025-08-01 10:12:20 INF] Partner "55555555-5555-5555-5555-555555555552" authenticated successfully for /zenInvoice/api/mobi-fone-invoice/save-and-sign-hoadon78
[2025-08-01 10:12:20 INF] AES Secret Encryption Service initialized
[2025-08-01 10:12:20 INF] Validating timestamp 1754017902
[2025-08-01 10:12:20 INF] Validating HMAC signature for partner "55555555-5555-5555-5555-555555555552"
[2025-08-01 10:12:20 INF] Generating HMAC signature for POST /zenInvoice/api/mobi-fone-invoice/save-and-sign-hoadon78
[2025-08-01 10:12:20 INF] Signature generated successfully
[2025-08-01 10:12:20 INF] Signature validation successful for partner "55555555-5555-5555-5555-555555555552"
[2025-08-01 10:12:20 INF] Signature validated successfully for partner "55555555-5555-5555-5555-555555555552"
[2025-08-01 10:12:20 INF] Save and sign invoice with auto authentication for TaxCode: 0123456789
[2025-08-01 10:12:20 ERR] Insufficient quota for TaxCode: 0123456789. Requested: 1, Available: 0
[2025-08-01 10:12:20 INF] Tracking API call for partner "55555555-5555-5555-5555-555555555552", operation invoice_create, success true, time 127.1998ms
[2025-08-01 10:23:41 ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5119: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
