[2025-08-05 18:13:52 WRN] The 'MerchantInvoiceOrderStatus' property 'Status' on entity type 'MerchantInvoiceOrder' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value '0', since this is the CLR default for the 'MerchantInvoiceOrderStatus' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
[2025-08-05 19:51:31 INF] JwtAuthenticationMiddleware checking path: /swagger/v1/swagger.json
[2025-08-05 19:51:31 INF] Skipping authentication for path: /swagger/v1/swagger.json (hardcoded rule)
[2025-08-05 19:51:31 INF] AES Secret Encryption Service initialized
[2025-08-05 19:53:28 INF] JwtAuthenticationMiddleware checking path: /swagger/index.html
[2025-08-05 19:53:28 INF] Skipping authentication for path: /swagger/index.html (hardcoded rule)
[2025-08-05 19:53:28 INF] AES Secret Encryption Service initialized
[2025-08-05 19:53:28 INF] JwtAuthenticationMiddleware checking path: /swagger/v1/swagger.json
[2025-08-05 19:53:28 INF] Skipping authentication for path: /swagger/v1/swagger.json (hardcoded rule)
[2025-08-05 19:53:28 INF] AES Secret Encryption Service initialized
[2025-08-05 19:57:48 INF] JwtAuthenticationMiddleware checking path: /swagger/index.html
[2025-08-05 19:57:48 INF] Skipping authentication for path: /swagger/index.html (hardcoded rule)
[2025-08-05 19:57:48 INF] AES Secret Encryption Service initialized
[2025-08-05 19:57:49 INF] JwtAuthenticationMiddleware checking path: /swagger/v1/swagger.json
[2025-08-05 19:57:49 INF] Skipping authentication for path: /swagger/v1/swagger.json (hardcoded rule)
[2025-08-05 19:57:49 INF] AES Secret Encryption Service initialized
[2025-08-05 20:59:42 WRN] Missing or invalid Authorization header for /zenInvoice/api/merchant-invoice-order
[2025-08-05 20:59:58 WRN] Missing or invalid Authorization header for /zenInvoice/api/merchant-invoice-order
[2025-08-05 21:00:04 INF] Validating JWT token
[2025-08-05 21:00:04 INF] Token validated successfully for partner "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:00:04 INF] Extracting claims from JWT token
[2025-08-05 21:00:04 INF] Claims extracted successfully for partner "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:00:04 INF] Partner ID extracted successfully: "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:00:04 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/merchant-invoice-order
[2025-08-05 21:00:04 INF] Skipping authentication for path: /zeninvoice/api/merchant-invoice-order (matched: /zeninvoice/api/merchant-invoice-order)
[2025-08-05 21:00:04 INF] AES Secret Encryption Service initialized
[2025-08-05 21:00:04 WRN] Failed to determine the https port for redirect.
[2025-08-05 21:00:04 INF] Validating JWT token
[2025-08-05 21:00:04 INF] Token validated successfully for partner "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:00:04 INF] Extracting claims from JWT token
[2025-08-05 21:00:04 INF] Claims extracted successfully for partner "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:00:04 INF] Partner ID extracted successfully: "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:00:04 INF] Creating merchant invoice order for MerchantBranchId: "3fa85f64-5717-4562-b3fc-2c963f66afa6"
[2025-08-05 21:00:04 INF] Tracking API call for partner "55555555-5555-5555-5555-555555555552", operation invoice_create, success true, time 97.3879ms
[2025-08-05 21:00:39 INF] Validating JWT token
[2025-08-05 21:00:39 INF] Token validated successfully for partner "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:00:39 INF] Extracting claims from JWT token
[2025-08-05 21:00:39 INF] Claims extracted successfully for partner "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:00:39 INF] Partner ID extracted successfully: "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:00:39 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/merchant-invoice-order
[2025-08-05 21:00:39 INF] Skipping authentication for path: /zeninvoice/api/merchant-invoice-order (matched: /zeninvoice/api/merchant-invoice-order)
[2025-08-05 21:00:39 INF] AES Secret Encryption Service initialized
[2025-08-05 21:00:39 INF] Validating JWT token
[2025-08-05 21:00:39 INF] Token validated successfully for partner "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:00:39 INF] Extracting claims from JWT token
[2025-08-05 21:00:39 INF] Claims extracted successfully for partner "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:00:39 INF] Partner ID extracted successfully: "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:00:39 INF] Creating merchant invoice order for MerchantBranchId: "da8ce038-11bd-4d8d-90c5-6cd62f0fd437"
[2025-08-05 21:00:39 INF] Creating merchant invoice order for MerchantBranchId: "da8ce038-11bd-4d8d-90c5-6cd62f0fd437"
[2025-08-05 21:00:39 WRN] MerchantBranchId "da8ce038-11bd-4d8d-90c5-6cd62f0fd437" already has an active order "00293002-e05e-4da9-885b-38c38ddc3b8d" valid until "2025-08-29T22:09:03.9500000Z"
[2025-08-05 21:00:39 INF] Tracking API call for partner "55555555-5555-5555-5555-555555555552", operation invoice_create, success true, time 128.2555ms
[2025-08-05 21:00:59 INF] Validating JWT token
[2025-08-05 21:00:59 INF] Token validated successfully for partner "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:00:59 INF] Extracting claims from JWT token
[2025-08-05 21:00:59 INF] Claims extracted successfully for partner "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:00:59 INF] Partner ID extracted successfully: "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:00:59 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/merchant-invoice-order
[2025-08-05 21:00:59 INF] Skipping authentication for path: /zeninvoice/api/merchant-invoice-order (matched: /zeninvoice/api/merchant-invoice-order)
[2025-08-05 21:00:59 INF] AES Secret Encryption Service initialized
[2025-08-05 21:00:59 INF] Validating JWT token
[2025-08-05 21:00:59 INF] Token validated successfully for partner "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:00:59 INF] Extracting claims from JWT token
[2025-08-05 21:00:59 INF] Claims extracted successfully for partner "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:00:59 INF] Partner ID extracted successfully: "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:00:59 INF] Creating merchant invoice order for MerchantBranchId: "da8ce038-11bd-4d8d-90c5-6cd62f0fd437"
[2025-08-05 21:00:59 INF] Creating merchant invoice order for MerchantBranchId: "da8ce038-11bd-4d8d-90c5-6cd62f0fd437"
[2025-08-05 21:01:00 INF] Successfully created merchant invoice order with Id: "01987a89-1149-7455-bd32-73061c294ef6" for MerchantBranchId: "da8ce038-11bd-4d8d-90c5-6cd62f0fd437"
[2025-08-05 21:01:00 INF] Tracking API call for partner "55555555-5555-5555-5555-555555555552", operation invoice_create, success true, time 174.8905ms
[2025-08-05 21:01:59 WRN] Missing or invalid Authorization header for /zenInvoice/api/merchant-invoice-order/da8ce038-11bd-4d8d-90c5-6cd62f0fd437/confirm
[2025-08-05 21:02:33 INF] Validating JWT token
[2025-08-05 21:02:34 INF] Token validated successfully for partner "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:02:34 INF] Extracting claims from JWT token
[2025-08-05 21:02:34 INF] Claims extracted successfully for partner "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:02:34 INF] Partner ID extracted successfully: "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:02:34 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/merchant-invoice-order/01987a89-1149-7455-bd32-73061c294ef6/confirm
[2025-08-05 21:02:34 INF] Skipping authentication for path: /zeninvoice/api/merchant-invoice-order/01987a89-1149-7455-bd32-73061c294ef6/confirm (matched: /zeninvoice/api/merchant-invoice-order)
[2025-08-05 21:02:34 INF] AES Secret Encryption Service initialized
[2025-08-05 21:02:34 INF] Validating JWT token
[2025-08-05 21:02:34 INF] Token validated successfully for partner "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:02:34 INF] Extracting claims from JWT token
[2025-08-05 21:02:34 INF] Claims extracted successfully for partner "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:02:34 INF] Partner ID extracted successfully: "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:02:34 INF] Confirming MerchantInvoiceOrder with Id: "01987a89-1149-7455-bd32-73061c294ef6"
[2025-08-05 21:02:34 INF] Confirming merchant invoice order with Id: "01987a89-1149-7455-bd32-73061c294ef6"
[2025-08-05 21:02:34 INF] Successfully confirmed merchant invoice order with Id: "01987a89-1149-7455-bd32-73061c294ef6" for partner "55555555-5555-5555-5555-555555555552"
[2025-08-05 21:02:34 INF] Tracking API call for partner "55555555-5555-5555-5555-555555555552", operation patch_01987a89-1149-7455-bd32-73061c294ef6, success true, time 74.3555ms
