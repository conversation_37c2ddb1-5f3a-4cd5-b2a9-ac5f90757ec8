[2025-08-05 18:13:52 WRN] The 'MerchantInvoiceOrderStatus' property 'Status' on entity type 'MerchantInvoiceOrder' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value '0', since this is the CLR default for the 'MerchantInvoiceOrderStatus' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
