[2025-08-05 18:13:52 WRN] The 'MerchantInvoiceOrderStatus' property 'Status' on entity type 'MerchantInvoiceOrder' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value '0', since this is the CLR default for the 'MerchantInvoiceOrderStatus' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
[2025-08-05 19:51:31 INF] JwtAuthenticationMiddleware checking path: /swagger/v1/swagger.json
[2025-08-05 19:51:31 INF] Skipping authentication for path: /swagger/v1/swagger.json (hardcoded rule)
[2025-08-05 19:51:31 INF] AES Secret Encryption Service initialized
[2025-08-05 19:53:28 INF] JwtAuthenticationMiddleware checking path: /swagger/index.html
[2025-08-05 19:53:28 INF] Skipping authentication for path: /swagger/index.html (hardcoded rule)
[2025-08-05 19:53:28 INF] AES Secret Encryption Service initialized
[2025-08-05 19:53:28 INF] JwtAuthenticationMiddleware checking path: /swagger/v1/swagger.json
[2025-08-05 19:53:28 INF] Skipping authentication for path: /swagger/v1/swagger.json (hardcoded rule)
[2025-08-05 19:53:28 INF] AES Secret Encryption Service initialized
