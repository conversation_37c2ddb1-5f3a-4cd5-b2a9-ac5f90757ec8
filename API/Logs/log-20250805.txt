[2025-08-05 18:13:52 WRN] The 'MerchantInvoiceOrderStatus' property 'Status' on entity type 'MerchantInvoiceOrder' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value '0', since this is the CLR default for the 'MerchantInvoiceOrderStatus' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
[2025-08-05 19:51:31 INF] JwtAuthenticationMiddleware checking path: /swagger/v1/swagger.json
[2025-08-05 19:51:31 INF] Skipping authentication for path: /swagger/v1/swagger.json (hardcoded rule)
[2025-08-05 19:51:31 INF] AES Secret Encryption Service initialized
[2025-08-05 19:53:28 INF] JwtAuthenticationMiddleware checking path: /swagger/index.html
[2025-08-05 19:53:28 INF] Skipping authentication for path: /swagger/index.html (hardcoded rule)
[2025-08-05 19:53:28 INF] AES Secret Encryption Service initialized
[2025-08-05 19:53:28 INF] JwtAuthenticationMiddleware checking path: /swagger/v1/swagger.json
[2025-08-05 19:53:28 INF] Skipping authentication for path: /swagger/v1/swagger.json (hardcoded rule)
[2025-08-05 19:53:28 INF] AES Secret Encryption Service initialized
[2025-08-05 19:57:48 INF] JwtAuthenticationMiddleware checking path: /swagger/index.html
[2025-08-05 19:57:48 INF] Skipping authentication for path: /swagger/index.html (hardcoded rule)
[2025-08-05 19:57:48 INF] AES Secret Encryption Service initialized
[2025-08-05 19:57:49 INF] JwtAuthenticationMiddleware checking path: /swagger/v1/swagger.json
[2025-08-05 19:57:49 INF] Skipping authentication for path: /swagger/v1/swagger.json (hardcoded rule)
[2025-08-05 19:57:49 INF] AES Secret Encryption Service initialized
[2025-08-05 20:59:42 WRN] Missing or invalid Authorization header for /zenInvoice/api/merchant-invoice-order
[2025-08-05 20:59:58 WRN] Missing or invalid Authorization header for /zenInvoice/api/merchant-invoice-order
[2025-08-05 21:00:04 INF] Validating JWT token
[2025-08-05 21:00:04 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:00:04 INF] Extracting claims from JWT token
[2025-08-05 21:00:04 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:00:04 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:00:04 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/merchant-invoice-order
[2025-08-05 21:00:04 INF] Skipping authentication for path: /zeninvoice/api/merchant-invoice-order (matched: /zeninvoice/api/merchant-invoice-order)
[2025-08-05 21:00:04 INF] AES Secret Encryption Service initialized
[2025-08-05 21:00:04 WRN] Failed to determine the https port for redirect.
[2025-08-05 21:00:04 INF] Validating JWT token
[2025-08-05 21:00:04 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:00:04 INF] Extracting claims from JWT token
[2025-08-05 21:00:04 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:00:04 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:00:04 INF] Creating merchant invoice order for MerchantBranchId: "3fa85f64-5717-4562-b3fc-2c963f66afa6"
[2025-08-05 21:00:04 INF] Tracking API call for partner "********-5555-5555-5555-********5552", operation invoice_create, success true, time 97.3879ms
[2025-08-05 21:00:39 INF] Validating JWT token
[2025-08-05 21:00:39 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:00:39 INF] Extracting claims from JWT token
[2025-08-05 21:00:39 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:00:39 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:00:39 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/merchant-invoice-order
[2025-08-05 21:00:39 INF] Skipping authentication for path: /zeninvoice/api/merchant-invoice-order (matched: /zeninvoice/api/merchant-invoice-order)
[2025-08-05 21:00:39 INF] AES Secret Encryption Service initialized
[2025-08-05 21:00:39 INF] Validating JWT token
[2025-08-05 21:00:39 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:00:39 INF] Extracting claims from JWT token
[2025-08-05 21:00:39 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:00:39 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:00:39 INF] Creating merchant invoice order for MerchantBranchId: "da8ce038-11bd-4d8d-90c5-6cd62f0fd437"
[2025-08-05 21:00:39 INF] Creating merchant invoice order for MerchantBranchId: "da8ce038-11bd-4d8d-90c5-6cd62f0fd437"
[2025-08-05 21:00:39 WRN] MerchantBranchId "da8ce038-11bd-4d8d-90c5-6cd62f0fd437" already has an active order "00293002-e05e-4da9-885b-38c38ddc3b8d" valid until "2025-08-29T22:09:03.9500000Z"
[2025-08-05 21:00:39 INF] Tracking API call for partner "********-5555-5555-5555-********5552", operation invoice_create, success true, time 128.2555ms
[2025-08-05 21:00:59 INF] Validating JWT token
[2025-08-05 21:00:59 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:00:59 INF] Extracting claims from JWT token
[2025-08-05 21:00:59 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:00:59 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:00:59 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/merchant-invoice-order
[2025-08-05 21:00:59 INF] Skipping authentication for path: /zeninvoice/api/merchant-invoice-order (matched: /zeninvoice/api/merchant-invoice-order)
[2025-08-05 21:00:59 INF] AES Secret Encryption Service initialized
[2025-08-05 21:00:59 INF] Validating JWT token
[2025-08-05 21:00:59 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:00:59 INF] Extracting claims from JWT token
[2025-08-05 21:00:59 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:00:59 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:00:59 INF] Creating merchant invoice order for MerchantBranchId: "da8ce038-11bd-4d8d-90c5-6cd62f0fd437"
[2025-08-05 21:00:59 INF] Creating merchant invoice order for MerchantBranchId: "da8ce038-11bd-4d8d-90c5-6cd62f0fd437"
[2025-08-05 21:01:00 INF] Successfully created merchant invoice order with Id: "01987a89-1149-7455-bd32-73061c294ef6" for MerchantBranchId: "da8ce038-11bd-4d8d-90c5-6cd62f0fd437"
[2025-08-05 21:01:00 INF] Tracking API call for partner "********-5555-5555-5555-********5552", operation invoice_create, success true, time 174.8905ms
[2025-08-05 21:01:59 WRN] Missing or invalid Authorization header for /zenInvoice/api/merchant-invoice-order/da8ce038-11bd-4d8d-90c5-6cd62f0fd437/confirm
[2025-08-05 21:02:33 INF] Validating JWT token
[2025-08-05 21:02:34 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:02:34 INF] Extracting claims from JWT token
[2025-08-05 21:02:34 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:02:34 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:02:34 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/merchant-invoice-order/01987a89-1149-7455-bd32-73061c294ef6/confirm
[2025-08-05 21:02:34 INF] Skipping authentication for path: /zeninvoice/api/merchant-invoice-order/01987a89-1149-7455-bd32-73061c294ef6/confirm (matched: /zeninvoice/api/merchant-invoice-order)
[2025-08-05 21:02:34 INF] AES Secret Encryption Service initialized
[2025-08-05 21:02:34 INF] Validating JWT token
[2025-08-05 21:02:34 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:02:34 INF] Extracting claims from JWT token
[2025-08-05 21:02:34 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:02:34 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:02:34 INF] Confirming MerchantInvoiceOrder with Id: "01987a89-1149-7455-bd32-73061c294ef6"
[2025-08-05 21:02:34 INF] Confirming merchant invoice order with Id: "01987a89-1149-7455-bd32-73061c294ef6"
[2025-08-05 21:02:34 INF] Successfully confirmed merchant invoice order with Id: "01987a89-1149-7455-bd32-73061c294ef6" for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:02:34 INF] Tracking API call for partner "********-5555-5555-5555-********5552", operation patch_01987a89-1149-7455-bd32-73061c294ef6, success true, time 74.3555ms
[2025-08-05 21:35:56 INF] JwtAuthenticationMiddleware checking path: /swagger/index.html
[2025-08-05 21:35:56 INF] Skipping authentication for path: /swagger/index.html (hardcoded rule)
[2025-08-05 21:35:56 INF] AES Secret Encryption Service initialized
[2025-08-05 21:35:57 INF] JwtAuthenticationMiddleware checking path: /swagger/v1/swagger.json
[2025-08-05 21:35:57 INF] Skipping authentication for path: /swagger/v1/swagger.json (hardcoded rule)
[2025-08-05 21:35:57 INF] AES Secret Encryption Service initialized
[2025-08-05 21:35:57 INF] JwtAuthenticationMiddleware checking path: /swagger/index.html
[2025-08-05 21:35:57 INF] Skipping authentication for path: /swagger/index.html (hardcoded rule)
[2025-08-05 21:35:57 INF] AES Secret Encryption Service initialized
[2025-08-05 21:35:57 INF] JwtAuthenticationMiddleware checking path: /swagger/v1/swagger.json
[2025-08-05 21:35:57 INF] Skipping authentication for path: /swagger/v1/swagger.json (hardcoded rule)
[2025-08-05 21:35:57 INF] AES Secret Encryption Service initialized
[2025-08-05 21:35:58 INF] JwtAuthenticationMiddleware checking path: /swagger/index.html
[2025-08-05 21:35:58 INF] Skipping authentication for path: /swagger/index.html (hardcoded rule)
[2025-08-05 21:35:58 INF] AES Secret Encryption Service initialized
[2025-08-05 21:35:58 INF] JwtAuthenticationMiddleware checking path: /swagger/v1/swagger.json
[2025-08-05 21:35:58 INF] Skipping authentication for path: /swagger/v1/swagger.json (hardcoded rule)
[2025-08-05 21:35:58 INF] AES Secret Encryption Service initialized
[2025-08-05 21:37:10 INF] Validating JWT token
[2025-08-05 21:37:11 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:37:11 INF] Extracting claims from JWT token
[2025-08-05 21:37:11 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:37:11 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:37:11 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/merchant-invoice-order
[2025-08-05 21:37:11 INF] Skipping authentication for path: /zeninvoice/api/merchant-invoice-order (matched: /zeninvoice/api/merchant-invoice-order)
[2025-08-05 21:37:11 INF] AES Secret Encryption Service initialized
[2025-08-05 21:37:11 WRN] Failed to determine the https port for redirect.
[2025-08-05 21:37:11 INF] Validating JWT token
[2025-08-05 21:37:11 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:37:11 INF] Extracting claims from JWT token
[2025-08-05 21:37:11 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:37:11 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:37:11 INF] Creating merchant invoice order for MerchantBranchId: null
[2025-08-05 21:37:11 INF] Creating merchant invoice order for MerchantBranchId: null
[2025-08-05 21:37:11 WRN] MerchantBranchId null not found for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:37:11 INF] Tracking API call for partner "********-5555-5555-5555-********5552", operation invoice_create, success true, time 135.2791ms
[2025-08-05 21:38:40 INF] Validating JWT token
[2025-08-05 21:38:40 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:38:40 INF] Extracting claims from JWT token
[2025-08-05 21:38:40 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:38:41 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:38:41 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/merchant-invoice-order
[2025-08-05 21:38:41 INF] Skipping authentication for path: /zeninvoice/api/merchant-invoice-order (matched: /zeninvoice/api/merchant-invoice-order)
[2025-08-05 21:38:41 INF] AES Secret Encryption Service initialized
[2025-08-05 21:38:41 WRN] Failed to determine the https port for redirect.
[2025-08-05 21:38:41 INF] Validating JWT token
[2025-08-05 21:38:41 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:38:41 INF] Extracting claims from JWT token
[2025-08-05 21:38:41 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:38:41 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:38:41 INF] Creating merchant invoice order for MerchantBranchId: null
[2025-08-05 21:38:41 INF] Creating merchant invoice order for MerchantBranchId: null
[2025-08-05 21:38:41 INF] Successfully created merchant invoice order with Id: "01987aab-9207-75dd-a426-2009da7bab3f" for MerchantBranchId: null
[2025-08-05 21:38:41 INF] Tracking API call for partner "********-5555-5555-5555-********5552", operation invoice_create, success true, time 232.5932ms
[2025-08-05 21:38:59 WRN] Missing or invalid Authorization header for /zenInvoice/api/merchant-invoice-order
[2025-08-05 21:39:28 WRN] Missing or invalid Authorization header for /zenInvoice/api/merchant-invoice-order/01987aab-9207-75dd-a426-2009da7bab3f/create-account-and-confirm
[2025-08-05 21:40:49 INF] Validating JWT token
[2025-08-05 21:40:49 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:40:49 INF] Extracting claims from JWT token
[2025-08-05 21:40:49 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:40:49 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:40:49 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/merchant-invoice-order/01987aab-9207-75dd-a426-2009da7bab3f/create-account-and-confirm
[2025-08-05 21:40:49 INF] Skipping authentication for path: /zeninvoice/api/merchant-invoice-order/01987aab-9207-75dd-a426-2009da7bab3f/create-account-and-confirm (matched: /zeninvoice/api/merchant-invoice-order)
[2025-08-05 21:40:49 INF] AES Secret Encryption Service initialized
[2025-08-05 21:40:49 INF] Validating JWT token
[2025-08-05 21:40:49 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:40:49 INF] Extracting claims from JWT token
[2025-08-05 21:40:49 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:40:49 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:40:49 INF] Creating account and confirming MerchantInvoiceOrder with Id: "01987aab-9207-75dd-a426-2009da7bab3f"
[2025-08-05 21:40:49 INF] Processing create account and confirm order for OrderId: "01987aab-9207-75dd-a426-2009da7bab3f"
[2025-08-05 21:40:50 INF] Successfully created account "6c5d6c44-92f3-4b46-9b7b-8402b18cf46b" and confirmed order "01987aab-9207-75dd-a426-2009da7bab3f" for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:40:50 INF] Successfully created account and confirmed MerchantInvoiceOrder with Id: "01987aab-9207-75dd-a426-2009da7bab3f"
[2025-08-05 21:40:50 INF] Tracking API call for partner "********-5555-5555-5555-********5552", operation invoice_create, success true, time 173.3416ms
[2025-08-05 21:44:59 INF] JwtAuthenticationMiddleware checking path: /swagger/index.html
[2025-08-05 21:44:59 INF] Skipping authentication for path: /swagger/index.html (hardcoded rule)
[2025-08-05 21:44:59 INF] AES Secret Encryption Service initialized
[2025-08-05 21:44:59 INF] JwtAuthenticationMiddleware checking path: /swagger/v1/swagger.json
[2025-08-05 21:44:59 INF] Skipping authentication for path: /swagger/v1/swagger.json (hardcoded rule)
[2025-08-05 21:44:59 INF] AES Secret Encryption Service initialized
[2025-08-05 21:45:16 WRN] Missing or invalid Authorization header for /zenInvoice/api/merchant-invoice-order/3fa85f64-5717-4562-b3fc-2c963f66afa6/create-account-and-confirm
[2025-08-05 21:46:45 WRN] Missing or invalid Authorization header for /zenInvoice/api/merchant-invoice-order
[2025-08-05 21:46:56 WRN] Missing or invalid Authorization header for /zenInvoice/api/merchant-invoice-order
[2025-08-05 21:46:56 WRN] Missing or invalid Authorization header for /zenInvoice/api/merchant-invoice-order
[2025-08-05 21:46:57 WRN] Missing or invalid Authorization header for /zenInvoice/api/merchant-invoice-order
[2025-08-05 21:46:57 WRN] Missing or invalid Authorization header for /zenInvoice/api/merchant-invoice-order
[2025-08-05 21:46:57 WRN] Missing or invalid Authorization header for /zenInvoice/api/merchant-invoice-order
[2025-08-05 21:46:57 WRN] Missing or invalid Authorization header for /zenInvoice/api/merchant-invoice-order
[2025-08-05 21:46:57 WRN] Missing or invalid Authorization header for /zenInvoice/api/merchant-invoice-order
[2025-08-05 21:47:27 INF] Validating JWT token
[2025-08-05 21:47:27 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:47:27 INF] Extracting claims from JWT token
[2025-08-05 21:47:27 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:47:27 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:47:27 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/merchant-invoice-order
[2025-08-05 21:47:27 INF] Skipping authentication for path: /zeninvoice/api/merchant-invoice-order (matched: /zeninvoice/api/merchant-invoice-order)
[2025-08-05 21:47:27 INF] AES Secret Encryption Service initialized
[2025-08-05 21:47:27 WRN] Failed to determine the https port for redirect.
[2025-08-05 21:47:27 INF] Validating JWT token
[2025-08-05 21:47:27 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:47:27 INF] Extracting claims from JWT token
[2025-08-05 21:47:27 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:47:27 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:47:27 INF] Creating merchant invoice order for MerchantBranchId: null
[2025-08-05 21:47:27 INF] Creating merchant invoice order for MerchantBranchId: null
[2025-08-05 21:47:28 INF] Successfully created merchant invoice order with Id: "01987ab3-9c23-78c2-ab86-20d6e9bb59ec" for MerchantBranchId: null
[2025-08-05 21:47:28 INF] Tracking API call for partner "********-5555-5555-5555-********5552", operation invoice_create, success true, time 233.5366ms
[2025-08-05 21:48:31 WRN] Missing or invalid Authorization header for /zenInvoice/api/merchant-invoice-order/01987ab3-9c23-78c2-ab86-20d6e9bb59ec/create-account-and-confirm
[2025-08-05 21:48:36 INF] Validating JWT token
[2025-08-05 21:48:36 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:48:36 INF] Extracting claims from JWT token
[2025-08-05 21:48:36 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:48:36 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:48:36 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/merchant-invoice-order/01987ab3-9c23-78c2-ab86-20d6e9bb59ec/create-account-and-confirm
[2025-08-05 21:48:36 INF] Skipping authentication for path: /zeninvoice/api/merchant-invoice-order/01987ab3-9c23-78c2-ab86-20d6e9bb59ec/create-account-and-confirm (matched: /zeninvoice/api/merchant-invoice-order)
[2025-08-05 21:48:36 INF] AES Secret Encryption Service initialized
[2025-08-05 21:48:36 INF] Validating JWT token
[2025-08-05 21:48:36 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:48:36 INF] Extracting claims from JWT token
[2025-08-05 21:48:36 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:48:36 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:48:36 INF] Creating account and confirming MerchantInvoiceOrder with Id: "01987ab3-9c23-78c2-ab86-20d6e9bb59ec"
[2025-08-05 21:48:36 INF] Processing create account and confirm order for OrderId: "01987ab3-9c23-78c2-ab86-20d6e9bb59ec"
[2025-08-05 21:48:37 ERR] Failed executing DbCommand (9ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?' (DbType = Guid), @p3='?' (DbType = DateTime), @p4='?' (DbType = DateTime), @p5='?', @p6='?', @p7='?', @p8='?', @p9='?' (DbType = Boolean), @p10='?' (DbType = Guid), @p11='?', @p12='?' (DbType = Guid), @p13='?', @p14='?', @p15='?' (DbType = DateTime), @p16='?' (DbType = Guid), @p21='?' (DbType = Guid), @p17='?' (DbType = Guid), @p18='?', @p19='?' (DbType = DateTime), @p20='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "MerchantBranchInvoiceAccounts" ("Id", "CreatedAt", "CreatedBy", "EffectiveDate", "ExpirationDate", "InvoiceAccountPassword", "InvoiceAccountProvider", "InvoiceAccountUserName", "IpAddress", "IsDeleted", "MerchantBranchId", "MerchantBranchName", "PartnerId", "TaxNumber", "TraceId", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING "IsActive";
UPDATE "MerchantInvoiceOrders" SET "MerchantBranchId" = @p17, "Status" = @p18, "UpdatedAt" = @p19, "UpdatedBy" = @p20
WHERE "Id" = @p21;
[2025-08-05 21:48:37 ERR] An exception occurred in the database while saving changes for context type 'Infrastructure.Persistences.AppDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23505: duplicate key value violates unique constraint "AK_MerchantBranchInvoiceAccounts_MerchantBranchId"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23505
    MessageText: duplicate key value violates unique constraint "AK_MerchantBranchInvoiceAccounts_MerchantBranchId"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: public
    TableName: MerchantBranchInvoiceAccounts
    ConstraintName: AK_MerchantBranchInvoiceAccounts_MerchantBranchId
    File: nbtinsert.c
    Line: 666
    Routine: _bt_check_unique
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23505: duplicate key value violates unique constraint "AK_MerchantBranchInvoiceAccounts_MerchantBranchId"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23505
    MessageText: duplicate key value violates unique constraint "AK_MerchantBranchInvoiceAccounts_MerchantBranchId"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: public
    TableName: MerchantBranchInvoiceAccounts
    ConstraintName: AK_MerchantBranchInvoiceAccounts_MerchantBranchId
    File: nbtinsert.c
    Line: 666
    Routine: _bt_check_unique
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
[2025-08-05 21:48:37 ERR] Error occurred while processing order "01987ab3-9c23-78c2-ab86-20d6e9bb59ec"
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23505: duplicate key value violates unique constraint "AK_MerchantBranchInvoiceAccounts_MerchantBranchId"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23505
    MessageText: duplicate key value violates unique constraint "AK_MerchantBranchInvoiceAccounts_MerchantBranchId"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: public
    TableName: MerchantBranchInvoiceAccounts
    ConstraintName: AK_MerchantBranchInvoiceAccounts_MerchantBranchId
    File: nbtinsert.c
    Line: 666
    Routine: _bt_check_unique
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Infrastructure.Persistences.AppDbContext.SaveChangesAsync(CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Persistences/AppDbContext.cs:line 104
   at Applications.Features.MerchantInvoiceOrder.Commands.UpdateAccountAndConfirmCommandHandler.Handle(UpdateAccountAndConfirmCommand request, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Applications/Features/MerchantInvoiceOrder/Commands/UpdateAccountAndConfirmCommand.cs:line 186
[2025-08-05 21:48:37 ERR] Unexpected error while processing MerchantInvoiceOrder with Id "01987ab3-9c23-78c2-ab86-20d6e9bb59ec": An error occurred while processing the request
[2025-08-05 21:48:37 INF] Tracking API call for partner "********-5555-5555-5555-********5552", operation invoice_create, success true, time 341.1474ms
[2025-08-05 21:49:22 INF] Validating JWT token
[2025-08-05 21:49:22 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:49:22 INF] Extracting claims from JWT token
[2025-08-05 21:49:22 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:49:22 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:49:22 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/merchant-invoice-order/01987ab3-9c23-78c2-ab86-20d6e9bb59ec/create-account-and-confirm
[2025-08-05 21:49:22 INF] Skipping authentication for path: /zeninvoice/api/merchant-invoice-order/01987ab3-9c23-78c2-ab86-20d6e9bb59ec/create-account-and-confirm (matched: /zeninvoice/api/merchant-invoice-order)
[2025-08-05 21:49:22 INF] AES Secret Encryption Service initialized
[2025-08-05 21:49:22 INF] Validating JWT token
[2025-08-05 21:49:22 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:49:22 INF] Extracting claims from JWT token
[2025-08-05 21:49:22 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:49:22 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:49:22 INF] Creating account and confirming MerchantInvoiceOrder with Id: "01987ab3-9c23-78c2-ab86-20d6e9bb59ec"
[2025-08-05 21:49:22 INF] Processing create account and confirm order for OrderId: "01987ab3-9c23-78c2-ab86-20d6e9bb59ec"
[2025-08-05 21:49:22 ERR] Failed executing DbCommand (7ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = DateTime), @p2='?' (DbType = Guid), @p3='?' (DbType = DateTime), @p4='?' (DbType = DateTime), @p5='?', @p6='?', @p7='?', @p8='?', @p9='?' (DbType = Boolean), @p10='?' (DbType = Guid), @p11='?', @p12='?' (DbType = Guid), @p13='?', @p14='?', @p15='?' (DbType = DateTime), @p16='?' (DbType = Guid), @p21='?' (DbType = Guid), @p17='?' (DbType = Guid), @p18='?', @p19='?' (DbType = DateTime), @p20='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "MerchantBranchInvoiceAccounts" ("Id", "CreatedAt", "CreatedBy", "EffectiveDate", "ExpirationDate", "InvoiceAccountPassword", "InvoiceAccountProvider", "InvoiceAccountUserName", "IpAddress", "IsDeleted", "MerchantBranchId", "MerchantBranchName", "PartnerId", "TaxNumber", "TraceId", "UpdatedAt", "UpdatedBy")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16)
RETURNING "IsActive";
UPDATE "MerchantInvoiceOrders" SET "MerchantBranchId" = @p17, "Status" = @p18, "UpdatedAt" = @p19, "UpdatedBy" = @p20
WHERE "Id" = @p21;
[2025-08-05 21:49:22 ERR] An exception occurred in the database while saving changes for context type 'Infrastructure.Persistences.AppDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23505: duplicate key value violates unique constraint "AK_MerchantBranchInvoiceAccounts_MerchantBranchId"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23505
    MessageText: duplicate key value violates unique constraint "AK_MerchantBranchInvoiceAccounts_MerchantBranchId"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: public
    TableName: MerchantBranchInvoiceAccounts
    ConstraintName: AK_MerchantBranchInvoiceAccounts_MerchantBranchId
    File: nbtinsert.c
    Line: 666
    Routine: _bt_check_unique
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23505: duplicate key value violates unique constraint "AK_MerchantBranchInvoiceAccounts_MerchantBranchId"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23505
    MessageText: duplicate key value violates unique constraint "AK_MerchantBranchInvoiceAccounts_MerchantBranchId"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: public
    TableName: MerchantBranchInvoiceAccounts
    ConstraintName: AK_MerchantBranchInvoiceAccounts_MerchantBranchId
    File: nbtinsert.c
    Line: 666
    Routine: _bt_check_unique
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
[2025-08-05 21:49:22 ERR] Error occurred while processing order "01987ab3-9c23-78c2-ab86-20d6e9bb59ec"
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23505: duplicate key value violates unique constraint "AK_MerchantBranchInvoiceAccounts_MerchantBranchId"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23505
    MessageText: duplicate key value violates unique constraint "AK_MerchantBranchInvoiceAccounts_MerchantBranchId"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: public
    TableName: MerchantBranchInvoiceAccounts
    ConstraintName: AK_MerchantBranchInvoiceAccounts_MerchantBranchId
    File: nbtinsert.c
    Line: 666
    Routine: _bt_check_unique
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Infrastructure.Persistences.AppDbContext.SaveChangesAsync(CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Persistences/AppDbContext.cs:line 104
   at Applications.Features.MerchantInvoiceOrder.Commands.UpdateAccountAndConfirmCommandHandler.Handle(UpdateAccountAndConfirmCommand request, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Applications/Features/MerchantInvoiceOrder/Commands/UpdateAccountAndConfirmCommand.cs:line 186
[2025-08-05 21:49:22 ERR] Unexpected error while processing MerchantInvoiceOrder with Id "01987ab3-9c23-78c2-ab86-20d6e9bb59ec": An error occurred while processing the request
[2025-08-05 21:49:22 INF] Tracking API call for partner "********-5555-5555-5555-********5552", operation invoice_create, success true, time 219.7477ms
[2025-08-05 21:49:54 INF] Validating JWT token
[2025-08-05 21:49:54 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:49:54 INF] Extracting claims from JWT token
[2025-08-05 21:49:54 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:49:54 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:49:54 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/merchant-invoice-order/01987ab3-9c23-78c2-ab86-20d6e9bb59ec/create-account-and-confirm
[2025-08-05 21:49:54 INF] Skipping authentication for path: /zeninvoice/api/merchant-invoice-order/01987ab3-9c23-78c2-ab86-20d6e9bb59ec/create-account-and-confirm (matched: /zeninvoice/api/merchant-invoice-order)
[2025-08-05 21:49:54 INF] AES Secret Encryption Service initialized
[2025-08-05 21:49:54 INF] Validating JWT token
[2025-08-05 21:49:54 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:49:54 INF] Extracting claims from JWT token
[2025-08-05 21:49:54 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:49:54 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:49:54 INF] Creating account and confirming MerchantInvoiceOrder with Id: "01987ab3-9c23-78c2-ab86-20d6e9bb59ec"
[2025-08-05 21:49:54 INF] Processing create account and confirm order for OrderId: "01987ab3-9c23-78c2-ab86-20d6e9bb59ec"
[2025-08-05 21:49:54 INF] Successfully created account "453296e1-33b8-4a89-8b6d-517f1571af3d" and confirmed order "01987ab3-9c23-78c2-ab86-20d6e9bb59ec" for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:49:54 INF] Successfully created account and confirmed MerchantInvoiceOrder with Id: "01987ab3-9c23-78c2-ab86-20d6e9bb59ec"
[2025-08-05 21:49:54 INF] Tracking API call for partner "********-5555-5555-5555-********5552", operation invoice_create, success true, time 177.1087ms
[2025-08-05 21:54:20 WRN] Missing or invalid Authorization header for /zenInvoice/api/merchant-invoice-order/statistics/96c5dc49-3ef8-4bfd-9b4d-4b5cc9c922b0
[2025-08-05 21:55:19 WRN] Missing or invalid Authorization header for /zenInvoice/api/merchant-invoice-order/statistics/da8ce038-11bd-4d8d-90c5-6cd62f0fd437
[2025-08-05 21:55:22 INF] Validating JWT token
[2025-08-05 21:55:22 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:55:22 INF] Extracting claims from JWT token
[2025-08-05 21:55:22 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:55:22 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:55:22 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/merchant-invoice-order/statistics/da8ce038-11bd-4d8d-90c5-6cd62f0fd437
[2025-08-05 21:55:22 INF] Skipping authentication for path: /zeninvoice/api/merchant-invoice-order/statistics/da8ce038-11bd-4d8d-90c5-6cd62f0fd437 (matched: /zeninvoice/api/merchant-invoice-order)
[2025-08-05 21:55:22 INF] AES Secret Encryption Service initialized
[2025-08-05 21:55:22 INF] Validating JWT token
[2025-08-05 21:55:22 INF] Token validated successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:55:22 INF] Extracting claims from JWT token
[2025-08-05 21:55:22 INF] Claims extracted successfully for partner "********-5555-5555-5555-********5552"
[2025-08-05 21:55:22 INF] Partner ID extracted successfully: "********-5555-5555-5555-********5552"
[2025-08-05 21:55:22 INF] Getting invoice statistics for MerchantBranchId: "da8ce038-11bd-4d8d-90c5-6cd62f0fd437"
[2025-08-05 21:55:22 INF] Retrieved invoice statistics for MerchantBranchId "da8ce038-11bd-4d8d-90c5-6cd62f0fd437": Total: 10, Used: 0, Available: 10, OrderId: "01987ab3-9c23-78c2-ab86-20d6e9bb59ec"
[2025-08-05 21:55:22 INF] Successfully retrieved invoice statistics for MerchantBranchId: "da8ce038-11bd-4d8d-90c5-6cd62f0fd437"
[2025-08-05 21:55:22 INF] Tracking API call for partner "********-5555-5555-5555-********5552", operation invoice_read, success true, time 66.6891ms
