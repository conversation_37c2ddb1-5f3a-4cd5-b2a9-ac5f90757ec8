{"ConnectionStrings": {"DefaultConnection": "Server=YOUR_PRODUCTION_DB_SERVER;User Id=YOUR_DB_USER;Password=YOUR_DB_PASSWORD;Port=5432;Database=ZEN-INVOICE;Pooling=true"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.AspNetCore": "Warning"}}, "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"], "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "Logs/log-.txt", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}, "MobiFoneInvoice": {"ProductionBaseUrl": "https://hcm.mobifone.vn/gateway/mbfinv", "TestBaseUrl": "http://mobiinvoice.vn:9000", "TaxCode": "YOUR_PRODUCTION_TAX_CODE", "Username": "0301397239APP", "Password": "YOUR_PRODUCTION_PASSWORD", "TimeoutSeconds": 30, "ProductionRefId": "RF00059", "StagingRefId": "RF00059", "DevelopmentRefId": "RF00059"}, "AllowedHosts": "*"}