namespace API.Attributes;

/// <summary>
/// Attribute để chỉ định quyền hạn cần thiết cho một action
/// </summary>
[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class, AllowMultiple = true)]
public class RequirePermissionAttribute : Attribute
{
    /// <summary>
    /// Mã chức năng cần kiểm tra quyền
    /// </summary>
    public string FunctionCode { get; }

    /// <summary>
    /// Mã quyền hạn cần thiết
    /// </summary>
    public string PermissionCode { get; }

    /// <summary>
    /// Khởi tạo attribute với function code và permission code
    /// </summary>
    /// <param name="functionCode">Mã chức năng (ví dụ: "INVOICE_CREATE")</param>
    /// <param name="permissionCode">Mã quyền hạn (ví dụ: "CREATE")</param>
    public RequirePermissionAttribute(string functionCode, string permissionCode)
    {
        FunctionCode = functionCode ?? throw new ArgumentNullException(nameof(functionCode));
        PermissionCode = permissionCode ?? throw new ArgumentNullException(nameof(permissionCode));
    }
}

/// <summary>
/// Attribute để chỉ định vai trò cần thiết cho một action
/// </summary>
[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class, AllowMultiple = true)]
public class RequireRoleAttribute : Attribute
{
    /// <summary>
    /// Mã vai trò cần thiết
    /// </summary>
    public string RoleCode { get; }

    /// <summary>
    /// Khởi tạo attribute với role code
    /// </summary>
    /// <param name="roleCode">Mã vai trò (ví dụ: "ADMIN")</param>
    public RequireRoleAttribute(string roleCode)
    {
        RoleCode = roleCode ?? throw new ArgumentNullException(nameof(roleCode));
    }
}

/// <summary>
/// Attribute để bỏ qua kiểm tra authorization cho một action
/// </summary>
[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class)]
public class AllowAnonymousAttribute : Attribute
{
}

/// <summary>
/// Attribute để chỉ định constraint cần kiểm tra
/// </summary>
[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class, AllowMultiple = true)]
public class RequireConstraintAttribute : Attribute
{
    /// <summary>
    /// Loại constraint cần kiểm tra
    /// </summary>
    public string ConstraintType { get; }

    /// <summary>
    /// Giá trị tối đa cho phép
    /// </summary>
    public object? MaxValue { get; set; }

    /// <summary>
    /// Khởi tạo attribute với constraint type
    /// </summary>
    /// <param name="constraintType">Loại constraint (ví dụ: "API_RATE_LIMIT_PER_HOUR")</param>
    public RequireConstraintAttribute(string constraintType)
    {
        ConstraintType = constraintType ?? throw new ArgumentNullException(nameof(constraintType));
    }
}

/// <summary>
/// Attribute để xác thực tax code thuộc về partner đang đăng nhập
/// </summary>
[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class)]
public class ValidateTaxCodeAttribute : Attribute
{
    /// <summary>
    /// Tên parameter chứa tax code (mặc định là "taxCode")
    /// </summary>
    public string ParameterName { get; set; } = "taxCode";

    /// <summary>
    /// Có cho phép tax code null/empty không (mặc định là false)
    /// </summary>
    public bool AllowEmpty { get; set; } = false;

    /// <summary>
    /// Khởi tạo attribute với tên parameter mặc định
    /// </summary>
    public ValidateTaxCodeAttribute()
    {
    }

    /// <summary>
    /// Khởi tạo attribute với tên parameter tùy chỉnh
    /// </summary>
    /// <param name="parameterName">Tên parameter chứa tax code</param>
    public ValidateTaxCodeAttribute(string parameterName)
    {
        ParameterName = parameterName ?? throw new ArgumentNullException(nameof(parameterName));
    }
}

/// <summary>
/// Attribute để đánh dấu controller/endpoint chỉ cần Simple Bearer Token authentication
/// Bypass các middleware B2B authentication phức tạp (JWT + Signature + IP Whitelist + Permission)
/// </summary>
[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class)]
public class SimpleBearerAuthAttribute : Attribute
{
    /// <summary>
    /// Có yêu cầu token không (mặc định là true)
    /// </summary>
    public bool RequireToken { get; set; } = true;

    /// <summary>
    /// Có validate token expiration không (mặc định là true)
    /// </summary>
    public bool ValidateExpiration { get; set; } = true;

    /// <summary>
    /// Có log authentication events không (mặc định là true)
    /// </summary>
    public bool EnableLogging { get; set; } = true;

    /// <summary>
    /// Custom error message khi authentication thất bại
    /// </summary>
    public string? CustomErrorMessage { get; set; }

    /// <summary>
    /// Khởi tạo attribute với cấu hình mặc định
    /// </summary>
    public SimpleBearerAuthAttribute()
    {
    }

    /// <summary>
    /// Khởi tạo attribute với cấu hình tùy chỉnh
    /// </summary>
    /// <param name="requireToken">Có yêu cầu token không</param>
    /// <param name="validateExpiration">Có validate token expiration không</param>
    public SimpleBearerAuthAttribute(bool requireToken = true, bool validateExpiration = true)
    {
        RequireToken = requireToken;
        ValidateExpiration = validateExpiration;
    }
}