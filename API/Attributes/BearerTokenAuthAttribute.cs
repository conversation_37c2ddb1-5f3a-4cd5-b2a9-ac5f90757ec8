using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Applications.Interfaces.Services.Authentication;
using Shared.Constants;
using BuildingBlocks.Abstractions;
using System.Text.Json;

namespace API.Attributes;

/// <summary>
/// Simple Bearer Token Authentication Attribute
/// Only validates Bearer token without additional security layers (HMAC, IP whitelist, etc.)
/// </summary>
public class BearerTokenAuthAttribute : Attribute, IAsyncActionFilter
{
    public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<BearerTokenAuthAttribute>>();
        var tokenService = context.HttpContext.RequestServices.GetRequiredService<ITokenService>();

        try
        {
            logger.LogInformation("Validating Bearer token for simple authentication");

            var authHeader = context.HttpContext.Request.Headers["Authorization"].FirstOrDefault();

            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                logger.LogWarning("Missing or invalid Authorization header");
                await RespondWithError(context, ErrorCodes.UNAUTHORIZED_ERROR, "Missing or invalid Authorization header", 401);
                return;
            }

            var token = authHeader.Substring("Bearer ".Length).Trim();
            logger.LogInformation("Token extracted, length: {Length}", token.Length);

            // Validate token using existing token service
            var validationResult = await tokenService.ValidateTokenAsync(token);
            logger.LogInformation("Token validation result: IsValid={IsValid}, ErrorMessage={ErrorMessage}",
                validationResult.IsValid, validationResult.ErrorMessage);

            if (!validationResult.IsValid)
            {
                logger.LogWarning("Token validation failed: {ErrorMessage}", validationResult.ErrorMessage);
                await RespondWithError(context, ErrorCodes.UNAUTHORIZED_ERROR,
                    validationResult.ErrorMessage ?? "Invalid token", 401);
                return;
            }

            logger.LogInformation("Token validated successfully for partner {PartnerName}", validationResult.PartnerName);

            // Extract claims and set user context
            var claims = tokenService.ExtractClaims(token);
            if (claims == null)
            {
                logger.LogWarning("Unable to extract token claims");
                await RespondWithError(context, ErrorCodes.UNAUTHORIZED_ERROR, "Unable to extract token claims", 401);
                return;
            }

            var partnerId = tokenService.GetPartnerIdFromClaims(claims);
            if (partnerId == null)
            {
                logger.LogWarning("Invalid partner ID in token");
                await RespondWithError(context, ErrorCodes.UNAUTHORIZED_ERROR, "Invalid partner ID in token", 401);
                return;
            }

            // Set user context for the request
            context.HttpContext.User = claims;
            context.HttpContext.Items["PartnerId"] = partnerId.Value;
            context.HttpContext.Items["PartnerName"] = validationResult.PartnerName;
            context.HttpContext.Items["TokenExpires"] = validationResult.ExpiresAt;
            context.HttpContext.Items["IsSimpleBearerAuth"] = true; // Flag to indicate simple auth

            logger.LogInformation("Partner {PartnerId} authenticated successfully with simple Bearer token", partnerId);

            await next();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in Bearer token authentication");
            await RespondWithError(context, ErrorCodes.INTERNAL_SERVER_ERROR, "Internal server error during authentication", 500);
        }
    }

    private static async Task RespondWithError(ActionExecutingContext context, string errorCode, string message, int statusCode)
    {
        var errorResponse = new Response<object>
        {
            Code = errorCode,
            Message = message,
            TraceId = context.HttpContext.TraceIdentifier
        };

        context.Result = new ContentResult
        {
            StatusCode = statusCode,
            Content = JsonSerializer.Serialize(errorResponse),
            ContentType = "application/json"
        };
    }
}