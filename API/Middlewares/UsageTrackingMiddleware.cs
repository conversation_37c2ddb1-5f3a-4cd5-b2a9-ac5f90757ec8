using Applications.Interfaces.Services.Authorization;
using Microsoft.Extensions.Options;
using System.Diagnostics;

namespace API.Middlewares;

/// <summary>
/// Middleware for tracking API usage and performance metrics
/// </summary>
public class UsageTrackingMiddleware(RequestDelegate next,
    ILogger<UsageTrackingMiddleware> logger,
    IOptions<AuthenticationOptions> options)
{
    private readonly AuthenticationOptions _options = options.Value;

    public async Task InvokeAsync(HttpContext context, IUsageTrackingService usageTrackingService)
    {
        // Skip tracking for non-protected endpoints
        if (ShouldSkipTracking(context))
        {
            await next(context);
            return;
        }

        // Skip B2B usage tracking if Simple Bearer Auth has been completed
        if (context.Items.ContainsKey("SimpleBearerAuthCompleted"))
        {
            logger.LogDebug("Skipping B2B usage tracking - Simple Bearer Auth already completed");
            await next(context);
            return;
        }

        var stopwatch = Stopwatch.StartNew();
        var startTime = DateTime.UtcNow;
        var originalBodySize = 0L;
        var responseBodySize = 0L;
        
        try
        {
            // Measure request body size
            if (context.Request.ContentLength.HasValue)
            {
                originalBodySize = context.Request.ContentLength.Value;
            }

            // Wrap response stream to measure response size
            var originalResponseBody = context.Response.Body;
            using var responseBodyStream = new MemoryStream();
            context.Response.Body = responseBodyStream;

            // Process request
            await next(context);

            // Measure response size
            responseBodySize = responseBodyStream.Length;

            // Copy response back to original stream
            responseBodyStream.Seek(0, SeekOrigin.Begin);
            await responseBodyStream.CopyToAsync(originalResponseBody);
            context.Response.Body = originalResponseBody;

            stopwatch.Stop();

            // Track usage if partner is authenticated and tracking is enabled
            if (_options.EnableUsageTracking)
            {
                await TrackApiUsage(context, usageTrackingService, stopwatch.Elapsed.TotalMilliseconds, 
                    originalBodySize + responseBodySize, true);
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            // Track failed request
            if (_options.EnableUsageTracking)
            {
                await TrackApiUsage(context, usageTrackingService, stopwatch.Elapsed.TotalMilliseconds, 
                    originalBodySize, false);
            }
            
            logger.LogError(ex, "Error in usage tracking middleware");
            throw; // Re-throw to maintain error handling flow
        }
    }

    private async Task TrackApiUsage(
        HttpContext context, 
        IUsageTrackingService usageTrackingService, 
        double responseTimeMs, 
        long dataTransferBytes, 
        bool success)
    {
        try
        {
            var partnerId = context.Items["PartnerId"] as Guid?;
            if (partnerId == null) return;

            var operationType = DetermineOperationType(context);
            var metadata = CreateMetadata(context);

            await usageTrackingService.TrackApiCallAsync(
                partnerId.Value,
                operationType,
                success,
                responseTimeMs,
                dataTransferBytes,
                metadata);

            // Check for specific tracking attributes
            var endpoint = context.GetEndpoint();
            var trackingAttributes = endpoint?.Metadata.GetOrderedMetadata<TrackUsageAttribute>() ?? 
                [];

            foreach (var trackingAttr in trackingAttributes)
            {
                await TrackSpecificUsage(context, usageTrackingService, trackingAttr, partnerId.Value);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to track API usage for partner {PartnerId}", 
                context.Items["PartnerId"]);
        }
    }

    private async Task TrackSpecificUsage(
        HttpContext context, 
        IUsageTrackingService usageTrackingService, 
        TrackUsageAttribute trackingAttr, 
        Guid partnerId)
    {
        try
        {
            if (trackingAttr.OperationType == "invoice_purchase")
            {
                // Extract invoice purchase details from request/response
                var invoiceCount = ExtractInvoiceCount(context);
                var totalAmount = ExtractTotalAmount(context);
                var invoiceType = ExtractInvoiceType(context);

                if (invoiceCount > 0 && totalAmount > 0)
                {
                    await usageTrackingService.TrackInvoicePurchaseAsync(
                        partnerId,
                        invoiceCount,
                        totalAmount,
                        invoiceType ?? "unknown");
                }
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to track specific usage {OperationType} for partner {PartnerId}", 
                trackingAttr.OperationType, partnerId);
        }
    }

    private static bool ShouldSkipTracking(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLower();

        // Skip tracking for:
        return path != null && (
            path.Contains("/swagger") ||
            path.Contains("/health") ||
            path.Contains("/metrics") ||
            path.Contains("/zeninvoice/api/merchant-branch-invoice-account") || // ✅ Skip MerchantBranchInvoiceAccount APIs
            path == "/" ||
            path == "/favicon.ico"
        );
    }

    private static string DetermineOperationType(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLower() ?? "";
        var method = context.Request.Method;

        // Determine operation type based on endpoint
        return path switch
        {
            var p when p.Contains("invoice") && p.Contains("purchase") => "invoice_purchase",
            var p when p.Contains("invoice") && p.Contains("price") => "price_check",
            var p when p.Contains("invoice") && method == "GET" => "invoice_read",
            var p when p.Contains("invoice") && method == "POST" => "invoice_create",
            var p when p.Contains("oauth2/token") => "authentication",
            _ => $"{method.ToLower()}_{GetControllerName(path)}"
        };
    }

    private static string GetControllerName(string path)
    {
        var segments = path.Split('/', StringSplitOptions.RemoveEmptyEntries);
        if (segments.Length >= 2)
        {
            return segments[segments.Length - 2]; // Assume controller is second to last segment
        }
        return "unknown";
    }

    private static string CreateMetadata(HttpContext context)
    {
        var metadata = new
        {
            method = context.Request.Method,
            path = context.Request.Path.Value,
            userAgent = context.Request.Headers["User-Agent"].FirstOrDefault(),
            contentType = context.Request.ContentType,
            statusCode = context.Response.StatusCode,
            timestamp = DateTime.UtcNow
        };

        return System.Text.Json.JsonSerializer.Serialize(metadata);
    }

    private static int ExtractInvoiceCount(HttpContext context)
    {
        // Try to extract invoice count from query or context
        if (context.Request.Query.TryGetValue("quantity", out var quantity))
        {
            return int.TryParse(quantity, out var value) ? value : 0;
        }

        // Could also parse from request body if needed
        return 1; // Default to 1 for single invoice operations
    }

    private static decimal ExtractTotalAmount(HttpContext context)
    {
        // Try to extract amount from query or context
        if (context.Request.Query.TryGetValue("amount", out var amount))
        {
            return decimal.TryParse(amount, out var value) ? value : 0m;
        }

        return 0m;
    }

    private static string? ExtractInvoiceType(HttpContext context)
    {
        // Extract invoice type from path or query
        if (context.Request.Query.TryGetValue("type", out var type))
        {
            return type.FirstOrDefault();
        }

        var path = context.Request.Path.Value?.ToLower() ?? "";
        if (path.Contains("gtgt")) return "GTGT";
        if (path.Contains("sales")) return "SALES";
        if (path.Contains("other")) return "OTHER";

        return null;
    }
}

/// <summary>
/// Attribute to track specific usage for an endpoint
/// </summary>
[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class, AllowMultiple = true)]
public class TrackUsageAttribute : Attribute
{
    public string OperationType { get; }

    public TrackUsageAttribute(string operationType)
    {
        OperationType = operationType;
    }
}