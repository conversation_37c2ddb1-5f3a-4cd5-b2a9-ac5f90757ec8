using API.Attributes;
using Applications.Interfaces.Services.Authentication;
using BuildingBlocks.Abstractions;
using Microsoft.Extensions.Options;
using Shared.Constants;
using System.Text.Json;

namespace API.Middlewares;

/// <summary>
/// Middleware để xử lý Simple Bearer Token authentication
/// Bypass các middleware B2B phức tạp cho endpoints có SimpleBearerAuthAttribute
/// </summary>
public class SimpleBearerAuthenticationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<SimpleBearerAuthenticationMiddleware> _logger;
    private readonly AuthenticationOptions _options;

    public SimpleBearerAuthenticationMiddleware(
        RequestDelegate next,
        ILogger<SimpleBearerAuthenticationMiddleware> logger,
        IOptions<AuthenticationOptions> options)
    {
        _next = next;
        _logger = logger;
        _options = options.Value;
    }

    public async Task InvokeAsync(HttpContext context, ITokenService tokenService)
    {
        try
        {
            // Kiểm tra xem endpoint có SimpleBearerAuthAttribute không
            var endpoint = context.GetEndpoint();
            var simpleBearerAuthAttribute = endpoint?.Metadata.GetMetadata<SimpleBearerAuthAttribute>();

            // Nếu không tìm thấy ở endpoint metadata, thử tìm trong controller metadata
            if (simpleBearerAuthAttribute == null && endpoint != null)
            {
                // Tìm trong tất cả metadata của endpoint
                var allMetadata = endpoint.Metadata.ToList();
                _logger.LogDebug("Endpoint metadata count: {Count} for {Path}", allMetadata.Count, context.Request.Path);

                foreach (var metadata in allMetadata)
                {
                    _logger.LogDebug("Metadata type: {Type}", metadata.GetType().Name);
                    if (metadata is SimpleBearerAuthAttribute attr)
                    {
                        simpleBearerAuthAttribute = attr;
                        break;
                    }
                }
            }

            _logger.LogDebug("SimpleBearerAuth check for {Path}: Endpoint={EndpointExists}, Attribute={AttributeExists}",
                context.Request.Path, endpoint != null, simpleBearerAuthAttribute != null);

            if (simpleBearerAuthAttribute == null)
            {
                // Không có SimpleBearerAuthAttribute, tiếp tục với middleware pipeline bình thường
                _logger.LogDebug("No SimpleBearerAuthAttribute found for {Path}, continuing with B2B pipeline", context.Request.Path);
                await _next(context);
                return;
            }

            if (simpleBearerAuthAttribute.EnableLogging)
            {
                _logger.LogInformation("Processing Simple Bearer Authentication for {Path}", context.Request.Path);
            }

            // Kiểm tra xem có skip authentication paths không
            if (ShouldSkipAuthentication(context))
            {
                if (simpleBearerAuthAttribute.EnableLogging)
                {
                    _logger.LogInformation("Skipping authentication for path {Path}", context.Request.Path);
                }
                await _next(context);
                return;
            }

            // Thực hiện Simple Bearer Authentication
            var authResult = await PerformSimpleBearerAuthenticationAsync(context, tokenService, simpleBearerAuthAttribute);
            
            if (!authResult.IsSuccess)
            {
                await RespondWithError(context, authResult.ErrorCode, authResult.ErrorMessage, authResult.StatusCode);
                return;
            }

            if (simpleBearerAuthAttribute.EnableLogging)
            {
                _logger.LogInformation("Simple Bearer Authentication successful for partner {PartnerId} on {Path}", 
                    context.Items["PartnerId"], context.Request.Path);
            }

            // Đánh dấu rằng đã thực hiện Simple Bearer Auth để bypass các middleware B2B khác
            context.Items["SimpleBearerAuthCompleted"] = true;

            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Simple Bearer Authentication middleware");
            await RespondWithError(context, ErrorCodes.INTERNAL_SERVER_ERROR, 
                "Internal server error during authentication", 500);
        }
    }

    private async Task<AuthenticationResult> PerformSimpleBearerAuthenticationAsync(
        HttpContext context, 
        ITokenService tokenService, 
        SimpleBearerAuthAttribute attribute)
    {
        try
        {
            var authHeader = context.Request.Headers["Authorization"].FirstOrDefault();

            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                if (!attribute.RequireToken)
                {
                    // Token không bắt buộc, cho phép tiếp tục
                    return AuthenticationResult.Success();
                }

                _logger.LogWarning("Missing or invalid Authorization header for {Path}", context.Request.Path);
                return AuthenticationResult.Failure(
                    ErrorCodes.UNAUTHORIZED_ERROR,
                    attribute.CustomErrorMessage ?? "Missing or invalid Authorization header",
                    401);
            }

            var token = authHeader.Substring("Bearer ".Length).Trim();
            
            if (attribute.EnableLogging)
            {
                _logger.LogDebug("Token extracted for {Path}, length: {Length}", context.Request.Path, token.Length);
            }

            // Validate token using existing token service
            var validationResult = await tokenService.ValidateTokenAsync(token);
            
            if (!validationResult.IsValid)
            {
                _logger.LogWarning("Token validation failed for {Path}: {ErrorMessage}", 
                    context.Request.Path, validationResult.ErrorMessage);
                return AuthenticationResult.Failure(
                    ErrorCodes.UNAUTHORIZED_ERROR,
                    attribute.CustomErrorMessage ?? validationResult.ErrorMessage ?? "Invalid token",
                    401);
            }

            // Kiểm tra token expiration nếu được yêu cầu
            if (attribute.ValidateExpiration && validationResult.ExpiresAt.HasValue && 
                validationResult.ExpiresAt.Value <= DateTime.UtcNow)
            {
                _logger.LogWarning("Token expired for {Path}", context.Request.Path);
                return AuthenticationResult.Failure(
                    ErrorCodes.UNAUTHORIZED_ERROR,
                    attribute.CustomErrorMessage ?? "Token has expired",
                    401);
            }

            // Extract claims and set user context
            var claims = tokenService.ExtractClaims(token);
            if (claims == null)
            {
                _logger.LogWarning("Unable to extract token claims for {Path}", context.Request.Path);
                return AuthenticationResult.Failure(
                    ErrorCodes.UNAUTHORIZED_ERROR,
                    attribute.CustomErrorMessage ?? "Unable to extract token claims",
                    401);
            }

            var partnerId = tokenService.GetPartnerIdFromClaims(claims);
            if (partnerId == null)
            {
                _logger.LogWarning("Invalid partner ID in token for {Path}", context.Request.Path);
                return AuthenticationResult.Failure(
                    ErrorCodes.UNAUTHORIZED_ERROR,
                    attribute.CustomErrorMessage ?? "Invalid partner ID in token",
                    401);
            }

            // Set user context for the request
            context.User = claims;
            context.Items["PartnerId"] = partnerId.Value;
            context.Items["PartnerName"] = validationResult.PartnerName;
            context.Items["TokenExpires"] = validationResult.ExpiresAt;
            context.Items["IsSimpleBearerAuth"] = true;

            if (attribute.EnableLogging)
            {
                _logger.LogDebug("Partner {PartnerId} authenticated successfully with Simple Bearer token for {Path}", 
                    partnerId, context.Request.Path);
            }

            return AuthenticationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during Simple Bearer Authentication for {Path}", context.Request.Path);
            return AuthenticationResult.Failure(
                ErrorCodes.INTERNAL_SERVER_ERROR,
                "Internal server error during authentication",
                500);
        }
    }

    private bool ShouldSkipAuthentication(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLowerInvariant();
        return _options.SkipAuthenticationPaths?.Any(skipPath => 
            path?.StartsWith(skipPath.ToLowerInvariant()) == true) == true;
    }

    private static async Task RespondWithError(HttpContext context, string errorCode, string message, int statusCode)
    {
        var errorResponse = new Response<object>
        {
            Code = errorCode,
            Message = message,
            TraceId = context.TraceIdentifier
        };

        context.Response.StatusCode = statusCode;
        context.Response.ContentType = "application/json";
        
        await context.Response.WriteAsync(JsonSerializer.Serialize(errorResponse));
    }

    private class AuthenticationResult
    {
        public bool IsSuccess { get; private set; }
        public string ErrorCode { get; private set; } = string.Empty;
        public string ErrorMessage { get; private set; } = string.Empty;
        public int StatusCode { get; private set; }

        private AuthenticationResult() { }

        public static AuthenticationResult Success()
        {
            return new AuthenticationResult { IsSuccess = true };
        }

        public static AuthenticationResult Failure(string errorCode, string errorMessage, int statusCode)
        {
            return new AuthenticationResult
            {
                IsSuccess = false,
                ErrorCode = errorCode,
                ErrorMessage = errorMessage,
                StatusCode = statusCode
            };
        }
    }
}


