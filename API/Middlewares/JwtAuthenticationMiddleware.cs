﻿using Applications.Interfaces.Services.Authentication;
using Microsoft.Extensions.Options;
using Shared.Constants;
using BuildingBlocks.Abstractions;
using System.Text.Json;
using System.Security.Claims;

namespace API.Middlewares;

/// <summary>
/// Middleware for JWT authentication validation
/// </summary>
public class JwtAuthenticationMiddleware(RequestDelegate next,
    ILogger<JwtAuthenticationMiddleware> logger,
    IOptions<AuthenticationOptions> options)
{
    private readonly AuthenticationOptions _options = options.Value;

    public async Task InvokeAsync(HttpContext context, ITokenService tokenService, IIpWhitelistService ipWhitelistService)
    {
        // Skip authentication for non-protected endpoints
        if (ShouldSkipAuthentication(context))
        {
            await next(context);
            return;
        }

        // Skip B2B authentication if Simple Bearer Auth has been completed
        if (context.Items.ContainsKey("SimpleBearerAuthCompleted"))
        {
            logger.LogDebug("Skipping B2B JWT authentication - Simple Bearer Auth already completed");
            await next(context);
            return;
        }

        try
        {
            logger.LogInformation("Validating JWT token");
            var authHeader = context.Request.Headers["Authorization"].FirstOrDefault();

            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                logger.LogWarning("Missing or invalid Authorization header");
                await RespondWithError(context, ErrorCodes.UNAUTHORIZED_ERROR,
                    "Missing or invalid Authorization header", 401);
                return;
            }

            var token = authHeader.Substring("Bearer ".Length).Trim();
            logger.LogInformation("Token extracted, length: {Length}", token.Length);

            // Validate token
            var validationResult = await tokenService.ValidateTokenAsync(token);
            logger.LogInformation("Token validation result: IsValid={IsValid}, ErrorMessage={ErrorMessage}",
                validationResult.IsValid, validationResult.ErrorMessage);

            if (!validationResult.IsValid)
            {
                logger.LogWarning("Token validation failed: {ErrorMessage}", validationResult.ErrorMessage);
                await RespondWithError(context, ErrorCodes.UNAUTHORIZED_ERROR,
                    validationResult.ErrorMessage ?? "Invalid token", 401);
                return;
            }

            logger.LogInformation("Token validated successfully for partner {PartnerName}", validationResult.PartnerName);

            // Extract claims and set user context
            var claims = tokenService.ExtractClaims(token);
            if (claims == null)
            {
                logger.LogWarning("Unable to extract token claims");
                await RespondWithError(context, ErrorCodes.UNAUTHORIZED_ERROR,
                    "Unable to extract token claims", 401);
                return;
            }

            logger.LogInformation("Extracting claims from JWT token");
            var partnerId = tokenService.GetPartnerIdFromClaims(claims);
            if (partnerId == null)
            {
                logger.LogWarning("Invalid partner ID in token");
                await RespondWithError(context, ErrorCodes.UNAUTHORIZED_ERROR,
                    "Invalid partner ID in token", 401);
                return;
            }

            logger.LogInformation("Claims extracted successfully for partner {PartnerId}", partnerId);
            logger.LogInformation("Partner ID extracted successfully: {PartnerId}", partnerId);

            // Check IP whitelist if enabled
            logger.LogInformation("Checking IP whitelist for partner {PartnerId}, IP {IP}", partnerId, context.Items["ClientIP"]);
            if (_options.EnableIpWhitelist)
            {
                var clientIp = context.Items["ClientIP"]?.ToString() ??
                    ipWhitelistService.GetClientIpAddress(context);

                var ipValidationResult = await ipWhitelistService.ValidateIpAsync(
                    partnerId.Value, clientIp);

                if (!ipValidationResult.Code.Equals("000") || ipValidationResult.Data?.IsAllowed != true)
                {
                    logger.LogWarning("IP {IP} not whitelisted for partner {PartnerId}",
                        clientIp, partnerId);

                    await RespondWithError(context, ErrorCodes.FORBIDDEN_ERROR,
                        "IP address not whitelisted", 403);
                    return;
                }

                logger.LogInformation("IP whitelist validation passed for partner {PartnerId}", partnerId);
            }
            else
            {
                logger.LogInformation("IP whitelist disabled for partner {PartnerId}", partnerId);
            }

            // Set user context for the request
            context.User = claims;
            context.Items["PartnerId"] = partnerId.Value;
            context.Items["PartnerName"] = validationResult.PartnerName;
            context.Items["TokenExpires"] = validationResult.ExpiresAt;

            logger.LogInformation("Partner {PartnerId} authenticated successfully for {Path}", 
                partnerId, context.Request.Path);

            await next(context);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in JWT authentication middleware");
            
            await RespondWithError(context, ErrorCodes.INTERNAL_SERVER_ERROR, 
                "Internal server error during authentication", 500);
        }
    }

    private bool ShouldSkipAuthentication(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLower();
        
        if (string.IsNullOrEmpty(path))
            return false;
        
        // Debug logging to see actual path
        logger.LogInformation("JwtAuthenticationMiddleware checking path: {Path}", path);
            
        // Check against configured skip paths
        if (_options.SkipAuthenticationPaths != null)
        {
            foreach (var skipPath in _options.SkipAuthenticationPaths)
            {
                if (path.Contains(skipPath.ToLower()))
                {
                    logger.LogInformation("Skipping authentication for path: {Path} (matched: {SkipPath})", path, skipPath);
                    return true;
                }
            }
        }
        
        // Additional hardcoded paths for basic functionality
        var shouldSkip = path.Contains("/swagger") ||
                        path.Contains("/health") ||
                        path.Contains("/metrics") ||
                        path == "/" ||
                        path == "/favicon.ico";
                        
        if (shouldSkip)
        {
            logger.LogInformation("Skipping authentication for path: {Path} (hardcoded rule)", path);
        }
        
        return shouldSkip;
    }

    private static async Task RespondWithError(HttpContext context, string errorCode, string message, int statusCode)
    {
        var errorResponse = new Response<object>
        {
            Code = errorCode,
            Message = message,
            TraceId = context.TraceIdentifier
        };

        context.Response.StatusCode = statusCode;
        context.Response.ContentType = "application/json";
        await context.Response.WriteAsync(JsonSerializer.Serialize(errorResponse));
    }
}
