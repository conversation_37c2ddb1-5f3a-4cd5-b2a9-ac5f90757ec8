using API.Attributes;
using Applications.Interfaces.Services;
using BuildingBlocks.Abstractions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Shared.Constants;
using System.Text.Json;

namespace API.Filters;

/// <summary>
/// Action filter để xác thực tax code thuộc về partner đang đăng nhập
/// </summary>
public class ValidateTaxCodeActionFilter : IAsyncActionFilter
{
    private readonly ITaxCodeValidationService _taxCodeValidationService;
    private readonly ILogger<ValidateTaxCodeActionFilter> _logger;

    public ValidateTaxCodeActionFilter(
        ITaxCodeValidationService taxCodeValidationService,
        ILogger<ValidateTaxCodeActionFilter> logger)
    {
        _taxCodeValidationService = taxCodeValidationService;
        _logger = logger;
    }

    public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        // <PERSON><PERSON><PERSON> tra xem action có ValidateTaxCodeAttribute không
        var validateTaxCodeAttribute = context.ActionDescriptor.EndpointMetadata
            .OfType<ValidateTaxCodeAttribute>()
            .FirstOrDefault();

        if (validateTaxCodeAttribute == null)
        {
            // Không có attribute, bỏ qua validation
            await next();
            return;
        }

        try
        {
            // Lấy partner ID từ context (được set bởi authentication middleware)
            var partnerId = context.HttpContext.Items["PartnerId"] as Guid?;
            if (partnerId == null)
            {
                _logger.LogWarning("Partner ID not found in context for tax code validation");
                await RespondWithError(context, ErrorCodes.UNAUTHORIZED_ERROR, 
                    "Partner not authenticated", 401);
                return;
            }

            // Lấy tax code từ parameters
            var taxCode = GetTaxCodeFromParameters(context, validateTaxCodeAttribute.ParameterName);
            
            // Kiểm tra tax code có empty không
            if (string.IsNullOrWhiteSpace(taxCode))
            {
                if (!validateTaxCodeAttribute.AllowEmpty)
                {
                    _logger.LogWarning("Tax code is required but not provided for partner {PartnerId}", partnerId);
                    await RespondWithError(context, ErrorCodes.BAD_REQUEST_ERROR, 
                        "Tax code is required", 400);
                    return;
                }
                
                // Cho phép empty, bỏ qua validation
                await next();
                return;
            }

            // Thực hiện validation tax code
            var validationResult = await _taxCodeValidationService.ValidateTaxCodeAsync(
                partnerId.Value, taxCode);

            if (!validationResult.Code.Equals("000") || validationResult.Data?.IsValid != true)
            {
                _logger.LogWarning("Tax code validation failed for partner {PartnerId}, tax code {TaxCode}: {Message}", 
                    partnerId, taxCode, validationResult.Message);
                
                await RespondWithError(context, ErrorCodes.FORBIDDEN_ERROR, 
                    validationResult.Message ?? "Tax code does not belong to current partner", 403);
                return;
            }

            _logger.LogDebug("Tax code validation successful for partner {PartnerId}, tax code {TaxCode}", 
                partnerId, taxCode);

            // Validation thành công, tiếp tục thực hiện action
            await next();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during tax code validation for partner {PartnerId}", 
                context.HttpContext.Items["PartnerId"]);
            
            await RespondWithError(context, ErrorCodes.INTERNAL_SERVER_ERROR, 
                "Internal server error during tax code validation", 500);
        }
    }

    /// <summary>
    /// Lấy tax code từ parameters của action
    /// </summary>
    private static string? GetTaxCodeFromParameters(ActionExecutingContext context, string parameterName)
    {
        // Thử lấy từ action parameters trước
        if (context.ActionArguments.TryGetValue(parameterName, out var paramValue) && 
            paramValue is string taxCodeFromParam)
        {
            return taxCodeFromParam;
        }

        // Thử lấy từ query string
        if (context.HttpContext.Request.Query.TryGetValue(parameterName, out var queryValue))
        {
            return queryValue.FirstOrDefault();
        }

        // Thử lấy từ route values
        if (context.RouteData.Values.TryGetValue(parameterName, out var routeValue) && 
            routeValue is string taxCodeFromRoute)
        {
            return taxCodeFromRoute;
        }

        return null;
    }

    /// <summary>
    /// Trả về response lỗi
    /// </summary>
    private static async Task RespondWithError(ActionExecutingContext context, string errorCode, 
        string message, int statusCode)
    {
        var errorResponse = new Response<object>
        {
            Code = errorCode,
            Message = message,
            Data = null
        };

        context.Result = new ObjectResult(errorResponse)
        {
            StatusCode = statusCode
        };

        context.HttpContext.Response.ContentType = "application/json";
        
        await Task.CompletedTask;
    }
}
