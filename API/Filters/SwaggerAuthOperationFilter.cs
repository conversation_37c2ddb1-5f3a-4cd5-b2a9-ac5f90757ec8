using Microsoft.AspNetCore.Authorization;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using API.Attributes;

namespace API.Filters;

/// <summary>
/// Filter để loại bỏ Authorization requirement cho các API có [AllowAnonymous]
/// </summary>
public class SwaggerAuthOperationFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        // Check if the action has [AllowAnonymous] attribute (both Microsoft and custom)
        var hasAllowAnonymous = context.MethodInfo
            .GetCustomAttributes(typeof(Microsoft.AspNetCore.Authorization.AllowAnonymousAttribute), true)
            .Any() ||
            context.MethodInfo
            .GetCustomAttributes(typeof(API.Attributes.AllowAnonymousAttribute), true)
            .Any();

        // Check if the controller has [AllowAnonymous] attribute (both Microsoft and custom)
        var controllerHasAllowAnonymous = (context.MethodInfo.DeclaringType?
            .GetCustomAttributes(typeof(Microsoft.AspNetCore.Authorization.AllowAnonymousAttribute), true)
            .Any() ?? false) ||
            (context.MethodInfo.DeclaringType?
            .GetCustomAttributes(typeof(API.Attributes.AllowAnonymousAttribute), true)
            .Any() ?? false);

        // Check if using Simple Bearer Auth policy
        var hasSimpleBearerAuth = false;
        var authorizeAttributes = context.MethodInfo.GetCustomAttributes(typeof(AuthorizeAttribute), true)
            .Cast<AuthorizeAttribute>()
            .Concat(context.MethodInfo.DeclaringType?.GetCustomAttributes(typeof(AuthorizeAttribute), true)
                .Cast<AuthorizeAttribute>() ?? Enumerable.Empty<AuthorizeAttribute>());

        foreach (var attr in authorizeAttributes)
        {
            if (attr.Policy == "SimpleBearerAuth" || attr.Policy == "SimpleBearerAuthOptional")
            {
                hasSimpleBearerAuth = true;
                break;
            }
        }

        // If method or controller has [AllowAnonymous], remove ALL security requirements
        if (hasAllowAnonymous || controllerHasAllowAnonymous)
        {
            operation.Security?.Clear();

            // Also remove any parameters that are authentication-related
            if (operation.Parameters != null)
            {
                var authParams = operation.Parameters
                    .Where(p => p.Name == "X-Timestamp" ||
                               p.Name == "X-Signature" ||
                               p.Name == "X-Client-ID" ||
                               p.Name == "Authorization")
                    .ToList();

                foreach (var param in authParams)
                {
                    operation.Parameters.Remove(param);
                }
            }
        }
        // If using Simple Bearer Auth, set only Bearer token security requirement
        else if (hasSimpleBearerAuth)
        {
            operation.Security?.Clear();
            operation.Security = new List<OpenApiSecurityRequirement>
            {
                new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer"
                            }
                        },
                        Array.Empty<string>()
                    }
                }
            };

            // Remove B2B authentication parameters for Simple Bearer Auth endpoints
            if (operation.Parameters != null)
            {
                var b2bParams = operation.Parameters
                    .Where(p => p.Name == "X-Timestamp" ||
                               p.Name == "X-Signature" ||
                               p.Name == "X-Client-ID")
                    .ToList();

                foreach (var param in b2bParams)
                {
                    operation.Parameters.Remove(param);
                }
            }
        }
    }
}