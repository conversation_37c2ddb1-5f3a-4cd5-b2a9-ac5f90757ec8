using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using Microsoft.AspNetCore.Authorization;

namespace API.Filters;

/// <summary>
/// Swagger Operation Filter để thêm headers cho MobiFone APIs
/// </summary>
public class AddMobiFoneHeadersOperationFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        // Kiểm tra xem có phải controller MobiF<PERSON> không
        var controllerName = context.MethodInfo.DeclaringType?.Name;
        var isMobiFoneController = controllerName == "MobiFoneInvoiceControlle";
        var isAuthenticationController = controllerName == "AuthenticationController";
        var isAdminController = controllerName == "AdminController";

        // Check if the action has [AllowAnonymous] attribute (both Microsoft and custom)
        var hasAllowAnonymous = context.MethodInfo
            .GetCustomAttributes(typeof(Microsoft.AspNetCore.Authorization.AllowAnonymousAttribute), true)
            .Any() ||
            context.MethodInfo
            .GetCustomAttributes(typeof(API.Attributes.AllowAnonymousAttribute), true)
            .Any();

        // Check if the controller has [AllowAnonymous] attribute (both Microsoft and custom)
        var controllerHasAllowAnonymous = (context.MethodInfo.DeclaringType?
            .GetCustomAttributes(typeof(Microsoft.AspNetCore.Authorization.AllowAnonymousAttribute), true)
            .Any() ?? false) ||
            (context.MethodInfo.DeclaringType?
            .GetCustomAttributes(typeof(API.Attributes.AllowAnonymousAttribute), true)
            .Any() ?? false);

        // Check if using Simple Bearer Auth policy
        var hasSimpleBearerAuth = false;
        var authorizeAttributes = context.MethodInfo.GetCustomAttributes(typeof(AuthorizeAttribute), true)
            .Cast<AuthorizeAttribute>()
            .Concat(context.MethodInfo.DeclaringType?.GetCustomAttributes(typeof(AuthorizeAttribute), true)
                .Cast<AuthorizeAttribute>() ?? Enumerable.Empty<AuthorizeAttribute>());

        foreach (var attr in authorizeAttributes)
        {
            if (attr.Policy == "SimpleBearerAuth" || attr.Policy == "SimpleBearerAuthOptional")
            {
                hasSimpleBearerAuth = true;
                break;
            }
        }

        if (operation.Parameters == null)
        {
            operation.Parameters = new List<OpenApiParameter>();
        }

        // ✅ RESTORED: Authentication headers cho MobiFone APIs
        // Thêm HMAC headers cho tất cả protected APIs (trừ authentication endpoints, AllowAnonymous endpoints, và Simple Bearer Auth endpoints)
        if (!isAuthenticationController && !isAdminController && !hasAllowAnonymous && !controllerHasAllowAnonymous && !hasSimpleBearerAuth)
        {
            // X-Timestamp header
            operation.Parameters.Add(new OpenApiParameter
            {
                Name = "X-Timestamp",
                In = ParameterLocation.Header,
                Required = true,
                Schema = new OpenApiSchema { Type = "string" },
                Description = "Unix timestamp (seconds since epoch). Use /api/authentication/timestamp to get current timestamp"
            });

            // X-Signature header
            operation.Parameters.Add(new OpenApiParameter
            {
                Name = "X-Signature",
                In = ParameterLocation.Header,
                Required = true,
                Schema = new OpenApiSchema { Type = "string" },
                Description = "HMAC-SHA256 signature. Format: Base64(HMAC-SHA256(StringToSign, HmacSecret))"
            });

            // X-Client-ID header (case-sensitive to match middleware)
            operation.Parameters.Add(new OpenApiParameter
            {
                Name = "X-Client-ID",
                In = ParameterLocation.Header,
                Required = true,
                Schema = new OpenApiSchema { Type = "string" },
                Description = "Client ID used for HMAC signature calculation"
            });
        }

        // Thêm description về Simple Bearer Auth
        if (hasSimpleBearerAuth)
        {
            var originalDescription = operation.Description ?? "";
            operation.Description = originalDescription + "\n\n" +
                "**Simple Bearer Authentication:**\n" +
                "This endpoint uses Simple Bearer Token authentication. Only the Authorization header is required:\n" +
                "```\n" +
                "Authorization: Bearer your-jwt-token-here\n" +
                "```\n" +
                "No additional headers (X-Timestamp, X-Signature, X-Client-ID) are needed.";
        }
        // Thêm description về HMAC signature calculation cho B2B endpoints
        else if (!isAuthenticationController && !isAdminController && !hasAllowAnonymous && !controllerHasAllowAnonymous)
        {
            var originalDescription = operation.Description ?? "";
            operation.Description = originalDescription + "\n\n" +
                "**HMAC Signature Calculation:**\n" +
                "```\n" +
                "StringToSign = HTTP_METHOD + \"\\n\" + REQUEST_PATH + \"\\n\" + TIMESTAMP + \"\\n\" + CLIENT_ID + \"\\n\" + PAYLOAD\n" +
                "Signature = Base64(HMAC-SHA256(StringToSign, HmacSecret))\n" +
                "```\n" +
                "Where:\n" +
                "- HTTP_METHOD: GET, POST, PUT, DELETE, etc.\n" +
                "- REQUEST_PATH: /api/mobifone-invoice/login\n" +
                "- TIMESTAMP: Unix timestamp from X-Timestamp header\n" +
                "- CLIENT_ID: Value from X-Client-Id header\n" +
                "- PAYLOAD: Request body (empty string for GET requests)\n" +
                "- HmacSecret: Your partner's HMAC secret key";
        }
    }
}