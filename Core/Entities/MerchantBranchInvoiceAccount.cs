using System.ComponentModel.DataAnnotations;
using Core.Entities.Authentication;

namespace Core.Entities;

/// <summary>
/// Merchant Branch Invoice Account entity - represents invoice account credentials for merchant branches
/// </summary>
public class MerchantBranchInvoiceAccount : Audit
{
    /// <summary>
    /// Tax number for the merchant branch
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string TaxNumber { get; set; } = null!;

    /// <summary>
    /// Username for the invoice account
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string InvoiceAccountUserName { get; set; } = null!;

    /// <summary>
    /// Password for the invoice account (should be encrypted in production)
    /// </summary>
    [Required]
    [MaxLength(500)]
    public string InvoiceAccountPassword { get; set; } = null!;

    /// <summary>
    /// Invoice account provider (hardcoded as "MBF")
    /// </summary>
    [Required]
    [MaxLength(10)]
    public string InvoiceAccountProvider { get; set; } = "MBF";

    /// <summary>
    /// When this invoice account becomes effective
    /// </summary>
    [Required]
    public DateTime EffectiveDate { get; set; }

    /// <summary>
    /// When this invoice account expires
    /// </summary>
    [Required]
    public DateTime ExpirationDate { get; set; }

    /// <summary>
    /// Foreign key to MerchantBranch (using Guid as per existing patterns)
    /// </summary>
    [Required]
    public Guid MerchantBranchId { get; set; }

    /// <summary>
    /// Merchant branch name for display purposes
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string MerchantBranchName { get; set; } = null!;

    /// <summary>
    /// Foreign key reference to Partner entity's Id
    /// </summary>
    [Required]
    public Guid PartnerId { get; set; }

    /// <summary>
    /// Whether this invoice account is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    // Navigation Properties
    /// <summary>
    /// Navigation property to Partner
    /// </summary>
    public virtual Partner Partner { get; set; } = null!;

    /// <summary>
    /// Navigation property to MerchantInvoiceOrders
    /// </summary>
    public virtual ICollection<MerchantInvoiceOrder> MerchantInvoiceOrders { get; set; } = [];

    /// <summary>
    /// Navigation property to InvoiceInfos
    /// </summary>
    public virtual ICollection<InvoiceInfo> InvoiceInfos { get; set; } = [];
}
