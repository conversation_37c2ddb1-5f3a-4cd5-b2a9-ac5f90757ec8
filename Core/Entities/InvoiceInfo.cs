using System.ComponentModel.DataAnnotations;
using Core.Entities.Authentication;
using Core.Enumerables;

namespace Core.Entities;

/// <summary>
/// Invoice Info entity - stores invoice information when invoices are created
/// Processed in invoice creation, signing, and related APIs
/// </summary>
public class InvoiceInfo : Audit
{
    /// <summary>
    /// Invoice ID from MobiFone API response
    /// This is the unique identifier returned by the MobiFone invoice system
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string InvoiceId { get; set; } = string.Empty;

    /// <summary>
    /// Tax authority invoice status
    /// Represents the status of the invoice as reported by the tax authority system
    /// </summary>
    [Required]
    public CqtInvoiceStatus CqtInvoiceStatus { get; set; }

    /// <summary>
    /// Invoice business status
    /// Represents the business status of the invoice in the system
    /// </summary>
    [Required]
    public InvoiceStatus InvoiceStatus { get; set; }

    /// <summary>
    /// JSON field storing the original API request data
    /// Contains the complete request payload sent to MobiFone API
    /// </summary>
    [Required]
    public string RequestData { get; set; } = string.Empty;

    /// <summary>
    /// JSON field storing the API response data
    /// Contains the complete response received from MobiFone API
    /// </summary>
    [Required]
    public string ResponseData { get; set; } = string.Empty;

    /// <summary>
    /// Foreign key to MerchantInvoiceOrder
    /// References the invoice order that this invoice belongs to
    /// </summary>
    [Required]
    public Guid MerchantInvoiceOrderId { get; set; }



    /// <summary>
    /// Invoice number as assigned by the system
    /// </summary>
    [MaxLength(50)]
    public string? InvoiceNumber { get; set; }

    /// <summary>
    /// Invoice series/template code
    /// </summary>
    [MaxLength(20)]
    public string? InvoiceSeries { get; set; }

    /// <summary>
    /// Total amount of the invoice
    /// </summary>
    public decimal? TotalAmount { get; set; }

    /// <summary>
    /// Tax amount of the invoice
    /// </summary>
    public decimal? TaxAmount { get; set; }

    /// <summary>
    /// Customer name on the invoice
    /// </summary>
    [MaxLength(255)]
    public string? CustomerName { get; set; }

    /// <summary>
    /// Customer tax code
    /// </summary>
    [MaxLength(20)]
    public string? CustomerTaxCode { get; set; }

    /// <summary>
    /// Date when the invoice was issued
    /// </summary>
    public DateTime? InvoiceDate { get; set; }

    /// <summary>
    /// Date when the invoice was signed
    /// </summary>
    public DateTime? SignedDate { get; set; }

    /// <summary>
    /// Date when the invoice was sent to tax authority
    /// </summary>
    public DateTime? SentToCqtDate { get; set; }

    /// <summary>
    /// Error message if any error occurred during processing
    /// </summary>
    [MaxLength(1000)]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Additional notes or comments about this invoice
    /// </summary>
    [MaxLength(1000)]
    public string? Notes { get; set; }

    public string CurlString { get; set; } = string.Empty;

    // Navigation Properties
    /// <summary>
    /// Navigation property to MerchantInvoiceOrder
    /// </summary>
    public virtual MerchantInvoiceOrder MerchantInvoiceOrder { get; set; } = null!;


}
