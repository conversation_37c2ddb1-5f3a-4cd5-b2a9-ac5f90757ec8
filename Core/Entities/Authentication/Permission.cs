using System.ComponentModel.DataAnnotations;

namespace Core.Entities.Authentication;

/// <summary>
/// Represents permission types (CREATE, READ, UPDATE, DELETE, EXECUTE)
/// </summary>
public class Permission : Audit
{
    /// <summary>
    /// Permission code (e.g., "CREATE", "READ", "UPDATE", "DELETE", "EXECUTE")
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string Code { get; set; } = null!;

    /// <summary>
    /// Human-readable permission name
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = null!;

    /// <summary>
    /// Permission description
    /// </summary>
    [MaxLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Whether this permission is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Priority/order for display purposes
    /// </summary>
    public int DisplayOrder { get; set; } = 0;

    // Navigation Properties
    public virtual ICollection<FunctionPermission> FunctionPermissions { get; set; } = new List<FunctionPermission>();
    public virtual ICollection<RoleFunctionPermission> RoleFunctionPermissions { get; set; } = new List<RoleFunctionPermission>();
    public virtual ICollection<PartnerFunctionPermission> PartnerFunctionPermissions { get; set; } = new List<PartnerFunctionPermission>();
}