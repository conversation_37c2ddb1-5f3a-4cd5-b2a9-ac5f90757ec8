using System.ComponentModel.DataAnnotations;

namespace Core.Entities.Authentication;

/// <summary>
/// Represents active OAuth2 access tokens for partners
/// </summary>
public class PartnerToken : Audit
{
    /// <summary>
    /// Foreign key to Partner
    /// </summary>
    public Guid PartnerId { get; set; }

    /// <summary>
    /// The actual JWT token value
    /// </summary>
    [Required]
    [MaxLength(2000)]
    public string AccessToken { get; set; } = null!;

    /// <summary>
    /// When this token expires (UTC)
    /// </summary>
    public DateTime ExpiresAt { get; set; }

    /// <summary>
    /// Token scope/permissions (JSON array)
    /// </summary>
    [MaxLength(1000)]
    public string? Scope { get; set; }

    /// <summary>
    /// Whether this token is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// When this token was revoked (if applicable)
    /// </summary>
    public DateTime? RevokedAt { get; set; }

    /// <summary>
    /// Reason for revocation
    /// </summary>
    [MaxLength(500)]
    public string? RevocationReason { get; set; }

    /// <summary>
    /// IP address where token was issued
    /// </summary>
    [MaxLength(45)]
    public string? IssuedFromIp { get; set; }

    /// <summary>
    /// User agent where token was issued
    /// </summary>
    [MaxLength(1000)]
    public string? IssuedFromUserAgent { get; set; }

    // Navigation Properties
    public virtual Partner Partner { get; set; } = null!;

    /// <summary>
    /// Check if token is currently valid
    /// </summary>
    public bool IsValid => IsActive && !IsDeleted && ExpiresAt > DateTime.UtcNow;
}