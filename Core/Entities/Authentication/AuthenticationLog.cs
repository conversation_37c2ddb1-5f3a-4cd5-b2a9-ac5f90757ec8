using System.ComponentModel.DataAnnotations;

namespace Core.Entities.Authentication;

/// <summary>
/// Audit log for all authentication attempts
/// </summary>
public class AuthenticationLog : Audit
{
    /// <summary>
    /// Foreign key to <PERSON> (nullable for failed attempts with unknown partner)
    /// </summary>
    public Guid? PartnerId { get; set; }

    /// <summary>
    /// Client ID used in the authentication attempt
    /// </summary>
    [MaxLength(100)]
    public string? ClientId { get; set; }

    /// <summary>
    /// IP address of the authentication attempt
    /// </summary>
    [Required]
    [MaxLength(45)]
    public string IpAddress { get; set; } = null!;

    /// <summary>
    /// User agent of the authentication attempt
    /// </summary>
    [MaxLength(1000)]
    public string? UserAgent { get; set; }

    /// <summary>
    /// Whether the authentication was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Reason for authentication failure (if applicable)
    /// </summary>
    [MaxLength(500)]
    public string? FailureReason { get; set; }

    /// <summary>
    /// Authentication method used
    /// Examples: "OAuth2", "HMAC", "IP_Whitelist"
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string AuthenticationMethod { get; set; } = null!;

    /// <summary>
    /// Timestamp of the authentication attempt
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Request path that was being accessed
    /// </summary>
    [MaxLength(500)]
    public string? RequestPath { get; set; }

    /// <summary>
    /// HTTP method used
    /// </summary>
    [MaxLength(10)]
    public string? HttpMethod { get; set; }

    /// <summary>
    /// Additional metadata about the authentication attempt (JSON)
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// Token ID that was generated (if successful)
    /// </summary>
    public Guid? TokenId { get; set; }

    // Navigation Properties
    public virtual Partner? Partner { get; set; }
    public virtual PartnerToken? Token { get; set; }
}