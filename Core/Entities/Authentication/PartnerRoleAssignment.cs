using System.ComponentModel.DataAnnotations;

namespace Core.Entities.Authentication;

/// <summary>
/// Assigns roles to partners with optional expiration
/// </summary>
public class PartnerRoleAssignment : Audit
{
    /// <summary>
    /// Foreign key to Partner
    /// </summary>
    public Guid PartnerId { get; set; }

    /// <summary>
    /// Foreign key to PartnerRole
    /// </summary>
    public Guid RoleId { get; set; }

    /// <summary>
    /// When this role assignment was made
    /// </summary>
    public DateTime AssignedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When this role assignment expires (optional)
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Whether this role assignment is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Who assigned this role (foreign key to user/admin)
    /// </summary>
    public Guid? AssignedBy { get; set; }

    /// <summary>
    /// Reason or notes for this role assignment
    /// </summary>
    [MaxLength(1000)]
    public string? AssignmentReason { get; set; }

    // Navigation Properties
    public virtual Partner Partner { get; set; } = null!;
    public virtual PartnerRole Role { get; set; } = null!;

    /// <summary>
    /// Check if this role assignment is currently valid
    /// </summary>
    public bool IsValid => IsActive && !IsDeleted && (ExpiresAt == null || ExpiresAt > DateTime.UtcNow);
}