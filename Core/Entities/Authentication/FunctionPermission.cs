using System.ComponentModel.DataAnnotations;

namespace Core.Entities.Authentication;

/// <summary>
/// Defines which permissions are required/available for each function
/// </summary>
public class FunctionPermission : Audit
{
    /// <summary>
    /// Foreign key to Function
    /// </summary>
    public Guid FunctionId { get; set; }

    /// <summary>
    /// Foreign key to Permission
    /// </summary>
    public Guid PermissionId { get; set; }

    /// <summary>
    /// Whether this permission is required for the function
    /// If true, this permission must be granted to access the function
    /// If false, this permission is optional/available but not required
    /// </summary>
    public bool IsRequired { get; set; } = true;

    /// <summary>
    /// Additional constraints or metadata for this function-permission relationship
    /// </summary>
    [MaxLength(1000)]
    public string? Constraints { get; set; }

    // Navigation Properties
    public virtual Function Function { get; set; } = null!;
    public virtual Permission Permission { get; set; } = null!;
}