using System.ComponentModel.DataAnnotations;

namespace Core.Entities.Authentication;

/// <summary>
/// Represents roles that can be assigned to partners
/// </summary>
public class PartnerRole : Audit
{
    /// <summary>
    /// Unique role code (e.g., "ADMIN", "STANDARD_PARTNER", "PREMIUM_PARTNER")
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Code { get; set; } = null!;

    /// <summary>
    /// Human-readable role name
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = null!;

    /// <summary>
    /// Role description
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Role priority (higher number = higher priority)
    /// Used for role hierarchy and conflict resolution
    /// </summary>
    public int Priority { get; set; } = 0;

    /// <summary>
    /// Whether this role is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Default API rate limit per hour for partners with this role
    /// </summary>
    public int DefaultApiRateLimitPerHour { get; set; } = 1000;

    /// <summary>
    /// Default monthly invoice limit for partners with this role
    /// </summary>
    public decimal DefaultMonthlyInvoiceLimit { get; set; } = 10000;

    /// <summary>
    /// JSON metadata for additional role properties
    /// </summary>
    public string? Metadata { get; set; }

    // Navigation Properties
    public virtual ICollection<RoleFunctionPermission> RoleFunctionPermissions { get; set; } = new List<RoleFunctionPermission>();
    public virtual ICollection<PartnerRoleAssignment> PartnerRoleAssignments { get; set; } = new List<PartnerRoleAssignment>();
}