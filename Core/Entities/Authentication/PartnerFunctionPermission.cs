using System.ComponentModel.DataAnnotations;

namespace Core.Entities.Authentication;

/// <summary>
/// Override permissions for specific partners (takes precedence over role permissions)
/// </summary>
public class PartnerFunctionPermission : Audit
{
    /// <summary>
    /// Foreign key to Partner
    /// </summary>
    public Guid PartnerId { get; set; }

    /// <summary>
    /// Foreign key to Function
    /// </summary>
    public Guid FunctionId { get; set; }

    /// <summary>
    /// Foreign key to Permission
    /// </summary>
    public Guid PermissionId { get; set; }

    /// <summary>
    /// Whether this permission is granted to the partner
    /// true = granted, false = explicitly denied (overrides role permissions)
    /// </summary>
    public bool IsGranted { get; set; } = true;

    /// <summary>
    /// When this permission override expires (optional)
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Who granted this permission override
    /// </summary>
    public Guid? GrantedBy { get; set; }

    /// <summary>
    /// Reason or notes for this permission override
    /// </summary>
    [MaxLength(1000)]
    public string? GrantReason { get; set; }

    /// <summary>
    /// Additional constraints or conditions for this permission
    /// JSON format for flexibility
    /// </summary>
    [MaxLength(1000)]
    public string? Constraints { get; set; }

    // Navigation Properties
    public virtual Partner Partner { get; set; } = null!;
    public virtual Function Function { get; set; } = null!;
    public virtual Permission Permission { get; set; } = null!;

    /// <summary>
    /// Check if this permission override is currently valid
    /// </summary>
    public bool IsValid => !IsDeleted && (ExpiresAt == null || ExpiresAt > DateTime.UtcNow);
}