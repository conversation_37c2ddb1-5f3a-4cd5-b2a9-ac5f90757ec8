using System.ComponentModel.DataAnnotations;

namespace Core.Entities.Authentication;

/// <summary>
/// Tracks API usage and billing information for partners
/// </summary>
public class PartnerUsage : Audit
{
    /// <summary>
    /// Foreign key to Partner
    /// </summary>
    public Guid PartnerId { get; set; }

    /// <summary>
    /// Usage period (YYYY-MM format, e.g., "2024-01")
    /// </summary>
    [Required]
    [MaxLength(7)]
    public string Period { get; set; } = null!;

    /// <summary>
    /// Number of invoices purchased in this period
    /// </summary>
    public int InvoicesPurchased { get; set; } = 0;

    /// <summary>
    /// Total amount spent on invoices in this period
    /// </summary>
    public decimal TotalAmount { get; set; } = 0;

    /// <summary>
    /// Number of API calls made in this period
    /// </summary>
    public long ApiCallsCount { get; set; } = 0;

    /// <summary>
    /// Number of successful API calls
    /// </summary>
    public long SuccessfulApiCalls { get; set; } = 0;

    /// <summary>
    /// Number of failed API calls
    /// </summary>
    public long FailedApiCalls { get; set; } = 0;

    /// <summary>
    /// Total data transfer in bytes
    /// </summary>
    public long DataTransferBytes { get; set; } = 0;

    /// <summary>
    /// Peak API requests per hour in this period
    /// </summary>
    public int PeakRequestsPerHour { get; set; } = 0;

    /// <summary>
    /// Average response time in milliseconds
    /// </summary>
    public double AverageResponseTimeMs { get; set; } = 0;

    /// <summary>
    /// Additional usage metrics (JSON format)
    /// </summary>
    public string? AdditionalMetrics { get; set; }

    /// <summary>
    /// When this usage record was last updated
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

    // Navigation Properties
    public virtual Partner Partner { get; set; } = null!;

    /// <summary>
    /// Generate period string from date
    /// </summary>
    public static string GetPeriod(DateTime date)
    {
        return date.ToString("yyyy-MM");
    }

    /// <summary>
    /// Get current period
    /// </summary>
    public static string GetCurrentPeriod()
    {
        return GetPeriod(DateTime.UtcNow);
    }
}