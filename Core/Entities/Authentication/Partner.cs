using System.ComponentModel.DataAnnotations;

namespace Core.Entities.Authentication;

/// <summary>
/// Partner entity - represents external partners/clients using the API
/// </summary>
public class Partner : Audit
{
    /// <summary>
    /// Client ID for OAuth2 authentication (public identifier)
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string ClientId { get; set; } = null!;

    /// <summary>
    /// Hashed client secret for OAuth2 authentication
    /// </summary>
    [Required]
    [MaxLength(500)]
    public string ClientSecretHash { get; set; } = null!;

    /// <summary>
    /// HMAC secret for payload signature validation (separate from OAuth2 secret)
    /// NOTE: Currently stored as plaintext. TODO: Implement encryption in production
    /// </summary>
    [Required]
    [MaxLength(500)]
    public string HmacSecretHash { get; set; } = null!;

    /// <summary>
    /// Partner name/company name
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = null!;

    /// <summary>
    /// Contact email for this partner
    /// </summary>
    [MaxLength(200)]
    public string? ContactEmail { get; set; }

    /// <summary>
    /// Contact phone for this partner
    /// </summary>
    [MaxLength(20)]
    public string? ContactPhone { get; set; }

    /// <summary>
    /// Description/notes about this partner
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// JSON array of whitelisted IP addresses/CIDR ranges
    /// Example: ["***********/24", "********"]
    /// </summary>
    public string? IpWhitelist { get; set; }

    /// <summary>
    /// Whether IP whitelist checking is enabled for this partner
    /// </summary>
    public bool EnableIpWhitelist { get; set; } = true;

    /// <summary>
    /// Whether this partner is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// API rate limit per hour for this partner
    /// </summary>
    public int ApiRateLimitPerHour { get; set; } = 1000;

    /// <summary>
    /// Monthly invoice purchase limit for this partner
    /// </summary>
    public decimal MonthlyInvoiceLimit { get; set; } = 10000;

    /// <summary>
    /// Current month's invoice usage
    /// </summary>
    public decimal CurrentMonthUsage { get; set; } = 0;

    // Navigation Properties
    public virtual ICollection<PartnerToken> PartnerTokens { get; set; } = new List<PartnerToken>();
    public virtual ICollection<PartnerRoleAssignment> RoleAssignments { get; set; } = new List<PartnerRoleAssignment>();
    public virtual ICollection<PartnerFunctionPermission> FunctionPermissions { get; set; } = new List<PartnerFunctionPermission>();
    public virtual ICollection<PartnerConstraint> PartnerConstraints { get; set; } = new List<PartnerConstraint>();
    public virtual ICollection<AuthenticationLog> AuthenticationLogs { get; set; } = new List<AuthenticationLog>();
    public virtual ICollection<PartnerUsage> PartnerUsages { get; set; } = new List<PartnerUsage>();
}