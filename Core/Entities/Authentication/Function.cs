using System.ComponentModel.DataAnnotations;

namespace Core.Entities.Authentication;

/// <summary>
/// Represents system functions/features that can have permissions applied
/// </summary>
public class Function : Audit
{
    /// <summary>
    /// Unique function code (e.g., "invoice.purchase", "invoice.price_check")
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Code { get; set; } = null!;

    /// <summary>
    /// Human-readable function name
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = null!;

    /// <summary>
    /// Function description
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Module/category this function belongs to (e.g., "Invoice", "Payment", "Report")
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Module { get; set; } = null!;

    /// <summary>
    /// Whether this function is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Priority/order for display purposes
    /// </summary>
    public int DisplayOrder { get; set; } = 0;

    /// <summary>
    /// JSON metadata for additional function properties
    /// </summary>
    public string? Metadata { get; set; }

    // Navigation Properties
    public virtual ICollection<FunctionPermission> FunctionPermissions { get; set; } = new List<FunctionPermission>();
    public virtual ICollection<RoleFunctionPermission> RoleFunctionPermissions { get; set; } = new List<RoleFunctionPermission>();
    public virtual ICollection<PartnerFunctionPermission> PartnerFunctionPermissions { get; set; } = new List<PartnerFunctionPermission>();
}