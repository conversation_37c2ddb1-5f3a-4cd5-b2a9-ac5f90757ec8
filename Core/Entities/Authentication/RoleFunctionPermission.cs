using System.ComponentModel.DataAnnotations;

namespace Core.Entities.Authentication;

/// <summary>
/// Defines which function permissions are granted to each role
/// </summary>
public class RoleFunctionPermission : Audit
{
    /// <summary>
    /// Foreign key to PartnerRole
    /// </summary>
    public Guid RoleId { get; set; }

    /// <summary>
    /// Foreign key to Function
    /// </summary>
    public Guid FunctionId { get; set; }

    /// <summary>
    /// Foreign key to Permission
    /// </summary>
    public Guid PermissionId { get; set; }

    /// <summary>
    /// Whether this permission is granted to the role
    /// true = granted, false = explicitly denied
    /// </summary>
    public bool IsGranted { get; set; } = true;

    /// <summary>
    /// When this permission grant expires (optional)
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Additional constraints or conditions for this permission grant
    /// JSON format for flexibility
    /// </summary>
    [MaxLength(1000)]
    public string? Constraints { get; set; }

    // Navigation Properties
    public virtual PartnerRole Role { get; set; } = null!;
    public virtual Function Function { get; set; } = null!;
    public virtual Permission Permission { get; set; } = null!;

    /// <summary>
    /// Check if this permission grant is currently valid
    /// </summary>
    public bool IsValid => IsGranted && !IsDeleted && (ExpiresAt == null || ExpiresAt > DateTime.UtcNow);
}