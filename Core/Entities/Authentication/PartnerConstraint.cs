using System.ComponentModel.DataAnnotations;

namespace Core.Entities.Authentication;

/// <summary>
/// Business rule constraints for partners (limits, quotas, etc.)
/// </summary>
public class PartnerConstraint : Audit
{
    /// <summary>
    /// Foreign key to Partner
    /// </summary>
    public Guid PartnerId { get; set; }

    /// <summary>
    /// Type of constraint (e.g., "MONTHLY_LIMIT", "TRANSACTION_SIZE", "API_RATE_LIMIT")
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string ConstraintType { get; set; } = null!;

    /// <summary>
    /// Constraint value (JSON format for flexibility)
    /// Examples:
    /// - {"maxAmount": 50000} for MONTHLY_LIMIT
    /// - {"maxSize": 1000} for TRANSACTION_SIZE
    /// - {"requestsPerHour": 500} for API_RATE_LIMIT
    /// </summary>
    [Required]
    public string ConstraintValue { get; set; } = null!;

    /// <summary>
    /// When this constraint becomes valid
    /// </summary>
    public DateTime ValidFrom { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When this constraint expires (optional)
    /// </summary>
    public DateTime? ValidTo { get; set; }

    /// <summary>
    /// Whether this constraint is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Priority for constraint resolution (higher number = higher priority)
    /// </summary>
    public int Priority { get; set; } = 0;

    /// <summary>
    /// Description or reason for this constraint
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Who set this constraint
    /// </summary>
    public Guid? SetBy { get; set; }

    // Navigation Properties
    public virtual Partner Partner { get; set; } = null!;

    /// <summary>
    /// Check if this constraint is currently valid
    /// </summary>
    public bool IsValid => IsActive && !IsDeleted && 
                          DateTime.UtcNow >= ValidFrom && 
                          (ValidTo == null || DateTime.UtcNow <= ValidTo);
}