using System.ComponentModel.DataAnnotations;
using Core.Enumerables;

namespace Core.Entities;

/// <summary>
/// Merchant Invoice Order entity - represents invoice allocation orders for merchant branches
/// Tracks the total quantity of invoices allocated and remaining for each merchant branch
/// </summary>
public class MerchantInvoiceOrder : Audit
{
    /// <summary>
    /// Foreign key to MerchantBranchInvoiceAccount (merchant_branch_id)
    /// References the merchant branch that this invoice order belongs to
    /// </summary>
    public Guid? MerchantBranchId { get; set; }

    /// <summary>
    /// Total number of invoices allocated in this order
    /// </summary>
    [Required]
    public int TotalInvoiceQuantity { get; set; }

    /// <summary>
    /// Number of invoices remaining/unused from this order
    /// This value decreases as invoices are created and used
    /// </summary>
    [Required]
    public int RemainingInvoiceQuantity { get; set; }

    /// <summary>
    /// Start date of the validity period for this invoice order
    /// </summary>
    [Required]
    public DateTime EffectiveDateFrom { get; set; }

    /// <summary>
    /// End date of the validity period for this invoice order
    /// </summary>
    [Required]
    public DateTime EffectiveDateTo { get; set; }

    /// <summary>
    /// Optional description or notes about this invoice order
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Whether this invoice order is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Order reference number or identifier from external system
    /// </summary>
    [MaxLength(100)]
    public string? OrderReference { get; set; }

    /// <summary>
    /// Status of the merchant invoice order
    /// </summary>
    public MerchantInvoiceOrderStatus? Status { get; set; }

    // Navigation Properties
    /// <summary>
    /// Navigation property to MerchantBranchInvoiceAccount
    /// </summary>
    public virtual MerchantBranchInvoiceAccount MerchantBranchInvoiceAccount { get; set; } = null!;

    /// <summary>
    /// Navigation property to InvoiceInfo records that belong to this order
    /// </summary>
    public virtual ICollection<InvoiceInfo> InvoiceInfos { get; set; } = [];
}
