namespace Core.Enumerables;

/// <summary>
/// Tax Authority Invoice Status (Trạng thái hóa đơn từ Cơ quan Thuế)
/// Represents the status of invoices as reported by the tax authority system
/// </summary>
public enum CqtInvoiceStatus
{
    /// <summary>
    /// Chờ ký - Invoice is waiting to be signed (value: 0)
    /// </summary>
    PendingSignature = 0,

    /// <summary>
    /// Đã ký - Invoice has been signed (value: 1)
    /// </summary>
    Signed = 1,

    /// <summary>
    /// Chờ cấp mã - Waiting for code assignment from tax authority (value: 2)
    /// </summary>
    PendingCodeAssignment = 2,

    /// <summary>
    /// TBSS sai định dạng - Tax authority notification of incorrect format (value: 3)
    /// </summary>
    InvalidFormat = 3,

    /// <summary>
    /// CQT Không cấp mã - Tax authority did not assign code (value: 4)
    /// </summary>
    CodeNotAssigned = 4,

    /// <summary>
    /// Ch<PERSON> phản hồi - Waiting for response from tax authority (value: 5)
    /// </summary>
    PendingResponse = 5,

    /// <summary>
    /// CQT không tiếp nhận HĐ - Tax authority did not accept the invoice (value: 6)
    /// </summary>
    NotAccepted = 6,

    /// <summary>
    /// Chấp nhận TBSS - Tax authority accepted the notification (value: 7)
    /// </summary>
    TbssAccepted = 7,

    /// <summary>
    /// Đã gửi - Invoice has been sent (value: 8)
    /// </summary>
    Sent = 8,

    /// <summary>
    /// CQT đã nhận - Tax authority has received the invoice (value: 9)
    /// </summary>
    ReceivedByCqt = 9,

    /// <summary>
    /// Đã cấp mã - Code has been assigned by tax authority (value: 10)
    /// </summary>
    CodeAssigned = 10,

    /// <summary>
    /// Không chấp nhận TBSS - Tax authority did not accept the notification (value: 11)
    /// </summary>
    TbssRejected = 11
}

/// <summary>
/// Helper class for CqtInvoiceStatus enum with Vietnamese display names
/// </summary>
public static class CqtInvoiceStatusExtensions
{
    /// <summary>
    /// Dictionary mapping enum values to Vietnamese display names
    /// </summary>
    public static readonly Dictionary<CqtInvoiceStatus, string> DisplayNames = new()
    {
        { CqtInvoiceStatus.PendingSignature, "Chờ ký" },
        { CqtInvoiceStatus.Signed, "Đã ký" },
        { CqtInvoiceStatus.PendingCodeAssignment, "Chờ cấp mã" },
        { CqtInvoiceStatus.InvalidFormat, "TBSS sai định dạng" },
        { CqtInvoiceStatus.CodeNotAssigned, "CQT Không cấp mã" },
        { CqtInvoiceStatus.PendingResponse, "Chờ phản hồi" },
        { CqtInvoiceStatus.NotAccepted, "CQT không tiếp nhận HĐ" },
        { CqtInvoiceStatus.TbssAccepted, "Chấp nhận TBSS" },
        { CqtInvoiceStatus.Sent, "Đã gửi" },
        { CqtInvoiceStatus.ReceivedByCqt, "CQT đã nhận" },
        { CqtInvoiceStatus.CodeAssigned, "Đã cấp mã" },
        { CqtInvoiceStatus.TbssRejected, "Không chấp nhận TBSS" }
    };

    /// <summary>
    /// Get Vietnamese display name for the status
    /// </summary>
    /// <param name="status">The CqtInvoiceStatus enum value</param>
    /// <returns>Vietnamese display name</returns>
    public static string GetDisplayName(this CqtInvoiceStatus status)
    {
        return DisplayNames.TryGetValue(status, out var displayName) ? displayName : status.ToString();
    }

    /// <summary>
    /// Get all available statuses with their display names
    /// </summary>
    /// <returns>List of all statuses with display names</returns>
    public static List<(CqtInvoiceStatus Status, string DisplayName)> GetAllStatuses()
    {
        return Enum.GetValues<CqtInvoiceStatus>()
            .Select(status => (status, status.GetDisplayName()))
            .ToList();
    }
}
