namespace Core.Enumerables;

/// <summary>
/// Invoice Status (Trạng thái hóa đơn)
/// Represents the business status of invoices in the system
/// </summary>
public enum InvoiceStatus
{
    /// <summary>
    /// Gốc - Original invoice (value: 0)
    /// </summary>
    Original = 0,

    /// <summary>
    /// Giải trình - Explanation invoice (value: 1)
    /// </summary>
    Explanation = 1,

    /// <summary>
    /// Thay thế - Replacement invoice (value: 2)
    /// </summary>
    Replacement = 2,

    /// <summary>
    /// Hủy - Cancelled invoice (value: 3)
    /// </summary>
    Cancelled = 3,

    /// <summary>
    /// Rà soát - Review invoice (value: 4)
    /// </summary>
    Review = 4,

    /// <summary>
    /// Điều chỉnh - Adjustment invoice (value: 5)
    /// </summary>
    Adjustment = 5,

    /// <summary>
    /// Chờ điều chỉnh - Pending adjustment (value: 7)
    /// </summary>
    PendingAdjustment = 7,

    /// <summary>
    /// Bị điều chỉnh - Adjusted (value: 11)
    /// </summary>
    Adjusted = 11,

    /// <summary>
    /// Chờ hủy - Pending cancellation (value: 13)
    /// </summary>
    PendingCancellation = 13,

    /// <summary>
    /// Chờ thay thế - Pending replacement (value: 15)
    /// </summary>
    PendingReplacement = 15,

    /// <summary>
    /// Bị thay thế - Replaced (value: 17)
    /// </summary>
    Replaced = 17,

    /// <summary>
    /// Điều chỉnh tăng - Adjustment increase (value: 19)
    /// </summary>
    AdjustmentIncrease = 19,

    /// <summary>
    /// Điều chỉnh giảm - Adjustment decrease (value: 21)
    /// </summary>
    AdjustmentDecrease = 21,

    /// <summary>
    /// Điều chỉnh thông tin - Information adjustment (value: 23)
    /// </summary>
    AdjustmentInfo = 23
}

/// <summary>
/// Helper class for InvoiceStatus enum with Vietnamese display names
/// </summary>
public static class InvoiceStatusExtensions
{
    /// <summary>
    /// Dictionary mapping enum values to Vietnamese display names
    /// </summary>
    public static readonly Dictionary<InvoiceStatus, string> DisplayNames = new()
    {
        { InvoiceStatus.Original, "Gốc" },
        { InvoiceStatus.Explanation, "Giải trình" },
        { InvoiceStatus.Replacement, "Thay thế" },
        { InvoiceStatus.Cancelled, "Hủy" },
        { InvoiceStatus.Review, "Rà soát" },
        { InvoiceStatus.Adjustment, "Điều chỉnh" },
        { InvoiceStatus.PendingAdjustment, "Chờ điều chỉnh" },
        { InvoiceStatus.Adjusted, "Bị điều chỉnh" },
        { InvoiceStatus.PendingCancellation, "Chờ hủy" },
        { InvoiceStatus.PendingReplacement, "Chờ thay thế" },
        { InvoiceStatus.Replaced, "Bị thay thế" },
        { InvoiceStatus.AdjustmentIncrease, "Điều chỉnh tăng" },
        { InvoiceStatus.AdjustmentDecrease, "Điều chỉnh giảm" },
        { InvoiceStatus.AdjustmentInfo, "Điều chỉnh thông tin" }
    };

    /// <summary>
    /// Get Vietnamese display name for the status
    /// </summary>
    /// <param name="status">The InvoiceStatus enum value</param>
    /// <returns>Vietnamese display name</returns>
    public static string GetDisplayName(this InvoiceStatus status)
    {
        return DisplayNames.TryGetValue(status, out var displayName) ? displayName : status.ToString();
    }

    /// <summary>
    /// Get all available statuses with their display names
    /// </summary>
    /// <returns>List of all statuses with display names</returns>
    public static List<(InvoiceStatus Status, string DisplayName)> GetAllStatuses()
    {
        return Enum.GetValues<InvoiceStatus>()
            .Select(status => (status, status.GetDisplayName()))
            .ToList();
    }
}
