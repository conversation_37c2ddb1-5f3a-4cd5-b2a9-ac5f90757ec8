namespace Core.Enumerables;

/// <summary>
/// Merchant Invoice Order Status (Trạng thái đơn hàng hóa đơn merchant)
/// Represents the status of merchant invoice orders in the system
/// </summary>
public enum MerchantInvoiceOrderStatus
{
    /// <summary>
    /// Chờ xử lý - Order is pending processing (value: 1)
    /// </summary>
    PENDING = 1,

    /// <summary>
    /// Đã hoàn thành - Order has been completed (value: 2)
    /// </summary>
    COMPLETED = 2
}

/// <summary>
/// Helper class for MerchantInvoiceOrderStatus enum with Vietnamese display names
/// </summary>
public static class MerchantInvoiceOrderStatusExtensions
{
    /// <summary>
    /// Dictionary mapping enum values to Vietnamese display names
    /// </summary>
    public static readonly Dictionary<MerchantInvoiceOrderStatus, string> DisplayNames = new()
    {
        { MerchantInvoiceOrderStatus.PENDING, "Chờ xử lý" },
        { MerchantInvoiceOrderStatus.COMPLETED, "Đã hoàn thành" }
    };

    /// <summary>
    /// Get Vietnamese display name for the status
    /// </summary>
    /// <param name="status">The MerchantInvoiceOrderStatus enum value</param>
    /// <returns>Vietnamese display name</returns>
    public static string GetDisplayName(this MerchantInvoiceOrderStatus status)
    {
        return DisplayNames.TryGetValue(status, out var displayName) ? displayName : status.ToString();
    }

    /// <summary>
    /// Get all available statuses with their display names
    /// </summary>
    /// <returns>List of all statuses with display names</returns>
    public static List<(MerchantInvoiceOrderStatus Status, string DisplayName)> GetAllStatuses()
    {
        return Enum.GetValues<MerchantInvoiceOrderStatus>()
            .Select(status => (status, status.GetDisplayName()))
            .ToList();
    }
}
