namespace Core.Enumerables;

/// <summary>
/// Types of business constraints that can be applied to partners
/// </summary>
public static class ConstraintTypeEnum
{
    /// <summary>
    /// Monthly invoice purchase limit
    /// ConstraintValue: {"maxAmount": 50000}
    /// </summary>
    public const string MonthlyLimit = "MONTHLY_LIMIT";

    /// <summary>
    /// Maximum transaction size
    /// ConstraintValue: {"maxSize": 1000}
    /// </summary>
    public const string TransactionSize = "TRANSACTION_SIZE";

    /// <summary>
    /// API rate limit per hour
    /// ConstraintValue: {"requestsPerHour": 500}
    /// </summary>
    public const string ApiRateLimit = "API_RATE_LIMIT";

    /// <summary>
    /// Maximum concurrent API connections
    /// ConstraintValue: {"maxConnections": 10}
    /// </summary>
    public const string ConcurrentConnections = "CONCURRENT_CONNECTIONS";

    /// <summary>
    /// Data transfer limits
    /// ConstraintValue: {"maxBytesPerDay": 1000000}
    /// </summary>
    public const string DataTransferLimit = "DATA_TRANSFER_LIMIT";

    /// <summary>
    /// Time-based access restrictions
    /// ConstraintValue: {"allowedHours": [9, 10, 11, 12, 13, 14, 15, 16, 17]}
    /// </summary>
    public const string TimeRestriction = "TIME_RESTRICTION";

    /// <summary>
    /// Geographic access restrictions
    /// ConstraintValue: {"allowedCountries": ["VN", "SG"]}
    /// </summary>
    public const string GeoRestriction = "GEO_RESTRICTION";

    /// <summary>
    /// Feature access limits
    /// ConstraintValue: {"allowedFeatures": ["basic_invoice", "pdf_export"]}
    /// </summary>
    public const string FeatureLimit = "FEATURE_LIMIT";
}