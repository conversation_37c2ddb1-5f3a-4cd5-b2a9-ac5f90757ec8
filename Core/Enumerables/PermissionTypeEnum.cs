namespace Core.Enumerables;

/// <summary>
/// Standard permission types for CRUD and execution operations
/// </summary>
public static class PermissionTypeEnum
{
    /// <summary>
    /// Permission to create new resources
    /// </summary>
    public const string Create = "CREATE";

    /// <summary>
    /// Permission to read/view resources
    /// </summary>
    public const string Read = "READ";

    /// <summary>
    /// Permission to update existing resources
    /// </summary>
    public const string Update = "UPDATE";

    /// <summary>
    /// Permission to delete resources
    /// </summary>
    public const string Delete = "DELETE";

    /// <summary>
    /// Permission to execute operations/functions
    /// </summary>
    public const string Execute = "EXECUTE";

    /// <summary>
    /// Administrative permissions (full access)
    /// </summary>
    public const string Admin = "ADMIN";

    /// <summary>
    /// Permission to export data
    /// </summary>
    public const string Export = "EXPORT";

    /// <summary>
    /// Permission to import data
    /// </summary>
    public const string Import = "IMPORT";

    /// <summary>
    /// Permission to approve actions
    /// </summary>
    public const string Approve = "APPROVE";

    /// <summary>
    /// Permission to reject actions
    /// </summary>
    public const string Reject = "REJECT";
}