using Core.Entities.Authentication;

namespace Applications.Interfaces.Repositories;

/// <summary>
/// Repository interface for Partner entity
/// </summary>
public interface IPartnerRepository : IBaseRepository<Partner>
{
    /// <summary>
    /// Get partner by ClientId
    /// </summary>
    /// <param name="clientId">Client ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Partner entity or null</returns>
    Task<Partner?> GetByClientIdAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if ClientId exists
    /// </summary>
    /// <param name="clientId">Client ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if exists</returns>
    Task<bool> ClientIdExistsAsync(string clientId, CancellationToken cancellationToken = default);
}
