using Core.Entities;
using Core.Entities.Authentication;
using Microsoft.EntityFrameworkCore;

namespace Applications.Interfaces;

/// <summary>
/// Interface cho Application Database Context
/// Cung cấp truy cập đến các DbSet và SaveChanges methods
/// </summary>
public interface IApplicationDbContext
{
    // Legacy DbSets (will be removed)
    DbSet<ClientCredential> ClientCredentials { get; }
    DbSet<SmsLog> SmsLogs { get; }
    DbSet<SmsRetryQueue> SmsRetryQueues { get; }

    // Authentication System DbSets
    DbSet<Partner> Partners { get; }
    DbSet<PartnerToken> PartnerTokens { get; }
    DbSet<Function> Functions { get; }
    DbSet<Permission> Permissions { get; }
    DbSet<FunctionPermission> FunctionPermissions { get; }
    DbSet<PartnerRole> PartnerRoles { get; }
    DbSet<RoleFunctionPermission> RoleFunctionPermissions { get; }
    DbSet<PartnerRoleAssignment> PartnerRoleAssignments { get; }
    DbSet<PartnerFunctionPermission> PartnerFunctionPermissions { get; }
    DbSet<PartnerConstraint> PartnerConstraints { get; }
    DbSet<AuthenticationLog> AuthenticationLogs { get; }
    DbSet<PartnerUsage> PartnerUsages { get; }

    // Business Entity DbSets
    DbSet<MerchantBranchInvoiceAccount> MerchantBranchInvoiceAccounts { get; }
    DbSet<MerchantInvoiceOrder> MerchantInvoiceOrders { get; }
    DbSet<InvoiceInfo> InvoiceInfos { get; }

    // SaveChanges methods
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    int SaveChanges();

    // Entry method for change tracking
    Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry<TEntity> Entry<TEntity>(TEntity entity) where TEntity : class;
}
