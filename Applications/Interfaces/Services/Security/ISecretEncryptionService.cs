namespace Applications.Interfaces.Services.Security;

/// <summary>
/// Service for encrypting and decrypting sensitive data like HMAC secrets
/// </summary>
public interface ISecretEncryptionService
{
    /// <summary>
    /// Encrypt a plaintext secret
    /// </summary>
    /// <param name="plaintext">The plaintext secret to encrypt</param>
    /// <returns>Base64 encoded encrypted secret</returns>
    string EncryptSecret(string plaintext);

    /// <summary>
    /// Decrypt an encrypted secret
    /// </summary>
    /// <param name="encryptedSecret">Base64 encoded encrypted secret</param>
    /// <returns>Decrypted plaintext secret</returns>
    string DecryptSecret(string encryptedSecret);

    /// <summary>
    /// Check if a string is encrypted (starts with encryption prefix)
    /// </summary>
    /// <param name="value">Value to check</param>
    /// <returns>True if encrypted</returns>
    bool IsEncrypted(string value);
}
