﻿using BuildingBlocks.Abstractions;

namespace Applications.Interfaces.Services.Authorization;

/// <summary>
/// Interface for dynamic permission checking from database
/// </summary>
public interface IPermissionService
{
    /// <summary>
    /// Check if a partner has permission to perform a specific action on a function
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="functionCode">Function code (e.g., "invoice.purchase")</param>
    /// <param name="permissionCode">Permission code (e.g., "CREATE", "READ")</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Permission check result</returns>
    Task<Response<PermissionCheckResult>> HasPermissionAsync(
        Guid partnerId,
        string functionCode,
        string permissionCode,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all permissions for a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of partner permissions</returns>
    Task<Response<PartnerPermissionSummary>> GetPartnerPermissionsAsync(
        Guid partnerId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check multiple permissions at once
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="permissionRequests">List of function-permission combinations to check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Bulk permission check results</returns>
    Task<Response<Dictionary<string, PermissionCheckResult>>> CheckMultiplePermissionsAsync(
        Guid partnerId,
        IEnumerable<PermissionRequest> permissionRequests,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Grant a specific permission to a partner (override)
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="functionCode">Function code</param>
    /// <param name="permissionCode">Permission code</param>
    /// <param name="grantedBy">Who granted this permission</param>
    /// <param name="reason">Reason for granting</param>
    /// <param name="expiresAt">When this permission expires (optional)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Grant result</returns>
    Task<Response<bool>> GrantPermissionAsync(
        Guid partnerId,
        string functionCode,
        string permissionCode,
        Guid grantedBy,
        string reason,
        DateTime? expiresAt = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Revoke a specific permission from a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="functionCode">Function code</param>
    /// <param name="permissionCode">Permission code</param>
    /// <param name="revokedBy">Who revoked this permission</param>
    /// <param name="reason">Reason for revoking</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Revoke result</returns>
    Task<Response<bool>> RevokePermissionAsync(
        Guid partnerId,
        string functionCode,
        string permissionCode,
        Guid revokedBy,
        string reason,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Clear cached permissions for a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    void ClearPermissionCache(Guid partnerId);

    /// <summary>
    /// Clear all permission cache
    /// </summary>
    void ClearAllPermissionCache();
}

/// <summary>
/// Request for permission check
/// </summary>
public class PermissionRequest
{
    public string FunctionCode { get; set; } = null!;
    public string PermissionCode { get; set; } = null!;
    public string? RequestKey { get; set; } // Optional key to identify this request in bulk results
}

/// <summary>
/// Result of permission check
/// </summary>
public class PermissionCheckResult
{
    public bool HasPermission { get; set; }
    public string FunctionCode { get; set; } = null!;
    public string PermissionCode { get; set; } = null!;
    public string Source { get; set; } = null!; // "Role", "Override", "Denied"
    public string? SourceDetails { get; set; } // Role name or override details
    public DateTime? ExpiresAt { get; set; }
    public string? DenialReason { get; set; }
}

/// <summary>
/// Summary of all permissions for a partner
/// </summary>
public class PartnerPermissionSummary
{
    public Guid PartnerId { get; set; }
    public string PartnerName { get; set; } = null!;
    public string[] Roles { get; set; } = Array.Empty<string>();
    public Dictionary<string, FunctionPermissions> Functions { get; set; } = new();
    public DateTime CacheTime { get; set; }
}

/// <summary>
/// Permissions for a specific function
/// </summary>
public class FunctionPermissions
{
    public string FunctionCode { get; set; } = null!;
    public string FunctionName { get; set; } = null!;
    public string Module { get; set; } = null!;
    public Dictionary<string, PermissionCheckResult> Permissions { get; set; } = new();
}
