using BuildingBlocks.Abstractions;

namespace Applications.Interfaces.Services;

/// <summary>
/// Interface cho service xác thực tax code
/// </summary>
public interface ITaxCodeValidationService
{
    /// <summary>
    /// Xác thực tax code có thuộc về partner đang đăng nhập không
    /// </summary>
    /// <param name="partnerId">ID của partner đang đăng nhập</param>
    /// <param name="taxCode">Tax code cần xác thực</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Kết quả xác thực</returns>
    Task<Response<TaxCodeValidationResult>> ValidateTaxCodeAsync(
        Guid partnerId, 
        string taxCode, 
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Kết quả xác thực tax code
/// </summary>
public class TaxCodeValidationResult
{
    /// <summary>
    /// Tax code c<PERSON> hợ<PERSON> lệ không
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// Tax code được kiểm tra
    /// </summary>
    public string TaxCode { get; set; } = null!;

    /// <summary>
    /// ID của partner
    /// </summary>
    public Guid PartnerId { get; set; }

    /// <summary>
    /// ID của merchant branch invoice account (nếu tìm thấy)
    /// </summary>
    public Guid? MerchantBranchInvoiceAccountId { get; set; }

    /// <summary>
    /// Tên merchant branch (nếu tìm thấy)
    /// </summary>
    public string? MerchantBranchName { get; set; }

    /// <summary>
    /// Lý do không hợp lệ (nếu có)
    /// </summary>
    public string? InvalidReason { get; set; }

    /// <summary>
    /// Thời gian hiệu lực
    /// </summary>
    public DateTime? EffectiveDate { get; set; }

    /// <summary>
    /// Thời gian hết hạn
    /// </summary>
    public DateTime? ExpirationDate { get; set; }

    /// <summary>
    /// Trạng thái active
    /// </summary>
    public bool? IsActive { get; set; }
}
