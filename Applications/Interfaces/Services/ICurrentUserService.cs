using Core.Entities.Authentication;

namespace Applications.Interfaces.Services;

/// <summary>
/// Service to get current authenticated user information
/// </summary>
public interface ICurrentUserService
{
    /// <summary>
    /// Get current partner ID from the authenticated context
    /// </summary>
    /// <returns>Partner ID if authenticated, null otherwise</returns>
    Guid? GetCurrentPartnerId();

    /// <summary>
    /// Get current partner name from the authenticated context
    /// </summary>
    /// <returns>Partner name if authenticated, null otherwise</returns>
    string? GetCurrentPartnerName();

    /// <summary>
    /// Get current partner information from database
    /// </summary>
    /// <returns>Partner entity if authenticated and found, null otherwise</returns>
    Task<Partner?> GetCurrentPartnerAsync();

    /// <summary>
    /// Get current partner information with related data
    /// </summary>
    /// <returns>Partner entity with navigation properties loaded</returns>
    Task<Partner?> GetCurrentPartnerWithDetailsAsync();

    /// <summary>
    /// Check if current request is authenticated
    /// </summary>
    /// <returns>True if authenticated, false otherwise</returns>
    bool IsAuthenticated();

    /// <summary>
    /// Get token expiration time for current user
    /// </summary>
    /// <returns>Token expiration time if available</returns>
    DateTime? GetTokenExpiration();

    /// <summary>
    /// Check if current authentication is simple Bearer token (without HMAC/IP validation)
    /// </summary>
    /// <returns>True if simple Bearer auth, false if full security auth</returns>
    bool IsSimpleBearerAuth();

    /// <summary>
    /// Get current user's scopes/permissions
    /// </summary>
    /// <returns>Array of scopes if available</returns>
    string[] GetCurrentUserScopes();
}