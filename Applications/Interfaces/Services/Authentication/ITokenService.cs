using Core.Entities.Authentication;
using System.Security.Claims;

namespace Applications.Interfaces.Services.Authentication;

/// <summary>
/// Interface for JWT token generation and validation
/// </summary>
public interface ITokenService
{
    /// <summary>
    /// Generate a new JWT access token for a partner
    /// </summary>
    /// <param name="partner">Partner entity</param>
    /// <param name="scopes">Token scopes/permissions</param>
    /// <param name="ipAddress">IP address where token is issued</param>
    /// <param name="userAgent">User agent where token is issued</param>
    /// <returns>Generated token information</returns>
    Task<TokenInfo> GenerateTokenAsync(
        Partner partner, 
        string[] scopes, 
        string ipAddress, 
        string? userAgent = null);

    /// <summary>
    /// Validate a JWT token and extract claims
    /// </summary>
    /// <param name="token">JWT token to validate</param>
    /// <returns>Token validation result with claims</returns>
    Task<TokenValidationResult> ValidateTokenAsync(string token);

    /// <summary>
    /// Extract claims from a valid JWT token
    /// </summary>
    /// <param name="token">JWT token</param>
    /// <returns>Claims principal</returns>
    ClaimsPrincipal? ExtractClaims(string token);

    /// <summary>
    /// Get partner ID from token claims
    /// </summary>
    /// <param name="claims">Claims principal</param>
    /// <returns>Partner ID if found</returns>
    Guid? GetPartnerIdFromClaims(ClaimsPrincipal claims);

    /// <summary>
    /// Check if token is expired
    /// </summary>
    /// <param name="token">JWT token</param>
    /// <returns>True if token is expired</returns>
    bool IsTokenExpired(string token);

    /// <summary>
    /// Get token expiration time
    /// </summary>
    /// <param name="token">JWT token</param>
    /// <returns>Expiration time if valid</returns>
    DateTime? GetTokenExpiration(string token);
}

/// <summary>
/// Information about a generated token
/// </summary>
public class TokenInfo
{
    public string AccessToken { get; set; } = null!;
    public DateTime ExpiresAt { get; set; }
    public int ExpiresIn { get; set; }
    public string TokenType { get; set; } = "Bearer";
    public string[] Scopes { get; set; } = Array.Empty<string>();
    public Guid TokenId { get; set; }
}