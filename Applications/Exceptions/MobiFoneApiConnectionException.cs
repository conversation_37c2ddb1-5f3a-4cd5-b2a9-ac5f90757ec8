﻿using BuildingBlocks.Exceptions;

namespace Applications.Exceptions;

public class MobiFoneApiConnectionException : AppLogicException
{
    public override string ErrorCode { get; } = "MOBIFONE_001";

    public MobiFoneApiConnectionException() : base("không thá»ƒ káº¿t ná»‘i Ä‘áº¿n MobiFone API")
    {
    }

    public MobiFoneApiConnectionException(string message) : base($"Lá»—i káº¿t ná»‘i MobiFone API: {message}")
    {
    }

    public MobiFoneApiConnectionException(string endpoint, Exception innerException) 
        : base($"không thá»ƒ káº¿t ná»‘i Ä‘áº¿n endpoint {endpoint}: {innerException.Message}")
    {
    }
}

