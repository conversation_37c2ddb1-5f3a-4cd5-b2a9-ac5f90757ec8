﻿using BuildingBlocks.Exceptions;

namespace Applications.Exceptions;

public class MobiFoneApiDeserializationException : AppLogicException
{
    public override string ErrorCode { get; } = "MOBIFONE_003";

    public MobiFoneApiDeserializationException() : base("không thá»ƒ chuyá»ƒn Ä‘á»•i dá»¯ liá»‡u tá»« MobiFone API")
    {
    }

    public MobiFoneApiDeserializationException(string apiName) 
        : base($"không thá»ƒ chuyá»ƒn Ä‘á»•i dá»¯ liá»‡u tá»« MobiFone {apiName} API")
    {
    }

    public MobiFoneApiDeserializationException(string apiName, Exception innerException) 
        : base($"Lá»—i chuyá»ƒn Ä‘á»•i dá»¯ liá»‡u tá»« MobiFone {apiName} API: {innerException.Message}")
    {
    }
}

