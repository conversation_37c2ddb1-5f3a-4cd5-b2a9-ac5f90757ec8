using BuildingBlocks.Exceptions;

namespace Applications.Exceptions;

public class MobiFoneAuthenticationException : AppLogicException
{
    public override string ErrorCode { get; } = "MOBIFONE_004";

    public MobiFoneAuthenticationException() : base("Xác thực với MobiFone API thất bại")
    {
    }

    public MobiFoneAuthenticationException(string message) 
        : base($"Lỗi xác thực MobiFone API: {message}")
    {
    }

    public MobiFoneAuthenticationException(string username, string reason) 
        : base($"Xác thực thất bại cho user {username}: {reason}")
    {
    }
}
