﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AutoMapper" Version="14.0.0" />
        <PackageReference Include="FluentValidation" Version="12.0.0" />
        <PackageReference Include="Mapster" Version="7.4.0" />
        <PackageReference Include="MediatR" Version="12.5.0" />
        <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.5" />
        <PackageReference Include="Serilog" Version="4.2.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Core\Core.csproj" />
        <ProjectReference Include="..\Shared\Shared.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Features\Tokens\Handlers\" />
    </ItemGroup>

</Project>
