using Applications.Interfaces.Repositories;
using BuildingBlocks.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Applications.Features.MerchantInvoiceOrder.Commands;

/// <summary>
/// Command để xóa MerchantInvoiceOrder
/// </summary>
public record DeleteMerchantInvoiceOrderCommand(Guid Id) 
    : IRequest<Response<bool>>;

public class DeleteMerchantInvoiceOrderCommandHandler(
    IMerchantInvoiceOrderRepository merchantInvoiceOrderRepository,
    IInvoiceInfoRepository invoiceInfoRepository,
    ILogger<DeleteMerchantInvoiceOrderCommandHandler> logger)
    : IRequestHandler<DeleteMerchantInvoiceOrderCommand, Response<bool>>
{
    public async Task<Response<bool>> Handle(
        DeleteMerchantInvoiceOrderCommand request, 
        CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Deleting MerchantInvoiceOrder with Id: {Id}", request.Id);

            // Check if MerchantInvoiceOrder exists
            var order = await merchantInvoiceOrderRepository
                .AsQueryable()
                .FirstOrDefaultAsync(o => o.Id == request.Id, cancellationToken);

            if (order == null)
            {
                logger.LogWarning("MerchantInvoiceOrder not found with Id: {Id}", request.Id);
                return new Response<bool>
                {
                    Code = "404",
                    Message = "MerchantInvoiceOrder not found"
                };
            }

            // Check if there are any invoices associated with this order
            var hasInvoices = await invoiceInfoRepository
                .AsQueryable()
                .AnyAsync(i => i.MerchantInvoiceOrderId == request.Id, cancellationToken);

            if (hasInvoices)
            {
                logger.LogWarning("Cannot delete MerchantInvoiceOrder {Id} because it has associated invoices", request.Id);
                return new Response<bool>
                {
                    Code = "400",
                    Message = "Cannot delete MerchantInvoiceOrder because it has associated invoices"
                };
            }

            // Soft delete the order và deactivate
            order.IsDeleted = true;
            order.IsActive = false; // 🔥 Đảm bảo order không còn active
            order.UpdatedAt = DateTime.UtcNow;
            order.UpdatedBy = Guid.NewGuid(); // TODO: Get from current user context

            merchantInvoiceOrderRepository.Update(order);
            await merchantInvoiceOrderRepository.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Successfully deleted MerchantInvoiceOrder with Id: {Id}", request.Id);
            return new Response<bool>(true);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while deleting MerchantInvoiceOrder with Id: {Id}", request.Id);
            return new Response<bool>
            {
                Code = "500",
                Message = "An error occurred while deleting MerchantInvoiceOrder"
            };
        }
    }
}
