using Applications.DTOs.MerchantInvoiceOrder.UpdateAccountAndConfirm;
using Applications.Interfaces;
using Applications.Interfaces.Services;
using BuildingBlocks.Abstractions;
using Core.Enumerables;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Shared.Constants;

namespace Applications.Features.MerchantInvoiceOrder.Commands;

/// <summary>
/// Command to create account and confirm merchant invoice order in one transaction
/// </summary>
public record UpdateAccountAndConfirmCommand(
    Guid OrderId,
    UpdateAccountAndConfirmRequest Request) : IRequest<Response<UpdateAccountAndConfirmResponse>>;

/// <summary>
/// Validator for UpdateAccountAndConfirmCommand
/// </summary>
public class UpdateAccountAndConfirmCommandValidator : AbstractValidator<UpdateAccountAndConfirmCommand>
{
    public UpdateAccountAndConfirmCommandValidator()
    {
        RuleFor(x => x.OrderId)
            .NotEmpty()
            .WithMessage("OrderId is required");

        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request cannot be null");

        When(x => x.Request != null, () =>
        {
            RuleFor(x => x.Request.TaxNumber)
                .NotEmpty()
                .WithMessage("TaxNumber is required")
                .MaximumLength(50)
                .WithMessage("TaxNumber cannot exceed 50 characters");

            RuleFor(x => x.Request.InvoiceAccountUserName)
                .NotEmpty()
                .WithMessage("InvoiceAccountUserName is required")
                .MaximumLength(100)
                .WithMessage("InvoiceAccountUserName cannot exceed 100 characters");

            RuleFor(x => x.Request.InvoiceAccountPassword)
                .NotEmpty()
                .WithMessage("InvoiceAccountPassword is required")
                .MinimumLength(6)
                .WithMessage("InvoiceAccountPassword must be at least 6 characters")
                .MaximumLength(500)
                .WithMessage("InvoiceAccountPassword cannot exceed 500 characters");

            RuleFor(x => x.Request.EffectiveDate)
                .NotEmpty()
                .WithMessage("EffectiveDate is required");

            RuleFor(x => x.Request.ExpirationDate)
                .NotEmpty()
                .WithMessage("ExpirationDate is required")
                .GreaterThan(x => x.Request.EffectiveDate)
                .WithMessage("ExpirationDate must be after EffectiveDate");

            RuleFor(x => x.Request.MerchantBranchName)
                .NotEmpty()
                .WithMessage("MerchantBranchName is required")
                .MaximumLength(200)
                .WithMessage("MerchantBranchName cannot exceed 200 characters");

            RuleFor(x => x.Request.ConfirmNotes)
                .MaximumLength(500)
                .WithMessage("ConfirmNotes cannot exceed 500 characters");
        });
    }
}

/// <summary>
/// Handler for UpdateAccountAndConfirmCommand
/// </summary>
public class UpdateAccountAndConfirmCommandHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    ILogger<UpdateAccountAndConfirmCommandHandler> logger)
    : IRequestHandler<UpdateAccountAndConfirmCommand, Response<UpdateAccountAndConfirmResponse>>
{
    public async Task<Response<UpdateAccountAndConfirmResponse>> Handle(
        UpdateAccountAndConfirmCommand request,
        CancellationToken cancellationToken)
    {
        using var transaction = await ((DbContext)dbContext).Database.BeginTransactionAsync(cancellationToken);

        try
        {
            logger.LogInformation("Processing create account and confirm order for OrderId: {OrderId}", request.OrderId);

            // Get current partner ID
            var currentPartnerId = currentUserService.GetCurrentPartnerId()
                ?? throw new UnauthorizedAccessException("User is not authenticated or partner ID not found");

            // 1. Find and validate the order
            var order = await dbContext.MerchantInvoiceOrders
                .Include(o => o.MerchantBranchInvoiceAccount)
                .FirstOrDefaultAsync(o => o.Id == request.OrderId &&
                                        (o.MerchantBranchId == null || o.MerchantBranchInvoiceAccount.PartnerId == currentPartnerId),
                                   cancellationToken);

            if (order == null)
            {
                logger.LogWarning("MerchantInvoiceOrder with Id {OrderId} not found for partner {PartnerId}",
                    request.OrderId, currentPartnerId);
                return new Response<UpdateAccountAndConfirmResponse>
                {
                    Message = "MerchantInvoiceOrder not found",
                    Code = ErrorCodes.NOT_FOUND_DATA
                };
            }

            // 2. Check if order can be confirmed
            if (order.Status == MerchantInvoiceOrderStatus.COMPLETED)
            {
                logger.LogWarning("MerchantInvoiceOrder with Id {OrderId} is already completed", request.OrderId);
                return new Response<UpdateAccountAndConfirmResponse>
                {
                    Message = "MerchantInvoiceOrder is already completed",
                    Code = ErrorCodes.BAD_REQUEST_ERROR
                };
            }

            if (order.Status != null && order.Status != MerchantInvoiceOrderStatus.PENDING)
            {
                logger.LogWarning("MerchantInvoiceOrder with Id {OrderId} has invalid status {Status} for confirmation",
                    request.OrderId, order.Status);
                return new Response<UpdateAccountAndConfirmResponse>
                {
                    Message = "Only PENDING orders can be confirmed",
                    Code = ErrorCodes.BAD_REQUEST_ERROR
                };
            }

            // 3. Check if TaxNumber already exists
            var existingAccount = await dbContext.MerchantBranchInvoiceAccounts
                .FirstOrDefaultAsync(a => a.TaxNumber == request.Request.TaxNumber &&
                                         a.PartnerId == currentPartnerId,
                                   cancellationToken);

            if (existingAccount != null)
            {
                logger.LogWarning("TaxNumber {TaxNumber} already exists for partner {PartnerId}",
                    request.Request.TaxNumber, currentPartnerId);
                return new Response<UpdateAccountAndConfirmResponse>
                {
                    Message = "TaxNumber already exists",
                    Code = ErrorCodes.BAD_REQUEST_ERROR
                };
            }

            // 4. Create new MerchantBranchInvoiceAccount
            var accountEntity = new Core.Entities.MerchantBranchInvoiceAccount
            {
                Id = Guid.NewGuid(),
                TaxNumber = request.Request.TaxNumber,
                InvoiceAccountUserName = request.Request.InvoiceAccountUserName,
                InvoiceAccountPassword = request.Request.InvoiceAccountPassword,
                InvoiceAccountProvider = "MBF",
                EffectiveDate = request.Request.EffectiveDate,
                ExpirationDate = request.Request.ExpirationDate,
                MerchantBranchId = request.Request.MerchantBranchId,
                MerchantBranchName = request.Request.MerchantBranchName,
                PartnerId = currentPartnerId,
                IsActive = request.Request.IsActive,
                CreatedAt = DateTime.UtcNow
            };

            await dbContext.MerchantBranchInvoiceAccounts.AddAsync(accountEntity, cancellationToken);

            // 5. Update order with new MerchantBranchId and confirm it
            order.MerchantBranchId = request.Request.MerchantBranchId;
            order.Status = MerchantInvoiceOrderStatus.COMPLETED;
            order.UpdatedAt = DateTime.UtcNow;

            // 6. Save all changes
            await dbContext.SaveChangesAsync(cancellationToken);
            await transaction.CommitAsync(cancellationToken);

            logger.LogInformation("Successfully created account {AccountId} and confirmed order {OrderId} for partner {PartnerId}",
                accountEntity.Id, request.OrderId, currentPartnerId);

            // 7. Create response
            var response = new UpdateAccountAndConfirmResponse
            {
                OrderId = order.Id,
                AccountId = accountEntity.Id,
                MerchantBranchId = accountEntity.MerchantBranchId,
                Status = order.Status,
                ProcessedAt = DateTime.UtcNow,
                ConfirmNotes = request.Request.ConfirmNotes,
                Account = new AccountInfo
                {
                    TaxNumber = accountEntity.TaxNumber,
                    InvoiceAccountUserName = accountEntity.InvoiceAccountUserName,
                    MerchantBranchName = accountEntity.MerchantBranchName,
                    EffectiveDate = accountEntity.EffectiveDate,
                    ExpirationDate = accountEntity.ExpirationDate,
                    IsActive = accountEntity.IsActive
                }
            };

            return new Response<UpdateAccountAndConfirmResponse>
            {
                Data = response,
                Message = "Account created and order confirmed successfully",
                Code = "000"
            };
        }
        catch (UnauthorizedAccessException ex)
        {
            await transaction.RollbackAsync(cancellationToken);
            logger.LogError(ex, "Unauthorized access while processing order {OrderId}", request.OrderId);
            return new Response<UpdateAccountAndConfirmResponse>
            {
                Message = "Unauthorized access",
                Code = ErrorCodes.UNAUTHORIZED_ERROR
            };
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync(cancellationToken);
            logger.LogError(ex, "Error occurred while processing order {OrderId}", request.OrderId);
            return new Response<UpdateAccountAndConfirmResponse>
            {
                Message = "An error occurred while processing the request",
                Code = ErrorCodes.INTERNAL_SERVER_ERROR
            };
        }
    }
}
