using Applications.DTOs.MerchantInvoiceOrder.ConfirmMerchantInvoiceOrder;
using Applications.Interfaces;
using Applications.Interfaces.Services;
using BuildingBlocks.Abstractions;
using Core.Enumerables;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Shared.Constants;

namespace Applications.Features.MerchantInvoiceOrder.Commands;

/// <summary>
/// Command to confirm a merchant invoice order (change status from PENDING to COMPLETED)
/// </summary>
public record ConfirmMerchantInvoiceOrderCommand(
    Guid Id,
    ConfirmMerchantInvoiceOrderRequest Request) : IRequest<Response<ConfirmMerchantInvoiceOrderResponse>>;

/// <summary>
/// Validator for ConfirmMerchantInvoiceOrderCommand
/// </summary>
public class ConfirmMerchantInvoiceOrderCommandValidator : AbstractValidator<ConfirmMerchantInvoiceOrderCommand>
{
    public ConfirmMerchantInvoiceOrderCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("Id is required");

        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request cannot be null");

        When(x => x.Request != null, () =>
        {
            RuleFor(x => x.Request.Notes)
                .MaximumLength(500)
                .WithMessage("Notes cannot exceed 500 characters");
        });
    }
}

/// <summary>
/// Handler for ConfirmMerchantInvoiceOrderCommand
/// </summary>
public class ConfirmMerchantInvoiceOrderCommandHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    ILogger<ConfirmMerchantInvoiceOrderCommandHandler> logger)
    : IRequestHandler<ConfirmMerchantInvoiceOrderCommand, Response<ConfirmMerchantInvoiceOrderResponse>>
{
    public async Task<Response<ConfirmMerchantInvoiceOrderResponse>> Handle(
        ConfirmMerchantInvoiceOrderCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Confirming merchant invoice order with Id: {Id}", request.Id);

            // Get current partner ID
            var currentPartnerId = currentUserService.GetCurrentPartnerId()
                ?? throw new UnauthorizedAccessException("User is not authenticated or partner ID not found");

            // Find the order and verify ownership
            var order = await dbContext.MerchantInvoiceOrders
                .Include(o => o.MerchantBranchInvoiceAccount)
                .FirstOrDefaultAsync(o => o.Id == request.Id && 
                                        !o.IsDeleted &&
                                        o.MerchantBranchInvoiceAccount.PartnerId == currentPartnerId, 
                                   cancellationToken);

            if (order == null)
            {
                logger.LogWarning("MerchantInvoiceOrder with Id {Id} not found for partner {PartnerId}", 
                    request.Id, currentPartnerId);
                return new Response<ConfirmMerchantInvoiceOrderResponse>
                {
                    Message = "MerchantInvoiceOrder not found",
                    Code = ErrorCodes.NOT_FOUND_DATA
                };
            }

            // Check if order is already completed
            if (order.Status == MerchantInvoiceOrderStatus.COMPLETED)
            {
                logger.LogWarning("MerchantInvoiceOrder with Id {Id} is already completed", request.Id);
                return new Response<ConfirmMerchantInvoiceOrderResponse>
                {
                    Message = "MerchantInvoiceOrder is already completed",
                    Code = ErrorCodes.BAD_REQUEST_ERROR
                };
            }

            // Check if order status is PENDING
            if (order.Status != MerchantInvoiceOrderStatus.PENDING)
            {
                logger.LogWarning("MerchantInvoiceOrder with Id {Id} has invalid status {Status} for confirmation", 
                    request.Id, order.Status);
                return new Response<ConfirmMerchantInvoiceOrderResponse>
                {
                    Message = "Only PENDING orders can be confirmed",
                    Code = ErrorCodes.BAD_REQUEST_ERROR
                };
            }

            // Update status to COMPLETED
            order.Status = MerchantInvoiceOrderStatus.COMPLETED;
            order.UpdatedAt = DateTime.UtcNow;

            // Save changes
            await dbContext.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Successfully confirmed merchant invoice order with Id: {Id} for partner {PartnerId}",
                request.Id, currentPartnerId);

            // Create response
            var response = new ConfirmMerchantInvoiceOrderResponse
            {
                Id = order.Id,
                Status = order.Status,
                ConfirmedAt = order.UpdatedAt ?? DateTime.UtcNow,
                Notes = request.Request.Notes
            };

            return new Response<ConfirmMerchantInvoiceOrderResponse>
            {
                Data = response,
                Message = "MerchantInvoiceOrder confirmed successfully",
                Code = "000"
            };
        }
        catch (UnauthorizedAccessException ex)
        {
            logger.LogError(ex, "Unauthorized access while confirming merchant invoice order with Id: {Id}", request.Id);
            return new Response<ConfirmMerchantInvoiceOrderResponse>
            {
                Message = "Unauthorized access",
                Code = ErrorCodes.UNAUTHORIZED_ERROR
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while confirming merchant invoice order with Id: {Id}", request.Id);
            return new Response<ConfirmMerchantInvoiceOrderResponse>
            {
                Message = "An error occurred while confirming the merchant invoice order",
                Code = ErrorCodes.INTERNAL_SERVER_ERROR
            };
        }
    }
}
