using Applications.DTOs.MerchantInvoiceOrder;
using Applications.Interfaces;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Shared.Constants;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MerchantInvoiceOrder.Commands;

/// <summary>
/// Command to update an existing merchant invoice order
/// </summary>
public record UpdateMerchantInvoiceOrderCommand(
    Guid Id,
    MerchantInvoiceOrderDto Request) : IRequest<Response<object>>;

/// <summary>
/// Validator for UpdateMerchantInvoiceOrderCommand
/// </summary>
public class UpdateMerchantInvoiceOrderCommandValidator : AbstractValidator<UpdateMerchantInvoiceOrderCommand>
{
    public UpdateMerchantInvoiceOrderCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("Id is required");

        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request cannot be null");

        When(x => x.Request != null, () =>
        {
            RuleFor(x => x.Request.MerchantBranchId)
                .NotEmpty()
                .WithMessage("MerchantBranchId is required");

            RuleFor(x => x.Request.TotalInvoiceQuantity)
                .GreaterThan(0)
                .WithMessage("TotalInvoiceQuantity must be greater than 0");

            RuleFor(x => x.Request.EffectiveDateFrom)
                .NotEmpty()
                .WithMessage("EffectiveDateFrom is required");

            RuleFor(x => x.Request.EffectiveDateTo)
                .NotEmpty()
                .WithMessage("EffectiveDateTo is required")
                .GreaterThan(x => x.Request.EffectiveDateFrom)
                .WithMessage("EffectiveDateTo must be after EffectiveDateFrom");

            RuleFor(x => x.Request.OrderReference)
                .MaximumLength(100)
                .WithMessage("OrderReference cannot exceed 100 characters");

            RuleFor(x => x.Request.Description)
                .MaximumLength(500)
                .WithMessage("Description cannot exceed 500 characters");
        });
    }
}

/// <summary>
/// Handler for UpdateMerchantInvoiceOrderCommand
/// </summary>
public class UpdateMerchantInvoiceOrderHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    ILogger<UpdateMerchantInvoiceOrderHandler> logger)
    : IRequestHandler<UpdateMerchantInvoiceOrderCommand, Response<object>>
{
    public async Task<Response<object>> Handle(
        UpdateMerchantInvoiceOrderCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Updating merchant invoice order with Id: {Id}", request.Id);

            // Get current partner ID
            var currentPartnerId = currentUserService.GetCurrentPartnerId()
                ?? throw new UnauthorizedAccessException("User is not authenticated or partner ID not found");

            // Find existing entity and ensure it belongs to current partner
            var existingEntity = await dbContext.MerchantInvoiceOrders
                .Include(o => o.MerchantBranchInvoiceAccount)
                .FirstOrDefaultAsync(x => x.Id == request.Id &&
                                         x.MerchantBranchInvoiceAccount.PartnerId == currentPartnerId, cancellationToken);

            if (existingEntity == null)
            {
                logger.LogWarning("Merchant invoice order with Id {Id} not found for partner {PartnerId}",
                    request.Id, currentPartnerId);
                return new Response<object>
                {
                    Code = ErrorCodes.NOT_FOUND_DATA,
                    Message = "Merchant invoice order not found"
                };
            }

            // Validate MerchantBranchId if changed
            if (existingEntity.MerchantBranchId != request.Request.MerchantBranchId)
            {
                var merchantBranchExists = await dbContext.MerchantBranchInvoiceAccounts
                    .FirstOrDefaultAsync(mb => mb.MerchantBranchId == request.Request.MerchantBranchId &&
                                              mb.PartnerId == currentPartnerId, cancellationToken);

                if (merchantBranchExists == null)
                {
                    logger.LogWarning("MerchantBranchId {MerchantBranchId} not found for partner {PartnerId}",
                        request.Request.MerchantBranchId, currentPartnerId);
                    return new Response<object>
                    {
                        Code = ErrorCodes.NOT_FOUND_DATA,
                        Message = "MerchantBranch not found or does not belong to current partner"
                    };
                }
            }

            // Update entity properties
            existingEntity.MerchantBranchId = request.Request.MerchantBranchId;
            existingEntity.TotalInvoiceQuantity = request.Request.TotalInvoiceQuantity;
            existingEntity.EffectiveDateFrom = request.Request.EffectiveDateFrom;
            existingEntity.EffectiveDateTo = request.Request.EffectiveDateTo;
            existingEntity.Description = request.Request.Description;
            existingEntity.OrderReference = request.Request.OrderReference;
            existingEntity.IsActive = request.Request.IsActive;
            // existingEntity.Status = request.Request.Status;
            existingEntity.UpdatedAt = DateTime.UtcNow;

            // Save changes
            await dbContext.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Successfully updated merchant invoice order with Id: {Id} for partner {PartnerId}",
                request.Id, currentPartnerId);

            return new Response<object>(new { Status = true });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating merchant invoice order with Id: {Id}", request.Id);
            return new Response<object>
            {
                Code = ErrorCodes.INTERNAL_SERVER_ERROR,
                Message = "An error occurred while updating the merchant invoice order"
            };
        }
    }
}
