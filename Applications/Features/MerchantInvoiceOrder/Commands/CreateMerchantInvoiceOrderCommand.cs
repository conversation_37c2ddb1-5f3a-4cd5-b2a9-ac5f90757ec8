using Applications.DTOs.MerchantInvoiceOrder;
using Applications.Interfaces;
using Applications.Interfaces.Services;
using FluentValidation;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Shared.Constants;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MerchantInvoiceOrder.Commands;

/// <summary>
/// Command to create a new merchant invoice order
/// </summary>
public record CreateMerchantInvoiceOrderCommand(
    MerchantInvoiceOrderDto Request) : IRequest<Response<object?>>;

/// <summary>
/// Validator for CreateMerchantInvoiceOrderCommand
/// </summary>
public class CreateMerchantInvoiceOrderCommandValidator : AbstractValidator<CreateMerchantInvoiceOrderCommand>
{
    public CreateMerchantInvoiceOrderCommandValidator()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request cannot be null");

        When(x => x.Request != null, () =>
        {
            RuleFor(x => x.Request.MerchantBranchId)
                .NotEmpty()
                .WithMessage("MerchantBranchId is required");

            RuleFor(x => x.Request.TotalInvoiceQuantity)
                .GreaterThan(0)
                .WithMessage("TotalInvoiceQuantity must be greater than 0");

            RuleFor(x => x.Request.EffectiveDateFrom)
                .NotEmpty()
                .WithMessage("EffectiveDateFrom is required");

            RuleFor(x => x.Request.EffectiveDateTo)
                .NotEmpty()
                .WithMessage("EffectiveDateTo is required")
                .GreaterThan(x => x.Request.EffectiveDateFrom)
                .WithMessage("EffectiveDateTo must be after EffectiveDateFrom");

            RuleFor(x => x.Request.OrderReference)
                .MaximumLength(100)
                .WithMessage("OrderReference cannot exceed 100 characters");

            RuleFor(x => x.Request.Description)
                .MaximumLength(500)
                .WithMessage("Description cannot exceed 500 characters");
        });
    }
}

/// <summary>
/// Handler for CreateMerchantInvoiceOrderCommand
/// </summary>
public class CreateMerchantInvoiceOrderHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    ILogger<CreateMerchantInvoiceOrderHandler> logger)
    : IRequestHandler<CreateMerchantInvoiceOrderCommand, Response<object?>>
{
    public async Task<Response<object?>> Handle(
        CreateMerchantInvoiceOrderCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Creating merchant invoice order for MerchantBranchId: {MerchantBranchId}",
                request.Request.MerchantBranchId);

            // Get current partner ID
            var currentPartnerId = currentUserService.GetCurrentPartnerId()
                ?? throw new UnauthorizedAccessException("User is not authenticated or partner ID not found");

            // Validate MerchantBranchId exists and belongs to current partner
            var merchantBranchExists = await dbContext.MerchantBranchInvoiceAccounts
                .FirstOrDefaultAsync(mb => mb.MerchantBranchId == request.Request.MerchantBranchId &&
                                          mb.PartnerId == currentPartnerId, cancellationToken);

            if (merchantBranchExists == null)
            {
                logger.LogWarning("MerchantBranchId {MerchantBranchId} not found for partner {PartnerId}",
                    request.Request.MerchantBranchId, currentPartnerId);
                return new Response<object?>
                {
                    Code = ErrorCodes.NOT_FOUND_DATA,
                    Message = "MerchantBranch not found or does not belong to current partner"
                };
            }

            // Validate no active order exists for this MerchantBranch
            var currentDate = DateTime.UtcNow;
            var existingActiveOrder = await dbContext.MerchantInvoiceOrders
                .Where(o => o.MerchantBranchId == request.Request.MerchantBranchId &&
                           o.IsActive &&
                           o.EffectiveDateTo >= currentDate)
                .FirstOrDefaultAsync(cancellationToken);

            if (existingActiveOrder != null)
            {
                logger.LogWarning("MerchantBranchId {MerchantBranchId} already has an active order {OrderId} valid until {EffectiveDateTo}",
                    request.Request.MerchantBranchId, existingActiveOrder.Id, existingActiveOrder.EffectiveDateTo);
                return new Response<object?>
                {
                    Code = ErrorCodes.BAD_REQUEST_ERROR,
                    Message = $"MerchantBranch already has an active order (ID: {existingActiveOrder.Id}) valid until {existingActiveOrder.EffectiveDateTo:dd/MM/yyyy}"
                };
            }

            // Use Adapter to convert to entity
            var entity = request.Request.Adapt<Core.Entities.MerchantInvoiceOrder>();

            // Save to database
            await dbContext.MerchantInvoiceOrders.AddAsync(entity, cancellationToken);
            await dbContext.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Successfully created merchant invoice order with Id: {Id} for MerchantBranchId: {MerchantBranchId}",
                entity.Id, request.Request.MerchantBranchId);

            return new Response<object?>(new { Id = entity.Id });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error creating merchant invoice order for MerchantBranchId: {MerchantBranchId}",
                request.Request.MerchantBranchId);
            return new Response<object?>
            {
                Code = ErrorCodes.INTERNAL_SERVER_ERROR,
                Message = "An error occurred while creating the merchant invoice order"
            };
        }
    }
}
