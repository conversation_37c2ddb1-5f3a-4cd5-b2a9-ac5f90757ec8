using Applications.Interfaces.Repositories;
using BuildingBlocks.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Applications.Features.MerchantInvoiceOrder.Commands;

/// <summary>
/// Command để deactivate MerchantInvoiceOrder (không delete, chỉ set IsActive = false)
/// </summary>
public record DeactivateMerchantInvoiceOrderCommand(Guid Id) 
    : IRequest<Response<bool>>;

public class DeactivateMerchantInvoiceOrderCommandHandler(
    IMerchantInvoiceOrderRepository merchantInvoiceOrderRepository,
    ILogger<DeactivateMerchantInvoiceOrderCommandHandler> logger)
    : IRequestHandler<DeactivateMerchantInvoiceOrderCommand, Response<bool>>
{
    public async Task<Response<bool>> Handle(
        DeactivateMerchantInvoiceOrderCommand request, 
        CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Deactivating MerchantInvoiceOrder with Id: {Id}", request.Id);

            // Get the order
            var order = await merchantInvoiceOrderRepository
                .AsQueryable()
                .FirstOrDefaultAsync(o => o.Id == request.Id && !o.IsDeleted, cancellationToken);

            if (order == null)
            {
                logger.LogWarning("MerchantInvoiceOrder not found with Id: {Id}", request.Id);
                return new Response<bool>
                {
                    Code = "404",
                    Message = "MerchantInvoiceOrder not found"
                };
            }

            if (!order.IsActive)
            {
                logger.LogInformation("MerchantInvoiceOrder {Id} is already inactive", request.Id);
                return new Response<bool>
                {
                    Code = "400",
                    Message = "MerchantInvoiceOrder is already inactive"
                };
            }

            // Deactivate the order
            order.IsActive = false;
            order.UpdatedAt = DateTime.UtcNow;
            order.UpdatedBy = Guid.NewGuid(); // TODO: Get from current user context

            merchantInvoiceOrderRepository.Update(order);
            await merchantInvoiceOrderRepository.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Successfully deactivated MerchantInvoiceOrder with Id: {Id} for MerchantBranchId: {MerchantBranchId}", 
                request.Id, order.MerchantBranchId);
            return new Response<bool>(true);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while deactivating MerchantInvoiceOrder with Id: {Id}", request.Id);
            return new Response<bool>
            {
                Code = "500",
                Message = "An error occurred while deactivating MerchantInvoiceOrder"
            };
        }
    }
}
