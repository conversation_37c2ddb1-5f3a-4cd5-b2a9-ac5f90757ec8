using Applications.DTOs.MerchantInvoiceOrder.IncreaseInvoiceQuantity;
using Applications.Interfaces;
using BuildingBlocks.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Applications.Features.MerchantInvoiceOrder.Commands;

/// <summary>
/// Command để tăng số lượng hóa đơn khả dụng cho một chi nhánh merchant
/// </summary>
public record IncreaseInvoiceQuantityCommand(IncreaseInvoiceQuantityRequest Request)
    : IRequest<Response<IncreaseInvoiceQuantityResponse>>;

public class IncreaseInvoiceQuantityCommandHandler(
    IApplicationDbContext dbContext,
    ILogger<IncreaseInvoiceQuantityCommandHandler> logger)
    : IRequestHandler<IncreaseInvoiceQuantityCommand, Response<IncreaseInvoiceQuantityResponse>>
{
    public async Task<Response<IncreaseInvoiceQuantityResponse>> Handle(
        IncreaseInvoiceQuantityCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            var req = request.Request;

            logger.LogInformation("Increasing invoice quantity for MerchantBranchId: {MerchantBranchId}, Quantity: {Quantity}",
                req.MerchantBranchId, req.Quantity);

            // 1. Validate MerchantBranchId exists in MerchantBranchInvoiceAccount
            var merchantBranchExists = await dbContext.MerchantBranchInvoiceAccounts
                .FirstOrDefaultAsync(mb => mb.MerchantBranchId == req.MerchantBranchId, cancellationToken);

            if (merchantBranchExists == null)
            {
                logger.LogWarning("MerchantBranchId {MerchantBranchId} not found in MerchantBranchInvoiceAccount",
                    req.MerchantBranchId);
                return new Response<IncreaseInvoiceQuantityResponse>
                {
                    Code = "404",
                    Message = "MerchantBranchId must exist in MerchantBranchInvoiceAccount"
                };
            }

            // 2. Tìm active order hiện tại cho MerchantBranch
            var currentDate = DateTime.UtcNow;
            var activeOrder = await dbContext.MerchantInvoiceOrders
                .Where(o => o.MerchantBranchId == req.MerchantBranchId &&
                           o.IsActive &&
                           o.EffectiveDateTo >= currentDate)
                .OrderByDescending(o => o.CreatedAt)
                .FirstOrDefaultAsync(cancellationToken);

            if (activeOrder == null)
            {
                logger.LogWarning("No active MerchantInvoiceOrder found for MerchantBranchId: {MerchantBranchId}",
                    req.MerchantBranchId);
                return new Response<IncreaseInvoiceQuantityResponse>
                {
                    Code = "404",
                    Message = "No active invoice order found for this merchant branch"
                };
            }

            // 3. Cập nhật số lượng hóa đơn
            var oldRemainingQuantity = activeOrder.RemainingInvoiceQuantity;
            var oldTotalQuantity = activeOrder.TotalInvoiceQuantity;

            activeOrder.TotalInvoiceQuantity += req.Quantity;
            activeOrder.RemainingInvoiceQuantity += req.Quantity;
            activeOrder.UpdatedAt = DateTime.UtcNow;
            activeOrder.UpdatedBy = Guid.NewGuid(); // TODO: Get from current user context

            // Ensure DateTime fields have UTC Kind để tránh PostgreSQL error
            if (activeOrder.EffectiveDateFrom.Kind != DateTimeKind.Utc)
            {
                activeOrder.EffectiveDateFrom = DateTime.SpecifyKind(activeOrder.EffectiveDateFrom, DateTimeKind.Utc);
            }
            if (activeOrder.EffectiveDateTo.Kind != DateTimeKind.Utc)
            {
                activeOrder.EffectiveDateTo = DateTime.SpecifyKind(activeOrder.EffectiveDateTo, DateTimeKind.Utc);
            }

            // 4. Save changes
            dbContext.MerchantInvoiceOrders.Update(activeOrder);
            await dbContext.SaveChangesAsync(cancellationToken);

            // 5. Create response
            var response = new IncreaseInvoiceQuantityResponse
            {
                MerchantBranchId = req.MerchantBranchId,
                QuantityAdded = req.Quantity,
                NewRemainingQuantity = activeOrder.RemainingInvoiceQuantity,
                NewTotalQuantity = activeOrder.TotalInvoiceQuantity,
                UpdatedOrderId = activeOrder.Id,
                UpdatedAt = activeOrder.UpdatedAt ?? DateTime.UtcNow,
                Description = req.Description
            };

            logger.LogInformation("Successfully increased invoice quantity for MerchantBranchId: {MerchantBranchId}. " +
                                 "Added: {Added}, New Total: {NewTotal}, New Remaining: {NewRemaining}",
                req.MerchantBranchId, req.Quantity, activeOrder.TotalInvoiceQuantity, activeOrder.RemainingInvoiceQuantity);

            return new Response<IncreaseInvoiceQuantityResponse>(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while increasing invoice quantity for MerchantBranchId: {MerchantBranchId}",
                request.Request.MerchantBranchId);
            return new Response<IncreaseInvoiceQuantityResponse>
            {
                Code = "500",
                Message = "An error occurred while increasing invoice quantity"
            };
        }
    }
}
