using Applications.DTOs.MerchantInvoiceOrder.GetMerchantInvoiceOrderDetail;
using Applications.Interfaces;
using Applications.Interfaces.Services;
using BuildingBlocks.Abstractions;
using Mapster;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Applications.Features.MerchantInvoiceOrder.Queries;

/// <summary>
/// Query để lấy chi tiết MerchantInvoiceOrder
/// </summary>
public record GetMerchantInvoiceOrderDetailQuery(Guid Id) 
    : IRequest<Response<GetMerchantInvoiceOrderDetailResponse>>;

public class GetMerchantInvoiceOrderDetailQueryHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    ILogger<GetMerchantInvoiceOrderDetailQueryHandler> logger)
    : IRequestHandler<GetMerchantInvoiceOrderDetailQuery, Response<GetMerchantInvoiceOrderDetailResponse>>
{
    public async Task<Response<GetMerchantInvoiceOrderDetailResponse>> Handle(
        GetMerchantInvoiceOrderDetailQuery request, 
        CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Getting MerchantInvoiceOrder detail for Id: {Id}", request.Id);

            // Get current partner ID
            var currentPartnerId = currentUserService.GetCurrentPartnerId()
                ?? throw new UnauthorizedAccessException("User is not authenticated or partner ID not found");

            // Lấy MerchantInvoiceOrder với includes
            var order = await dbContext.MerchantInvoiceOrders
                .Include(o => o.MerchantBranchInvoiceAccount)
                .FirstOrDefaultAsync(o => o.Id == request.Id
                    && o.MerchantBranchInvoiceAccount.PartnerId == currentPartnerId, cancellationToken);

            if (order == null)
            {
                logger.LogWarning("MerchantInvoiceOrder not found with Id: {Id}", request.Id);
                return new Response<GetMerchantInvoiceOrderDetailResponse>
                {
                    Code = "404",
                    Message = "MerchantInvoiceOrder not found"
                };
            }

            // Đếm số lượng hóa đơn đã sử dụng
            var usedInvoiceCount = await dbContext.InvoiceInfos
                .CountAsync(i => i.MerchantInvoiceOrderId == request.Id, cancellationToken: cancellationToken);

            // Kiểm tra trạng thái hết hạn
            var isExpired = order.EffectiveDateTo < DateTime.UtcNow;

            // Map to response
            var response = new GetMerchantInvoiceOrderDetailResponse
            {
                Id = order.Id,
                MerchantBranchId = order.MerchantBranchId,
                TotalInvoiceQuantity = order.TotalInvoiceQuantity,
                RemainingInvoiceQuantity = order.RemainingInvoiceQuantity,
                EffectiveDateFrom = order.EffectiveDateFrom,
                EffectiveDateTo = order.EffectiveDateTo,
                Description = order.Description,
                IsActive = order.IsActive,
                OrderReference = order.OrderReference,
                CreatedAt = order.CreatedAt,
                UpdatedAt = order.UpdatedAt ?? order.CreatedAt,
                UsedInvoiceCount = usedInvoiceCount,
                IsExpired = isExpired,
                MerchantBranch = order.MerchantBranchInvoiceAccount != null ? new MerchantBranchDetail
                {
                    Id = order.MerchantBranchInvoiceAccount.Id,
                    BranchName = order.MerchantBranchInvoiceAccount.MerchantBranchName,
                    BranchCode = order.MerchantBranchInvoiceAccount.TaxNumber,
                } : null
            };

            logger.LogInformation("Successfully retrieved MerchantInvoiceOrder detail for Id: {Id}", request.Id);
            return new Response<GetMerchantInvoiceOrderDetailResponse>(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting MerchantInvoiceOrder detail for Id: {Id}", request.Id);
            return new Response<GetMerchantInvoiceOrderDetailResponse>
            {
                Code = "500",
                Message = "An error occurred while retrieving MerchantInvoiceOrder detail"
            };
        }
    }
}
