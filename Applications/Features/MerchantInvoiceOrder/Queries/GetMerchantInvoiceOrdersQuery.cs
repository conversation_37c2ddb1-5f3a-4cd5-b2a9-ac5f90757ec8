using Applications.DTOs.MerchantInvoiceOrder;
using Applications.Interfaces;
using Applications.Interfaces.Services;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Shared.Constants;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MerchantInvoiceOrder.Queries;

/// <summary>
/// Query to get all merchant invoice orders
/// </summary>
public record GetAllMerchantInvoiceOrdersQuery() : IRequest<Response<List<MerchantInvoiceOrderResponseDto>>>;

/// <summary>
/// Handler for GetAllMerchantInvoiceOrdersQuery
/// </summary>
public class GetAllMerchantInvoiceOrdersHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    ILogger<GetAllMerchantInvoiceOrdersHandler> logger)
    : IRequestHandler<GetAllMerchantInvoiceOrdersQuery, Response<List<MerchantInvoiceOrderResponseDto>>>
{
    public async Task<Response<List<MerchantInvoiceOrderResponseDto>>> Handle(
        GetAllMerchantInvoiceOrdersQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Retrieving all merchant invoice orders");

            // Get current partner ID
            var currentPartnerId = currentUserService.GetCurrentPartnerId()
                ?? throw new UnauthorizedAccessException("User is not authenticated or partner ID not found");

            // Filter by current partner ID through MerchantBranchInvoiceAccount
            // Include orders with null MerchantBranchId or orders belonging to current partner
            var entities = await dbContext.MerchantInvoiceOrders
                .Include(o => o.MerchantBranchInvoiceAccount)
                .Where(o => o.MerchantBranchId == null || o.MerchantBranchInvoiceAccount.PartnerId == currentPartnerId)
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync(cancellationToken);

            logger.LogInformation("Found {Count} merchant invoice orders for partner {PartnerId}",
                entities.Count, currentPartnerId);

            var responses = entities.Adapt<List<MerchantInvoiceOrderResponseDto>>();

            return new Response<List<MerchantInvoiceOrderResponseDto>>(responses);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving merchant invoice orders");
            return new Response<List<MerchantInvoiceOrderResponseDto>>
            {
                Code = ErrorCodes.INTERNAL_SERVER_ERROR,
                Message = "An error occurred while retrieving merchant invoice orders"
            };
        }
    }
}
