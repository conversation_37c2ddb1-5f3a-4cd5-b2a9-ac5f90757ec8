using Applications.DTOs.MerchantInvoiceOrder;
using Applications.Interfaces;
using Applications.Interfaces.Services;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Shared.Constants;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MerchantInvoiceOrder.Queries;

/// <summary>
/// Query to get a merchant invoice order by ID
/// </summary>
public record GetMerchantInvoiceOrderByIdQuery(Guid Id) : IRequest<Response<MerchantInvoiceOrderResponseDto>>;

/// <summary>
/// Handler for GetMerchantInvoiceOrderByIdQuery
/// </summary>
public class GetMerchantInvoiceOrderByIdHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    ILogger<GetMerchantInvoiceOrderByIdHandler> logger)
    : IRequestHandler<GetMerchantInvoiceOrderByIdQuery, Response<MerchantInvoiceOrderResponseDto>>
{
    public async Task<Response<MerchantInvoiceOrderResponseDto>> Handle(
        GetMerchantInvoiceOrderByIdQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Retrieving merchant invoice order with Id: {Id}", request.Id);

            // Get current partner ID
            var currentPartnerId = currentUserService.GetCurrentPartnerId()
                ?? throw new UnauthorizedAccessException("User is not authenticated or partner ID not found");

            // Filter by current partner ID and specific ID
            var entity = await dbContext.MerchantInvoiceOrders
                .Include(o => o.MerchantBranchInvoiceAccount)
                .FirstOrDefaultAsync(x => x.Id == request.Id && 
                                         x.MerchantBranchInvoiceAccount.PartnerId == currentPartnerId, cancellationToken);

            if (entity == null)
            {
                logger.LogWarning("Merchant invoice order with Id {Id} not found for partner {PartnerId}", 
                    request.Id, currentPartnerId);
                return new Response<MerchantInvoiceOrderResponseDto>
                {
                    Code = ErrorCodes.NOT_FOUND_DATA,
                    Message = "Merchant invoice order not found"
                };
            }

            logger.LogInformation("Successfully retrieved merchant invoice order with Id: {Id} for partner {PartnerId}", 
                request.Id, currentPartnerId);

            var response = entity.Adapt<MerchantInvoiceOrderResponseDto>();

            return new Response<MerchantInvoiceOrderResponseDto>(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving merchant invoice order with Id: {Id}", request.Id);
            return new Response<MerchantInvoiceOrderResponseDto>
            {
                Code = ErrorCodes.INTERNAL_SERVER_ERROR,
                Message = "An error occurred while retrieving the merchant invoice order"
            };
        }
    }
}
