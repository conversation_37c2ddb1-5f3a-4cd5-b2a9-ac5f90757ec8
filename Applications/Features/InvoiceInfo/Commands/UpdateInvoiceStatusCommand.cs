using Applications.DTOs.InvoiceInfo.UpdateInvoiceStatus;
using Applications.Interfaces.Repositories;
using BuildingBlocks.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Applications.Features.InvoiceInfo.Commands;

/// <summary>
/// Command để cập nhật trạng thái CQT và trạng thái hóa đơn
/// </summary>
public record UpdateInvoiceStatusCommand(Guid Id, UpdateInvoiceStatusRequest Request) 
    : IRequest<Response<UpdateInvoiceStatusResponse>>;

public class UpdateInvoiceStatusCommandHandler(
    IInvoiceInfoRepository invoiceInfoRepository,
    ILogger<UpdateInvoiceStatusCommandHandler> logger)
    : IRequestHandler<UpdateInvoiceStatusCommand, Response<UpdateInvoiceStatusResponse>>
{
    public async Task<Response<UpdateInvoiceStatusResponse>> Handle(
        UpdateInvoiceStatusCommand request, 
        CancellationToken cancellationToken)
    {
        try
        {
            var req = request.Request;
            
            logger.LogInformation("Updating InvoiceInfo status for Id: {Id}", request.Id);

            // Validate that at least one field is being updated
            if (!req.CqtInvoiceStatus.HasValue &&
                !req.InvoiceStatus.HasValue &&
                string.IsNullOrWhiteSpace(req.ErrorMessage) &&
                string.IsNullOrWhiteSpace(req.Notes))
            {
                return new Response<UpdateInvoiceStatusResponse>
                {
                    Code = "400",
                    Message = "At least one field must be provided for update"
                };
            }

            // Get existing InvoiceInfo
            var invoice = await invoiceInfoRepository
                .AsQueryable()
                .FirstOrDefaultAsync(i => i.Id == request.Id, cancellationToken);

            if (invoice == null)
            {
                logger.LogWarning("InvoiceInfo not found with Id: {Id}", request.Id);
                return new Response<UpdateInvoiceStatusResponse>
                {
                    Code = "404",
                    Message = "InvoiceInfo not found"
                };
            }

            // Update fields if provided
            if (req.CqtInvoiceStatus.HasValue)
            {
                invoice.CqtInvoiceStatus = req.CqtInvoiceStatus.Value;
            }

            if (req.InvoiceStatus.HasValue)
            {
                invoice.InvoiceStatus = req.InvoiceStatus.Value;
            }

            if (!string.IsNullOrWhiteSpace(req.ErrorMessage))
            {
                invoice.ErrorMessage = req.ErrorMessage;
            }

            if (!string.IsNullOrWhiteSpace(req.Notes))
            {
                invoice.Notes = req.Notes;
            }

            // Update audit fields
            invoice.UpdatedAt = DateTime.UtcNow;
            invoice.UpdatedBy = Guid.NewGuid(); // TODO: Get from current user context

            // Save changes
            invoiceInfoRepository.Update(invoice);
            await invoiceInfoRepository.SaveChangesAsync(cancellationToken);

            // Map to response
            var response = new UpdateInvoiceStatusResponse
            {
                Id = invoice.Id,
                InvoiceId = invoice.InvoiceId,
                CqtInvoiceStatus = invoice.CqtInvoiceStatus,
                InvoiceStatus = invoice.InvoiceStatus,
                ErrorMessage = invoice.ErrorMessage,
                Notes = invoice.Notes,
                UpdatedAt = invoice.UpdatedAt ?? DateTime.UtcNow
            };

            logger.LogInformation("Successfully updated InvoiceInfo status for Id: {Id}", request.Id);
            return new Response<UpdateInvoiceStatusResponse>(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while updating InvoiceInfo status for Id: {Id}", request.Id);
            return new Response<UpdateInvoiceStatusResponse>
            {
                Code = "500",
                Message = "An error occurred while updating InvoiceInfo status"
            };
        }
    }
}
