using Applications.DTOs.InvoiceInfo.CreateInvoice;
using Applications.Interfaces;
using Applications.Interfaces.Services;
using BuildingBlocks.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace Applications.Features.InvoiceInfo.Commands;

/// <summary>
/// Command để tạo InvoiceInfo mới
/// </summary>
public record CreateInvoiceCommand(CreateInvoiceRequest Request)
    : IRequest<Response<CreateInvoiceResponse>>;

public class CreateInvoiceCommandHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    ILogger<CreateInvoiceCommandHandler> logger)
    : IRequestHandler<CreateInvoiceCommand, Response<CreateInvoiceResponse>>
{
    public async Task<Response<CreateInvoiceResponse>> Handle(
        CreateInvoiceCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            var req = request.Request;

            logger.LogInformation("Creating InvoiceInfo for InvoiceId: {InvoiceId}", req.InvoiceId);

            // Validate InvoiceId is unique
            var existingInvoice = await dbContext.InvoiceInfos
                .AnyAsync(i => i.InvoiceId == req.InvoiceId, cancellationToken);

            if (existingInvoice)
            {
                logger.LogWarning("InvoiceId {InvoiceId} already exists", req.InvoiceId);
                return new Response<CreateInvoiceResponse>
                {
                    Code = "409",
                    Message = "InvoiceId already exists"
                };
            }

            // Validate MerchantInvoiceOrderId exists and is active
            var merchantInvoiceOrder = await dbContext.MerchantInvoiceOrders
                .FirstOrDefaultAsync(o => o.Id == req.MerchantInvoiceOrderId && o.IsActive, cancellationToken);

            if (merchantInvoiceOrder == null)
            {
                logger.LogWarning("MerchantInvoiceOrderId {MerchantInvoiceOrderId} not found or inactive",
                    req.MerchantInvoiceOrderId);
                return new Response<CreateInvoiceResponse>
                {
                    Code = "404",
                    Message = "MerchantInvoiceOrderId not found or inactive"
                };
            }

            // Check if MerchantInvoiceOrder has remaining invoices
            if (merchantInvoiceOrder.RemainingInvoiceQuantity <= 0)
            {
                logger.LogWarning("MerchantInvoiceOrderId {MerchantInvoiceOrderId} has no remaining invoices",
                    req.MerchantInvoiceOrderId);
                return new Response<CreateInvoiceResponse>
                {
                    Code = "400",
                    Message = "MerchantInvoiceOrder has no remaining invoices"
                };
            }

            // Check if MerchantInvoiceOrder is still valid (not expired)
            var currentDate = DateTime.UtcNow;
            if (merchantInvoiceOrder.EffectiveDateTo < currentDate)
            {
                logger.LogWarning("MerchantInvoiceOrderId {MerchantInvoiceOrderId} has expired",
                    req.MerchantInvoiceOrderId);
                return new Response<CreateInvoiceResponse>
                {
                    Code = "400",
                    Message = "MerchantInvoiceOrder has expired"
                };
            }

            // Create new InvoiceInfo
            var newInvoice = new Core.Entities.InvoiceInfo
            {
                Id = Guid.NewGuid(),
                InvoiceId = req.InvoiceId,
                MerchantInvoiceOrderId = req.MerchantInvoiceOrderId,
                CqtInvoiceStatus = req.CqtInvoiceStatus,
                InvoiceStatus = req.InvoiceStatus,
                RequestData = JsonSerializer.Serialize(req.RequestData),
                ResponseData = JsonSerializer.Serialize(req.ResponseData),
                InvoiceNumber = req.InvoiceNumber,
                InvoiceSeries = req.InvoiceSeries,
                TotalAmount = req.TotalAmount,
                TaxAmount = req.TaxAmount,
                CustomerName = req.CustomerName,
                CustomerTaxCode = req.CustomerTaxCode,
                ErrorMessage = req.ErrorMessage,
                Notes = req.Notes,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = currentUserService.GetCurrentPartnerId() ?? Guid.Empty
            };

            // Save InvoiceInfo
            await dbContext.InvoiceInfos.AddAsync(newInvoice, cancellationToken);

            // Update MerchantInvoiceOrder remaining quantity
            merchantInvoiceOrder.RemainingInvoiceQuantity--;
            merchantInvoiceOrder.UpdatedAt = DateTime.UtcNow;
            merchantInvoiceOrder.UpdatedBy = currentUserService.GetCurrentPartnerId() ?? Guid.Empty;

            // Explicit update theo pattern EMR
            dbContext.MerchantInvoiceOrders.Update(merchantInvoiceOrder);

            // Save all changes
            await dbContext.SaveChangesAsync(cancellationToken);

            // Map to response
            var response = new CreateInvoiceResponse
            {
                Id = newInvoice.Id,
                InvoiceId = newInvoice.InvoiceId,
                MerchantInvoiceOrderId = newInvoice.MerchantInvoiceOrderId,
                CqtInvoiceStatus = newInvoice.CqtInvoiceStatus,
                InvoiceStatus = newInvoice.InvoiceStatus,
                InvoiceNumber = newInvoice.InvoiceNumber,
                InvoiceSeries = newInvoice.InvoiceSeries,
                TotalAmount = newInvoice.TotalAmount,
                TaxAmount = newInvoice.TaxAmount,
                CustomerName = newInvoice.CustomerName,
                CustomerTaxCode = newInvoice.CustomerTaxCode,
                ErrorMessage = newInvoice.ErrorMessage,
                Notes = newInvoice.Notes,
                CreatedAt = newInvoice.CreatedAt
            };

            logger.LogInformation("Successfully created InvoiceInfo with Id: {Id} for InvoiceId: {InvoiceId}",
                newInvoice.Id, req.InvoiceId);

            return new Response<CreateInvoiceResponse>(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while creating InvoiceInfo for InvoiceId: {InvoiceId}",
                request.Request.InvoiceId);
            return new Response<CreateInvoiceResponse>
            {
                Code = "500",
                Message = "An error occurred while creating InvoiceInfo"
            };
        }
    }
}
