using Applications.DTOs.InvoiceInfo.GetInvoicesByMerchantBranch;
using Applications.Interfaces.Repositories;
using BuildingBlocks.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Applications.Features.InvoiceInfo.Queries;

/// <summary>
/// Query để lấy danh sách Invoice theo MerchantBranchId với phân trang và filter
/// </summary>
public record GetInvoicesByMerchantBranchQuery(GetInvoicesByMerchantBranchRequest Request) 
    : IRequest<PaginationResponse<InvoiceByMerchantBranchDto>>;

public class GetInvoicesByMerchantBranchQueryHandler(
    IInvoiceInfoRepository invoiceInfoRepository,
    IMerchantInvoiceOrderRepository merchantInvoiceOrderRepository,
    ILogger<GetInvoicesByMerchantBranchQueryHandler> logger)
    : IRequestHandler<GetInvoicesByMerchantBranchQuery, PaginationResponse<InvoiceByMerchantBranchDto>>
{
    public async Task<PaginationResponse<InvoiceByMerchantBranchDto>> Handle(
        GetInvoicesByMerchantBranchQuery request, 
        CancellationToken cancellationToken)
    {
        try
        {
            var req = request.Request;
            
            logger.LogInformation("Getting Invoices by MerchantBranchId with filters - " +
                                 "MerchantBranchId: {MerchantBranchId}, CqtStatus: {CqtStatus}, " +
                                 "InvoiceStatus: {InvoiceStatus}, PageIndex: {PageIndex}, PageSize: {PageSize}",
                req.MerchantBranchId, req.CqtInvoiceStatus, req.InvoiceStatus, req.PageIndex, req.PageSize);

            // Build query với joins
            var query = invoiceInfoRepository
                .AsQueryable()
                .Include(i => i.MerchantInvoiceOrder)
                .ThenInclude(o => o.MerchantBranchInvoiceAccount)
                .Where(i => i.MerchantInvoiceOrder.MerchantBranchId == req.MerchantBranchId)
                .AsQueryable();

            // Apply filters
            if (req.CqtInvoiceStatus.HasValue)
            {
                query = query.Where(i => i.CqtInvoiceStatus == req.CqtInvoiceStatus.Value);
            }

            if (req.InvoiceStatus.HasValue)
            {
                query = query.Where(i => i.InvoiceStatus == req.InvoiceStatus.Value);
            }

            if (req.FromDate.HasValue)
            {
                query = query.Where(i => i.CreatedAt >= req.FromDate.Value);
            }

            if (req.ToDate.HasValue)
            {
                query = query.Where(i => i.CreatedAt <= req.ToDate.Value);
            }

            if (!string.IsNullOrWhiteSpace(req.SearchTerm))
            {
                var searchTerm = req.SearchTerm.Trim().ToLower();
                query = query.Where(i => 
                    i.InvoiceId.ToLower().Contains(searchTerm) ||
                    (i.CustomerName != null && i.CustomerName.ToLower().Contains(searchTerm)));
            }

            // Order by CreatedAt descending (mới nhất đến cũ nhất)
            query = query.OrderByDescending(i => i.CreatedAt);

            // Get total count
            var totalCount = await query.CountAsync(cancellationToken);

            // Apply pagination và mapping
            var items = await query
                .Skip((req.PageIndex - 1) * req.PageSize)
                .Take(req.PageSize)
                .Select(i => new InvoiceByMerchantBranchDto
                {
                    Id = i.Id,
                    InvoiceId = i.InvoiceId,
                    CqtInvoiceStatus = i.CqtInvoiceStatus,
                    InvoiceStatus = i.InvoiceStatus,
                    InvoiceNumber = i.InvoiceNumber,
                    InvoiceSeries = i.InvoiceSeries,
                    TotalAmount = i.TotalAmount,
                    TaxAmount = i.TaxAmount,
                    CustomerName = i.CustomerName,
                    CustomerTaxCode = i.CustomerTaxCode,
                    ErrorMessage = i.ErrorMessage,
                    Notes = i.Notes,
                    CreatedAt = i.CreatedAt,
                    UpdatedAt = i.UpdatedAt ?? i.CreatedAt,
                    MerchantInvoiceOrderId = i.MerchantInvoiceOrderId,
                    MerchantInvoiceOrder = i.MerchantInvoiceOrder != null ? new MerchantInvoiceOrderSummary
                    {
                        Id = i.MerchantInvoiceOrder.Id,
                        OrderReference = i.MerchantInvoiceOrder.OrderReference,
                        Description = i.MerchantInvoiceOrder.Description
                    } : null
                })
                .ToListAsync(cancellationToken);

            var result = new PaginationResponse<InvoiceByMerchantBranchDto>(
                req.PageIndex, 
                req.PageSize, 
                totalCount, 
                items);

            logger.LogInformation("Successfully retrieved {Count} Invoices out of {Total} total for MerchantBranchId {MerchantBranchId} page {PageIndex}",
                result.Data?.Count() ?? 0, result.Total, req.MerchantBranchId, req.PageIndex);

            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while retrieving Invoices by MerchantBranchId {MerchantBranchId}",
                request.Request.MerchantBranchId);
            return new PaginationResponse<InvoiceByMerchantBranchDto>(
                request.Request.PageIndex,
                request.Request.PageSize,
                0,
                [])
            {
                Code = "500",
                Message = "An error occurred while retrieving Invoices by MerchantBranchId"
            };
        }
    }
}
