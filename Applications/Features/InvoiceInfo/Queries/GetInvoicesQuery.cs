using Applications.DTOs.InvoiceInfo.GetInvoices;
using Applications.Interfaces.Repositories;
using BuildingBlocks.Abstractions;
using Core.Enumerables;
using Mapster;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Applications.Features.InvoiceInfo.Queries;

/// <summary>
/// Query để lấy danh sách hóa đơn với khả năng lọc và phân trang
/// </summary>
public record GetInvoicesQuery(GetInvoicesRequest Request) : IRequest<PaginationResponse<InvoiceInfoDto>>;

/// <summary>
/// Handler cho GetInvoicesQuery
/// </summary>
public class GetInvoicesQueryHandler : IRequestHandler<GetInvoicesQuery, PaginationResponse<InvoiceInfoDto>>
{
    private readonly IBaseRepository<Core.Entities.InvoiceInfo> _invoiceRepository;
    private readonly ILogger<GetInvoicesQueryHandler> _logger;

    public GetInvoicesQueryHandler(
        IBaseRepository<Core.Entities.InvoiceInfo> invoiceRepository,
        ILogger<GetInvoicesQueryHandler> logger)
    {
        _invoiceRepository = invoiceRepository;
        _logger = logger;
    }

    public async Task<PaginationResponse<InvoiceInfoDto>> Handle(GetInvoicesQuery request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing GetInvoicesQuery with filters: " +
                                 "MerchantInvoiceOrderId={MerchantInvoiceOrderId}, CqtStatus={CqtStatus}, " +
                                 "InvoiceStatus={InvoiceStatus}, PageIndex={PageIndex}, PageSize={PageSize}",
                request.Request.MerchantInvoiceOrderId,
                request.Request.CqtInvoiceStatus,
                request.Request.InvoiceStatus,
                request.Request.PageIndex,
                request.Request.PageSize);

            // Build the query with filters
            var query = _invoiceRepository.AsQueryable()
                .Include(i => i.MerchantInvoiceOrder)
                .AsQueryable();

            // Apply filters
            query = ApplyFilters(query, request.Request);

            // Get total count for pagination
            var totalCount = await query.CountAsync(cancellationToken);

            // Apply sorting
            query = ApplySorting(query, request.Request.SortBy ?? "CreatedAt", request.Request.SortDescending == true ? "desc" : "asc");

            // Apply pagination
            var skip = (request.Request.PageIndex!.Value - 1) * request.Request.PageSize!.Value;
            var pagedQuery = query.Skip(skip).Take(request.Request.PageSize.Value);

            // Execute query
            var invoices = await pagedQuery.ToListAsync(cancellationToken);

            // Map to response DTOs using Mapster
            var invoiceResponses = invoices.Adapt<List<InvoiceInfoDto>>();

            _logger.LogInformation("Successfully retrieved {Count} invoices out of {Total} total",
                invoices.Count, totalCount);

            return new PaginationResponse<InvoiceInfoDto>(
                request.Request.PageIndex,
                request.Request.PageSize,
                totalCount,
                invoiceResponses);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing GetInvoicesQuery");
            throw;
        }
    }

    private static IQueryable<Core.Entities.InvoiceInfo> ApplyFilters(
        IQueryable<Core.Entities.InvoiceInfo> query,
        GetInvoicesRequest request)
    {
        if (request.MerchantBranchId.HasValue)
        {
            query = query.Where(i => i.MerchantInvoiceOrder.MerchantBranchId == request.MerchantBranchId.Value);
        }

        if (request.MerchantInvoiceOrderId.HasValue)
        {
            query = query.Where(i => i.MerchantInvoiceOrderId == request.MerchantInvoiceOrderId.Value);
        }

        if (request.CqtInvoiceStatus.HasValue)
        {
            query = query.Where(i => i.CqtInvoiceStatus == request.CqtInvoiceStatus.Value);
        }

        if (request.InvoiceStatus.HasValue)
        {
            query = query.Where(i => i.InvoiceStatus == request.InvoiceStatus.Value);
        }

        if (request.FromDate.HasValue)
        {
            query = query.Where(i => i.CreatedAt >= request.FromDate.Value);
        }

        if (request.ToDate.HasValue)
        {
            query = query.Where(i => i.CreatedAt <= request.ToDate.Value);
        }



        return query;
    }

    private static IQueryable<Core.Entities.InvoiceInfo> ApplySorting(
        IQueryable<Core.Entities.InvoiceInfo> query,
        string sortBy,
        string sortDirection)
    {
        var isDescending = sortDirection.Equals("desc", StringComparison.OrdinalIgnoreCase);

        return sortBy.ToLowerInvariant() switch
        {
            "createdat" => isDescending ? query.OrderByDescending(i => i.CreatedAt) : query.OrderBy(i => i.CreatedAt),
            "invoiceid" => isDescending ? query.OrderByDescending(i => i.InvoiceId) : query.OrderBy(i => i.InvoiceId),
            "invoicenumber" => isDescending ? query.OrderByDescending(i => i.InvoiceNumber) : query.OrderBy(i => i.InvoiceNumber),
            "invoicedate" => isDescending ? query.OrderByDescending(i => i.InvoiceDate) : query.OrderBy(i => i.InvoiceDate),
            "totalamount" => isDescending ? query.OrderByDescending(i => i.TotalAmount) : query.OrderBy(i => i.TotalAmount),
            "customername" => isDescending ? query.OrderByDescending(i => i.CustomerName) : query.OrderBy(i => i.CustomerName),
            _ => isDescending ? query.OrderByDescending(i => i.CreatedAt) : query.OrderBy(i => i.CreatedAt)
        };
    }


}
