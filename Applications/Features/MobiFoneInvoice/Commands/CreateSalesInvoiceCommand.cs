﻿using Applications.DTOs.MobiFoneInvoice.CreateSalesInvoice.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command Ä‘á»ƒ táº¡o má»›i hóa đơn BÃ¡n hÃ ng trong MobiFone Invoice API
/// </summary>
public record CreateSalesInvoiceCommand(
    SaveListHoadonBanHangRequest Request,
    string Token,
    string MaDvcs,
    string? TaxCode) : IRequest<Response<List<SaveListHoadonBanHangResponse>>>;

/// <summary>
/// Validator cho CreateSalesInvoiceCommand
/// </summary>
public class ValidateCreateSalesInvoiceCommand : AbstractValidator<CreateSalesInvoiceCommand>
{
    public ValidateCreateSalesInvoiceCommand()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request không Ä‘Æ°á»£c null");

        RuleFor(x => x.Request.editmode)
            .InclusiveBetween(1, 3)
            .WithMessage("editmode phải là  1 (Táº¡o má»›i), 2 (Sá»­a), hoáº·c 3 (XÃ³a)");

        RuleFor(x => x.Request.data)
            .NotEmpty()
            .WithMessage("data không Ä‘Æ°á»£c trá»‘ng");

        // Validation cho tá»«ng item trong data
        RuleForEach(x => x.Request.data).ChildRules(data =>
        {
            data.RuleFor(x => x.cctbao_id)
                .NotEmpty()
                .WithMessage("cctbao_id là  bắt buộc");

            data.RuleFor(x => x.nlap)
                .NotEmpty()
                .WithMessage("nlap (ngÃ y hóa đơn) là  bắt buộc");

            data.RuleFor(x => x.htttoan)
                .NotEmpty()
                .Must(x => new[] { "Tiá»n máº·t", "Chuyá»ƒn khoáº£n", "Tiá»n máº·t/Chuyá»ƒn khoáº£n" }.Contains(x))
                .WithMessage("htttoan phải là  'Tiá»n máº·t', 'Chuyá»ƒn khoáº£n', hoáº·c 'Tiá»n máº·t/Chuyá»ƒn khoáº£n'");

            data.RuleFor(x => x.mdvi)
                .NotEmpty()
                .WithMessage("mdvi (Mã đơn vá»‹) là  bắt buộc");

            data.RuleFor(x => x.is_hdcma)
                .InclusiveBetween(0, 1)
                .WithMessage("is_hdcma phải là  0 (không Mã) hoáº·c 1 (CÃ³ Mã)");

            // Validation cho tthdon - giá»›i háº¡n cho hóa đơn BÃ¡n hÃ ng
            data.RuleFor(x => x.tthdon)
                .Must(x => x == null || new decimal[] { 0, 2, 19, 21, 23 }.Contains(x.Value))
                .WithMessage("tthdon cho hóa đơn BÃ¡n hÃ ng chá»‰ Ä‘Æ°á»£c là : 0 (Gá»‘c), 2 (Thay tháº¿), 19 (Äiá»u chá»‰nh tÄƒng), 21 (Äiá»u chá»‰nh giáº£m), 23 (Äiá»u chá»‰nh thÃ´ng tin)");

            data.RuleFor(x => x.details)
                .NotEmpty()
                .WithMessage("details không Ä‘Æ°á»£c trá»‘ng");

            // Validation cho Ä‘á»‹a chá»‰ khi cÃ³ MST
            data.RuleFor(x => x.dchi)
                .NotEmpty()
                .When(x => !string.IsNullOrEmpty(x.mst))
                .WithMessage("dchi là  bắt buộc khi cÃ³ mst");

            // Validation cho giáº£m thuáº¿
            data.RuleFor(x => x.tienthuegtgtgiam)
                .NotNull()
                .When(x => x.giamthuebanhang20 == true)
                .WithMessage("tienthuegtgtgiam là  bắt buộc khi giamthuebanhang20 = true");
        });

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token là  bắt buộc");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vá»‹ là  bắt buộc");
    }
}

/// <summary>
/// Handler cho CreateSalesInvoiceCommand
/// </summary>
public class CreateSalesInvoiceCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<CreateSalesInvoiceCommand, Response<List<SaveListHoadonBanHangResponse>>>
{
    public async Task<Response<List<SaveListHoadonBanHangResponse>>> Handle(CreateSalesInvoiceCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.CreateSalesInvoiceAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            request.TaxCode,
            cancellationToken);
    }
}
