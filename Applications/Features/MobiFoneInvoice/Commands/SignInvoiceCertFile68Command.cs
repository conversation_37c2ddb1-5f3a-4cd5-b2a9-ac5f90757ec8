using Applications.DTOs.MobiFoneInvoice.SignInvoiceCertFile68.Raws;
using Applications.Interfaces.Services;
using Applications.Interfaces;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;
using Shared.Constants;
using Shared.Interfaces;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Core.Enumerables;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để ký chờ xử lý hóa đơn (bằng file mềm, SIM) trong MobiFone Invoice API
/// API 4.5: SignInvoiceCertFile68 - Chỉ sử dụng Auto Authentication
/// </summary>
public record SignInvoiceCertFile68Command(
    SignInvoiceCertFile68Request Request,
    string TaxCode) : IRequest<Response<SignInvoiceCertFile68Response>>;

/// <summary>
/// Validator cho SignInvoiceCertFile68Command
/// </summary>
public class SignInvoiceCertFile68CommandValidator : AbstractValidator<SignInvoiceCertFile68Command>
{
    public SignInvoiceCertFile68CommandValidator()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request không được null");

        RuleFor(x => x.Request.data)
            .NotNull()
            .NotEmpty()
            .WithMessage("data không được null hoặc rỗng");

        RuleFor(x => x.TaxCode)
            .NotEmpty()
            .WithMessage("TaxCode là bắt buộc cho auto authentication");

        // Validate từng item trong data array
        RuleForEach(x => x.Request.data).ChildRules(signData =>
        {
            signData.RuleFor(x => x.branch_code)
                .NotEmpty()
                .WithMessage("branch_code (Mã đơn vị cơ sở) là bắt buộc");

            signData.RuleFor(x => x.username)
                .NotEmpty()
                .WithMessage("username (Tên tài khoản) là bắt buộc");

            signData.RuleFor(x => x.lsthdon_id)
                .NotNull()
                .NotEmpty()
                .WithMessage("lsthdon_id (List id hóa đơn) không được null hoặc rỗng");

            signData.RuleFor(x => x.type_cmd)
                .NotEmpty()
                .WithMessage("type_cmd là bắt buộc")
                .Must(x => x == "200" || x == "203" || x == "206")
                .WithMessage("type_cmd phải là 200 (có Mã), 203 (không Mã), hoặc 206 (có Mã từ MTT)");
        });
    }
}

/// <summary>
/// Handler cho SignInvoiceCertFile68Command - Chỉ sử dụng Auto Authentication
/// </summary>
public class SignInvoiceCertFile68CommandHandler(
    IMobiFoneInvoiceService mobiFoneInvoiceService,
    IMobiFoneAuthenticationCacheService authCacheService,
    IApplicationDbContext dbContext,
    ILogger<SignInvoiceCertFile68CommandHandler> logger)
    : IRequestHandler<SignInvoiceCertFile68Command, Response<SignInvoiceCertFile68Response>>
{
    public async Task<Response<SignInvoiceCertFile68Response>> Handle(SignInvoiceCertFile68Command request, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Signing invoice with auto authentication for TaxCode: {TaxCode}", request.TaxCode);

            // Lấy authentication info từ cache
            var authInfo = await authCacheService.GetAuthenticationAsync(request.TaxCode, cancellationToken);

            if (authInfo == null)
            {
                logger.LogError("Failed to get authentication for TaxCode: {TaxCode}", request.TaxCode);
                return new Response<SignInvoiceCertFile68Response>
                {
                    Code = ErrorCodes.UNAUTHORIZED_ERROR,
                    Message = $"Failed to authenticate for tax code: {request.TaxCode}"
                };
            }

            logger.LogInformation("Successfully retrieved authentication for SignInvoiceCertFile68 - TaxCode: {TaxCode}, Token expires in {Minutes} minutes",
                request.TaxCode, authInfo.MinutesUntilExpiry);

            // Gọi service với token và maDvcs từ cache
            var result = await mobiFoneInvoiceService.SignInvoiceCertFile68Async(
                request.Request,
                authInfo.Token,
                authInfo.MaDvcs,
                request.TaxCode,
                cancellationToken);

            // Nếu API thành công, cập nhật trạng thái hóa đơn
            if (result.IsSuccess && result.Data != null && result.Data.ok)
            {
                await UpdateInvoiceStatusesAsync(request.Request, CqtInvoiceStatus.Signed, cancellationToken);
            }

            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while signing invoice with auto authentication for TaxCode: {TaxCode}", request.TaxCode);
            return new Response<SignInvoiceCertFile68Response>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while signing invoice with auto authentication."
            };
        }
    }

    /// <summary>
    /// Cập nhật trạng thái của các hóa đơn trong database
    /// </summary>
    private async Task UpdateInvoiceStatusesAsync(SignInvoiceCertFile68Request request, CqtInvoiceStatus newStatus, CancellationToken cancellationToken)
    {
        try
        {
            // Lấy tất cả invoice IDs từ request
            var invoiceIds = new List<string>();
            foreach (var dataItem in request.data)
            {
                invoiceIds.AddRange(dataItem.lsthdon_id);
            }

            if (!invoiceIds.Any())
            {
                logger.LogWarning("No invoice IDs found in SignInvoiceCertFile68 request");
                return;
            }

            logger.LogInformation("Updating status to {Status} for {Count} invoices", newStatus, invoiceIds.Count);

            // Tìm các InvoiceInfo dựa trên InvoiceId
            var invoicesToUpdate = await dbContext.InvoiceInfos
                .Where(i => invoiceIds.Contains(i.InvoiceId))
                .ToListAsync(cancellationToken);

            if (!invoicesToUpdate.Any())
            {
                logger.LogWarning("No matching InvoiceInfo records found for the provided invoice IDs");
                return;
            }

            // Cập nhật trạng thái
            foreach (var invoice in invoicesToUpdate)
            {
                invoice.CqtInvoiceStatus = newStatus;
                invoice.SignedDate = DateTime.UtcNow;
                invoice.UpdatedAt = DateTime.UtcNow;
                invoice.UpdatedBy = Guid.NewGuid(); // TODO: Get from current user context

                dbContext.InvoiceInfos.Update(invoice);
            }

            // Lưu changes
            await dbContext.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Successfully updated {Count} invoice statuses to {Status}", invoicesToUpdate.Count, newStatus);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while updating invoice statuses after signing");
            // Không throw exception để không ảnh hưởng đến response chính
        }
    }
}
