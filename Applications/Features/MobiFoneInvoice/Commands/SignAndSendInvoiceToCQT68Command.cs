using Applications.DTOs.MobiFoneInvoice.SignAndSendInvoiceToCQT68.Raws;
using Applications.Interfaces.Services;
using Applications.Interfaces;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;
using Shared.Constants;
using Shared.Interfaces;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Core.Enumerables;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để ký và gửi hóa đơn tới CQT (Chỉ dành cho file mềm, SIM) trong MobiFone Invoice API
/// API 4.7: SignAndSendInvoiceToCQT68 - Chỉ sử dụng Auto Authentication
/// </summary>
public record SignAndSendInvoiceToCQT68Command(
    SignAndSendInvoiceToCQT68Request Request,
    string TaxCode) : IRequest<Response<SignAndSendInvoiceToCQT68Response>>;

/// <summary>
/// Validator cho SignAndSendInvoiceToCQT68Command
/// </summary>
public class SignAndSendInvoiceToCQT68CommandValidator : AbstractValidator<SignAndSendInvoiceToCQT68Command>
{
    public SignAndSendInvoiceToCQT68CommandValidator()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request không được null");

        RuleFor(x => x.Request.data)
            .NotNull()
            .NotEmpty()
            .WithMessage("data không được null hoặc rỗng");

        RuleFor(x => x.TaxCode)
            .NotEmpty()
            .WithMessage("TaxCode là bắt buộc cho auto authentication");

        // Validate từng item trong data array
        RuleForEach(x => x.Request.data).ChildRules(signData =>
        {
            signData.RuleFor(x => x.branch_code)
                .NotEmpty()
                .WithMessage("branch_code (Mã đơn vị cơ sở) là bắt buộc");

            signData.RuleFor(x => x.username)
                .NotEmpty()
                .WithMessage("username (Tên tài khoản) là bắt buộc");

            signData.RuleFor(x => x.lsthdon_id)
                .NotNull()
                .NotEmpty()
                .WithMessage("lsthdon_id (List id hóa đơn) không được null hoặc rỗng");

            signData.RuleFor(x => x.type_cmd)
                .NotEmpty()
                .WithMessage("type_cmd là bắt buộc")
                .Must(x => x == "200" || x == "203" || x == "206")
                .WithMessage("type_cmd phải là 200 (có Mã), 203 (không Mã), hoặc 206 (có Mã từ MTT)");
        });
    }
}

/// <summary>
/// Handler cho SignAndSendInvoiceToCQT68Command - Chỉ sử dụng Auto Authentication
/// </summary>
public class SignAndSendInvoiceToCQT68CommandHandler(
    IMobiFoneInvoiceService mobiFoneInvoiceService,
    IMobiFoneAuthenticationCacheService authCacheService,
    IApplicationDbContext dbContext,
    ILogger<SignAndSendInvoiceToCQT68CommandHandler> logger)
    : IRequestHandler<SignAndSendInvoiceToCQT68Command, Response<SignAndSendInvoiceToCQT68Response>>
{
    public async Task<Response<SignAndSendInvoiceToCQT68Response>> Handle(SignAndSendInvoiceToCQT68Command request, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Sign and send invoice to CQT with auto authentication for TaxCode: {TaxCode}", request.TaxCode);

            // Lấy authentication info từ cache
            var authInfo = await authCacheService.GetAuthenticationAsync(request.TaxCode, cancellationToken);

            if (authInfo == null)
            {
                logger.LogError("Failed to get authentication for TaxCode: {TaxCode}", request.TaxCode);
                return new Response<SignAndSendInvoiceToCQT68Response>
                {
                    Code = ErrorCodes.UNAUTHORIZED_ERROR,
                    Message = $"Failed to authenticate for tax code: {request.TaxCode}"
                };
            }

            logger.LogInformation("Successfully retrieved authentication for SignAndSendInvoiceToCQT68 - TaxCode: {TaxCode}, Token expires in {Minutes} minutes",
                request.TaxCode, authInfo.MinutesUntilExpiry);

            // Gọi service với token và maDvcs từ cache
            var result = await mobiFoneInvoiceService.SignAndSendInvoiceToCQT68Async(
                request.Request,
                authInfo.Token,
                authInfo.MaDvcs,
                request.TaxCode,
                cancellationToken);

            // Nếu API thành công, cập nhật trạng thái hóa đơn
            if (result.IsSuccess && result.Data != null && result.Data.ok)
            {
                // Xác định trạng thái dựa trên response
                var newStatus = CqtInvoiceStatus.Signed; // Mặc định là đã ký

                // Nếu có cả trạng thái gửi, nghĩa là đã gửi thành công
                if (!string.IsNullOrEmpty(result.Data.trang_thai))
                {
                    newStatus = CqtInvoiceStatus.Sent;
                }

                await UpdateInvoiceStatusesAsync(request.Request, newStatus, cancellationToken);
            }

            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while sign and send invoice to CQT with auto authentication for TaxCode: {TaxCode}", request.TaxCode);
            return new Response<SignAndSendInvoiceToCQT68Response>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while sign and send invoice to CQT with auto authentication."
            };
        }
    }

    /// <summary>
    /// Cập nhật trạng thái của các hóa đơn trong database
    /// </summary>
    private async Task UpdateInvoiceStatusesAsync(SignAndSendInvoiceToCQT68Request request, CqtInvoiceStatus newStatus, CancellationToken cancellationToken)
    {
        try
        {
            // Lấy tất cả invoice IDs từ request
            var invoiceIds = new List<string>();
            foreach (var dataItem in request.data)
            {
                invoiceIds.AddRange(dataItem.lsthdon_id);
            }

            if (!invoiceIds.Any())
            {
                logger.LogWarning("No invoice IDs found in SignAndSendInvoiceToCQT68 request");
                return;
            }

            logger.LogInformation("Updating status to {Status} for {Count} invoices", newStatus, invoiceIds.Count);

            // Tìm các InvoiceInfo dựa trên InvoiceId
            var invoicesToUpdate = await dbContext.InvoiceInfos
                .Where(i => invoiceIds.Contains(i.InvoiceId))
                .ToListAsync(cancellationToken);

            if (!invoicesToUpdate.Any())
            {
                logger.LogWarning("No matching InvoiceInfo records found for the provided invoice IDs");
                return;
            }

            // Cập nhật trạng thái - cập nhật date dựa trên trạng thái
            foreach (var invoice in invoicesToUpdate)
            {
                invoice.CqtInvoiceStatus = newStatus;
                invoice.UpdatedAt = DateTime.UtcNow;
                invoice.UpdatedBy = Guid.NewGuid(); // TODO: Get from current user context

                // Cập nhật thời gian tương ứng với trạng thái
                if (newStatus == CqtInvoiceStatus.Signed)
                {
                    invoice.SignedDate = DateTime.UtcNow;
                }
                else if (newStatus == CqtInvoiceStatus.Sent)
                {
                    // Nếu chưa có SignedDate, set cả hai
                    if (!invoice.SignedDate.HasValue)
                    {
                        invoice.SignedDate = DateTime.UtcNow;
                    }
                    invoice.SentToCqtDate = DateTime.UtcNow;
                }

                dbContext.InvoiceInfos.Update(invoice);
            }

            // Lưu changes
            await dbContext.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Successfully updated {Count} invoice statuses to {Status}", invoicesToUpdate.Count, newStatus);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while updating invoice statuses after sign and send to CQT");
            // Không throw exception để không ảnh hưởng đến response chính
        }
    }
}
