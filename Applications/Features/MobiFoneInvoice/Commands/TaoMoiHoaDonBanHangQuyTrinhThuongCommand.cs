﻿using Applications.DTOs.MobiFoneInvoice.CreateSalesInvoice.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command Ä‘á»ƒ táº¡o má»›i hóa đơn BÃ¡n hÃ ng quy trÃ¬nh thÆ°á»ng trong MobiFone Invoice API
/// </summary>
public record TaoMoiHoaDonBanHangQuyTrinhThuongCommand(
    SaveListHoadonBanHangRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<List<SaveListHoadonBanHangResponse>>>;

// Validation cho Command (táº¡m thá»i comment Ä‘á»ƒ trÃ¡nh lá»—i)
// public class ValidateTaoMoiHoaDonBanHangQuyTrinhThuongCommand : AbstractValidator<TaoMoiHoaDonBanHangQuyTrinhThuongCommand>
// {
//     public ValidateTaoMoiHoaDonBanHangQuyTrinhThuongCommand()
//     {
//         RuleFor(x => x.Request)
//             .NotNull()
//             .WithMessage("Request không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng");
//
//         RuleFor(x => x.Token)
//             .NotEmpty()
//             .WithMessage("Token là  bắt buộc");
//
//         RuleFor(x => x.MaDvcs)
//             .NotEmpty()
//             .WithMessage("Mã đơn vá»‹ là  bắt buộc");
//     }
// }

/// <summary>
/// Handler cho TaoMoiHoaDonBanHangQuyTrinhThuongCommand
/// </summary>
public class TaoMoiHoaDonBanHangQuyTrinhThuongCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<TaoMoiHoaDonBanHangQuyTrinhThuongCommand, Response<List<SaveListHoadonBanHangResponse>>>
{
    public async Task<Response<List<SaveListHoadonBanHangResponse>>> Handle(TaoMoiHoaDonBanHangQuyTrinhThuongCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.TaoMoiHoaDonBanHangQuyTrinhThuongAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
