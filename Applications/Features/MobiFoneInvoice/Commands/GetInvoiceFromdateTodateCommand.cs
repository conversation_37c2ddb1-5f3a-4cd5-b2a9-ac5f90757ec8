﻿using Applications.DTOs.MobiFoneInvoice.GetInvoiceFromdateTodate.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command Ä‘á»ƒ láº¥y danh sÃ¡ch hóa đơn theo khoáº£ng thá»i gian trong MobiFone Invoice API
/// API 4.16: GetInvoiceFromdateTodate
/// </summary>
public record GetInvoiceFromdateTodateCommand(
    GetInvoiceFromdateTodateRequest Request,
    string Token,
    string MaDvcs,
    string? TaxCode) : IRequest<Response<GetInvoiceFromdateTodateResponse>>;

/// <summary>
/// Validator cho GetInvoiceFromdateTodateCommand
/// </summary>
public class GetInvoiceFromdateTodateCommandValidator : AbstractValidator<GetInvoiceFromdateTodateCommand>
{
    public GetInvoiceFromdateTodateCommandValidator()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng");

        RuleFor(x => x.Request.tu_ngay)
            .NotEmpty()
            .WithMessage("NgÃ y bắt Ä‘áº§u không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng")
            .Must(BeValidDate)
            .WithMessage("NgÃ y bắt Ä‘áº§u phải cÃ³ Ä‘á»‹nh dáº¡ng YYYY-MM-DD");

        RuleFor(x => x.Request.den_ngay)
            .NotEmpty()
            .WithMessage("NgÃ y káº¿t thÃºc không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng")
            .Must(BeValidDate)
            .WithMessage("NgÃ y káº¿t thÃºc phải cÃ³ Ä‘á»‹nh dáº¡ng YYYY-MM-DD");

        RuleFor(x => x)
            .Must(x => BeValidDateRange(x.Request.tu_ngay, x.Request.den_ngay))
            .WithMessage("NgÃ y bắt Ä‘áº§u phải nhá» hÆ¡n hoáº·c báº±ng ngÃ y káº¿t thÃºc");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vá»‹ không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng");
    }

    private static bool BeValidDate(string dateString)
    {
        return DateTime.TryParseExact(dateString, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out _);
    }

    private static bool BeValidDateRange(string fromDate, string toDate)
    {
        if (!BeValidDate(fromDate) || !BeValidDate(toDate))
            return false;

        var from = DateTime.ParseExact(fromDate, "yyyy-MM-dd", null);
        var to = DateTime.ParseExact(toDate, "yyyy-MM-dd", null);

        return from <= to;
    }
}

/// <summary>
/// Handler cho GetInvoiceFromdateTodateCommand
/// </summary>
public class GetInvoiceFromdateTodateCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<GetInvoiceFromdateTodateCommand, Response<GetInvoiceFromdateTodateResponse>>
{
    public async Task<Response<GetInvoiceFromdateTodateResponse>> Handle(GetInvoiceFromdateTodateCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.GetInvoiceFromdateTodateAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            request.TaxCode,
            cancellationToken);
    }
}
