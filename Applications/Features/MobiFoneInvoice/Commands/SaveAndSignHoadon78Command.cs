using Applications.DTOs.MobiFoneInvoice.SaveAndSignHoadon78.Raws;
using Applications.Interfaces.Services;
using Applications.Interfaces;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;
using Shared.Constants;
using Shared.Interfaces;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using Core.Enumerables;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để tạo mới và ký gửi hóa đơn bằng HSM nhà cung cấp khác, file mềm, Sim PKI trong MobiFone Invoice API
/// API 4.4: SaveAndSignHoadon78 - Chỉ sử dụng Auto Authentication
/// </summary>
public record SaveAndSignHoadon78Command(
    SaveAndSignHoadon78Request Request,
    string TaxCode) : IRequest<Response<List<SaveAndSignHoadon78Response>>>;

/// <summary>
/// Validator cho SaveAndSignHoadon78Command
/// </summary>
public class SaveAndSignHoadon78CommandValidator : AbstractValidator<SaveAndSignHoadon78Command>
{
    public SaveAndSignHoadon78CommandValidator()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request không được null");

        RuleFor(x => x.Request.is_api)
            .NotEmpty()
            .WithMessage("is_api là bắt buộc")
            .Equal("1")
            .WithMessage("is_api phải là '1'");

        RuleFor(x => x.Request.data)
            .NotNull()
            .NotEmpty()
            .WithMessage("data không được null hoặc rỗng");

        RuleFor(x => x.TaxCode)
            .NotEmpty()
            .WithMessage("TaxCode là bắt buộc cho auto authentication");

        // Basic validation for data array
        RuleForEach(x => x.Request.data)
            .NotNull()
            .WithMessage("Mỗi item trong data không được null");
    }
}

/// <summary>
/// Handler cho SaveAndSignHoadon78Command - Chỉ sử dụng Auto Authentication
/// </summary>
public class SaveAndSignHoadon78CommandHandler(
    IMobiFoneInvoiceService mobiFoneInvoiceService,
    IMobiFoneAuthenticationCacheService authCacheService,
    IApplicationDbContext dbContext,
    ILogger<SaveAndSignHoadon78CommandHandler> logger)
    : IRequestHandler<SaveAndSignHoadon78Command, Response<List<SaveAndSignHoadon78Response>>>
{
    private readonly IMobiFoneInvoiceService _mobiFoneInvoiceService = mobiFoneInvoiceService;
    private readonly IMobiFoneAuthenticationCacheService _authCacheService = authCacheService;
    private readonly IApplicationDbContext _dbContext = dbContext;
    private readonly ILogger<SaveAndSignHoadon78CommandHandler> _logger = logger;
    public async Task<Response<List<SaveAndSignHoadon78Response>>> Handle(SaveAndSignHoadon78Command request, CancellationToken cancellationToken)
    {
        return await HandleAutoAuthentication(request, cancellationToken);
    }

    private async Task<Response<List<SaveAndSignHoadon78Response>>> HandleAutoAuthentication(SaveAndSignHoadon78Command request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Save and sign invoice with auto authentication for TaxCode: {TaxCode}", request.TaxCode);

            // 1. Validate TaxCode
            if (string.IsNullOrEmpty(request.TaxCode))
            {
                _logger.LogError("TaxCode is required for auto authentication");
                return new Response<List<SaveAndSignHoadon78Response>>
                {
                    Code = ErrorCodes.BAD_REQUEST_ERROR,
                    Message = "TaxCode is required for auto authentication"
                };
            }

            // 2. Validate business logic trước khi authentication
            var validationResult = await ValidateBusinessLogic(request, cancellationToken);
            if (!validationResult.IsSuccess)
            {
                return validationResult;
            }

            // 3. Lấy authentication info từ cache
            var authInfo = await _authCacheService.GetAuthenticationAsync(request.TaxCode, cancellationToken);

            if (authInfo == null)
            {
                _logger.LogError("Failed to get authentication for TaxCode: {TaxCode}", request.TaxCode);
                return new Response<List<SaveAndSignHoadon78Response>>
                {
                    Code = ErrorCodes.UNAUTHORIZED_ERROR,
                    Message = $"Failed to authenticate for tax code: {request.TaxCode}"
                };
            }

            _logger.LogInformation("Successfully retrieved authentication for SaveAndSignHoadon78 - TaxCode: {TaxCode}, Token expires in {Minutes} minutes",
                request.TaxCode, authInfo.MinutesUntilExpiry);

            // 4. Gọi service với token và maDvcs từ cache (service chỉ gọi API thuần túy)
            var apiResult = await _mobiFoneInvoiceService.SaveAndSignHoadon78Async(
                request.Request,
                authInfo.Token,
                authInfo.MaDvcs,
                request.TaxCode,
                cancellationToken);

            // 5. Post-processing: Save to DB và update quota nếu tạo và ký hóa đơn thành công
            if (apiResult.IsSuccess && apiResult.Data != null && apiResult.Data.Count > 0)
            {
                _logger.LogInformation("API call successful, starting post-processing for TaxCode: {TaxCode}, ResponseCount: {Count}",
                    request.TaxCode, apiResult.Data.Count);
                await ProcessSuccessfulInvoiceCreation(request, apiResult.Data, cancellationToken);
            }
            else
            {
                _logger.LogWarning("Skipping post-processing - API result: IsSuccess={IsSuccess}, DataCount={DataCount}",
                    apiResult.IsSuccess, apiResult.Data?.Count ?? 0);
            }

            return apiResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while save and sign invoice with auto authentication for TaxCode: {TaxCode}", request.TaxCode);
            return new Response<List<SaveAndSignHoadon78Response>>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while save and sign invoice with auto authentication."
            };
        }
    }

    /// <summary>
    /// Validate business logic trước khi gọi API
    /// </summary>
    private async Task<Response<List<SaveAndSignHoadon78Response>>> ValidateBusinessLogic(SaveAndSignHoadon78Command request, CancellationToken cancellationToken)
    {
        // 1. Kiểm tra merchant branch account tồn tại
        var merchantBranchAccount = await _dbContext.MerchantBranchInvoiceAccounts
            .FirstOrDefaultAsync(m => m.TaxNumber == request.TaxCode, cancellationToken);

        if (merchantBranchAccount == null)
        {
            _logger.LogError("MerchantBranchInvoiceAccount not found for TaxCode: {TaxCode}", request.TaxCode);
            return new Response<List<SaveAndSignHoadon78Response>>
            {
                Code = ErrorCodes.BAD_REQUEST_ERROR,
                Message = $"No active account found for tax code: {request.TaxCode}"
            };
        }

        // 2. Kiểm tra quota hóa đơn của merchant branch
        var currentDate = DateTime.UtcNow;
        var activeOrder = await _dbContext.MerchantInvoiceOrders
            .Where(o => o.MerchantBranchId == merchantBranchAccount.MerchantBranchId &&
                       o.IsActive &&
                       o.EffectiveDateTo >= currentDate)
            .OrderByDescending(o => o.CreatedAt)
            .FirstOrDefaultAsync(cancellationToken);

        if (activeOrder == null)
        {
            _logger.LogError("No active MerchantInvoiceOrder found for TaxCode: {TaxCode}", request.TaxCode);
            return new Response<List<SaveAndSignHoadon78Response>>
            {
                Code = ErrorCodes.BAD_REQUEST_ERROR,
                Message = $"No active invoice order found for tax code: {request.TaxCode}"
            };
        }

        // 3. Kiểm tra quota còn đủ không
        var requestedInvoiceCount = request.Request.data?.Count ?? 0;
        if (activeOrder.RemainingInvoiceQuantity < requestedInvoiceCount)
        {
            _logger.LogError("Insufficient quota for TaxCode: {TaxCode}. Requested: {Requested}, Available: {Available}",
                request.TaxCode, requestedInvoiceCount, activeOrder.RemainingInvoiceQuantity);
            return new Response<List<SaveAndSignHoadon78Response>>
            {
                Code = ErrorCodes.BAD_REQUEST_ERROR,
                Message = $"Insufficient invoice quota. Requested: {requestedInvoiceCount}, Available: {activeOrder.RemainingInvoiceQuantity}"
            };
        }

        _logger.LogInformation("Business logic validation passed for TaxCode: {TaxCode}. Quota: {Available}/{Total}",
            request.TaxCode, activeOrder.RemainingInvoiceQuantity, activeOrder.TotalInvoiceQuantity);

        // Validation passed - return success response
        return new Response<List<SaveAndSignHoadon78Response>>
        {
            Code = "200",
            Message = "Validation passed"
        };
    }

    /// <summary>
    /// Xử lý post-processing sau khi tạo và ký hóa đơn thành công:
    /// 1. Tạo InvoiceInfo records cho từng hóa đơn (với status đã ký)
    /// 2. Cập nhật RemainingInvoiceQuantity của MerchantInvoiceOrder
    /// </summary>
    private async Task ProcessSuccessfulInvoiceCreation(SaveAndSignHoadon78Command request, List<SaveAndSignHoadon78Response> invoiceResponses, CancellationToken cancellationToken)
    {
        using var transaction = await ((DbContext)_dbContext).Database.BeginTransactionAsync(cancellationToken);
        try
        {
            _logger.LogInformation("Processing successful save and sign invoice creation for TaxCode: {TaxCode}, InvoiceCount: {Count}",
                request.TaxCode, invoiceResponses.Count);

            // 1. Lấy MerchantInvoiceOrder để cập nhật quota
            var merchantBranch = await _dbContext.MerchantBranchInvoiceAccounts
                .FirstOrDefaultAsync(m => m.TaxNumber == request.TaxCode, cancellationToken);

            if (merchantBranch == null)
            {
                _logger.LogError("MerchantBranchInvoiceAccount not found for TaxCode: {TaxCode}", request.TaxCode);
                return;
            }

            var activeOrder = await _dbContext.MerchantInvoiceOrders
                .FirstOrDefaultAsync(o => o.MerchantBranchId == merchantBranch.MerchantBranchId
                    && o.IsActive
                    && o.EffectiveDateFrom <= DateTime.UtcNow
                    && o.EffectiveDateTo >= DateTime.UtcNow
                    && o.RemainingInvoiceQuantity > 0, cancellationToken);

            if (activeOrder == null)
            {
                _logger.LogError("Active MerchantInvoiceOrder not found for TaxCode: {TaxCode}", request.TaxCode);
                return;
            }

            // Validate quota consistency - đảm bảo quota đủ cho số hóa đơn thực tế tạo
            var successfulInvoiceCount = invoiceResponses.Count(IsValidInvoiceResponse);
            if (activeOrder.RemainingInvoiceQuantity < successfulInvoiceCount)
            {
                _logger.LogError("Insufficient quota for TaxCode: {TaxCode}. Required: {Required}, Available: {Available}",
                    request.TaxCode, successfulInvoiceCount, activeOrder.RemainingInvoiceQuantity);
                return;
            }

            // 2. Tạo InvoiceInfo records cho từng hóa đơn thành công
            var invoiceInfos = new List<Core.Entities.InvoiceInfo>();
            var validResponseCount = 0;
            var invalidResponseCount = 0;

            foreach (var invoiceResponse in invoiceResponses)
            {
                _logger.LogDebug("Processing save and sign invoice response - isSuccess: {IsSuccess}, dataCount: {DataCount}",
                    invoiceResponse.isSuccess, invoiceResponse.data?.Count ?? 0);

                // Validate response structure đầy đủ
                if (IsValidInvoiceResponse(invoiceResponse))
                {
                    validResponseCount++;

                    // SaveAndSignHoadon78 có thể trả về multiple data items
                    foreach (var dataItem in invoiceResponse.data!)
                    {
                        var invoiceInfo = new Core.Entities.InvoiceInfo
                        {
                            Id = Guid.NewGuid(),
                            InvoiceId = dataItem.hdon_id!, // Safe vì đã validate
                            MerchantInvoiceOrderId = activeOrder.Id,
                            CqtInvoiceStatus = CqtInvoiceStatus.Signed, // Đã ký và gửi CQT
                            InvoiceStatus = InvoiceStatus.Original, // Hóa đơn gốc
                            RequestData = JsonSerializer.Serialize(request.Request),
                            ResponseData = JsonSerializer.Serialize(invoiceResponse),
                            InvoiceNumber = dataItem.shdon!, // Safe vì đã validate
                            InvoiceSeries = dataItem.khieu!, // Safe vì đã validate
                            TotalAmount = dataItem.tgtttbso,
                            TaxAmount = dataItem.tgtthue,
                            CustomerName = dataItem.ten,
                            CustomerTaxCode = dataItem.mst,
                            InvoiceDate = DateTime.TryParse(dataItem.tdlap, out var invoiceDate)
                                ? DateTime.SpecifyKind(invoiceDate, DateTimeKind.Utc)
                                : null,
                            SignedDate = DateTime.UtcNow, // Đã ký ngay
                            SentToCqtDate = DateTime.UtcNow, // Đã gửi CQT ngay
                            CurlString = $"POST /api/Invoice68/SaveAndSignHoadon78?taxCode={request.TaxCode}",
                            CreatedAt = DateTime.UtcNow,
                            CreatedBy = Guid.NewGuid() // TODO: Get from current user context
                        };

                        invoiceInfos.Add(invoiceInfo);
                    }
                }
                else
                {
                    invalidResponseCount++;
                    _logger.LogWarning("Invalid save and sign invoice response - isSuccess: {IsSuccess}, code: {Code}, message: {Message}",
                        invoiceResponse.isSuccess, invoiceResponse.code, invoiceResponse.message);
                }
            }

            _logger.LogInformation("Save and sign invoice response processing summary for TaxCode: {TaxCode} - Valid: {Valid}, Invalid: {Invalid}, Total: {Total}",
                request.TaxCode, validResponseCount, invalidResponseCount, invoiceResponses.Count);

            // 3. Save InvoiceInfo records
            if (invoiceInfos.Count > 0)
            {
                _logger.LogInformation("Adding {Count} InvoiceInfo records to database for TaxCode: {TaxCode}",
                    invoiceInfos.Count, request.TaxCode);

                await _dbContext.InvoiceInfos.AddRangeAsync(invoiceInfos, cancellationToken);
                _logger.LogInformation("Successfully added {Count} InvoiceInfo records to context for TaxCode: {TaxCode}",
                    invoiceInfos.Count, request.TaxCode);
            }
            else
            {
                _logger.LogWarning("No InvoiceInfo records to save for TaxCode: {TaxCode}", request.TaxCode);
            }

            // 4. Cập nhật RemainingInvoiceQuantity
            activeOrder.RemainingInvoiceQuantity -= invoiceInfos.Count;
            activeOrder.UpdatedAt = DateTime.UtcNow;
            activeOrder.UpdatedBy = Guid.NewGuid(); // TODO: Get from current user context

            // Ensure DateTime fields have UTC Kind để tránh PostgreSQL error
            if (activeOrder.EffectiveDateFrom.Kind != DateTimeKind.Utc)
            {
                activeOrder.EffectiveDateFrom = DateTime.SpecifyKind(activeOrder.EffectiveDateFrom, DateTimeKind.Utc);
            }
            if (activeOrder.EffectiveDateTo.Kind != DateTimeKind.Utc)
            {
                activeOrder.EffectiveDateTo = DateTime.SpecifyKind(activeOrder.EffectiveDateTo, DateTimeKind.Utc);
            }

            _dbContext.MerchantInvoiceOrders.Update(activeOrder);

            // 5. Save all changes
            await _dbContext.SaveChangesAsync(cancellationToken);

            // 6. Commit transaction
            await transaction.CommitAsync(cancellationToken);

            _logger.LogInformation("Successfully processed save and sign invoice creation post-processing for TaxCode: {TaxCode}. " +
                "Created {InvoiceCount} invoices, Updated quota from {OldQuota} to {NewQuota}",
                request.TaxCode, invoiceInfos.Count,
                activeOrder.RemainingInvoiceQuantity + invoiceInfos.Count,
                activeOrder.RemainingInvoiceQuantity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing successful save and sign invoice creation for TaxCode: {TaxCode}", request.TaxCode);

            // Rollback transaction
            try
            {
                await transaction.RollbackAsync(cancellationToken);
                _logger.LogInformation("Transaction rolled back successfully for TaxCode: {TaxCode}", request.TaxCode);
            }
            catch (Exception rollbackEx)
            {
                _logger.LogError(rollbackEx, "Failed to rollback transaction for TaxCode: {TaxCode}", request.TaxCode);
            }

            // Note: Không throw exception để không ảnh hưởng đến response chính
            // Vì hóa đơn đã được tạo và ký thành công trên MobiFone, chỉ việc sync data bị lỗi
        }
    }

    /// <summary>
    /// Validate invoice response structure để đảm bảo data integrity cho SaveAndSignHoadon78
    /// </summary>
    private static bool IsValidInvoiceResponse(SaveAndSignHoadon78Response response)
    {
        return response != null &&
               response.isSuccess &&
               !string.IsNullOrEmpty(response.code) &&
               response.data != null &&
               response.data.Count > 0 &&
               response.data.All(d => !string.IsNullOrEmpty(d.hdon_id) &&
                                     !string.IsNullOrEmpty(d.shdon) &&
                                     !string.IsNullOrEmpty(d.khieu));
    }
}
