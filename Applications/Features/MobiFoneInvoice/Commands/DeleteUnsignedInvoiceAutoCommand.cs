using Applications.DTOs.MobiFoneInvoice.DeleteUnsignedInvoice.Raws;
using Applications.Interfaces.Services;
using Applications.Interfaces;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;
using Shared.Interfaces;
using Shared.Constants;
using Microsoft.Extensions.Logging;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để xóa hóa đơn chưa ký gửi trong MobiFone Invoice API - Chỉ sử dụng Auto Authentication
/// API 4.11: hoadonXoaNhieu
/// </summary>
public record DeleteUnsignedInvoiceAutoCommand(
    DeleteUnsignedInvoiceRequest Request,
    string TaxCode) : IRequest<Response<DeleteUnsignedInvoiceResponse>>;

/// <summary>
/// Validator cho DeleteUnsignedInvoiceAutoCommand
/// </summary>
public class DeleteUnsignedInvoiceAutoCommandValidator : AbstractValidator<DeleteUnsignedInvoiceAutoCommand>
{
    public DeleteUnsignedInvoiceAutoCommandValidator()
    {
        RuleFor(x => x.Request.editmode)
            .Equal(3)
            .WithMessage("Chế độ chỉnh sửa phải là 3 (Xóa hóa đơn)");

        RuleFor(x => x.Request.data)
            .NotEmpty()
            .WithMessage("Danh sách hóa đơn cần xóa không được để trống")
            .Must(x => x.Count > 0)
            .WithMessage("Phải có ít nhất một hóa đơn cần xóa");

        RuleForEach(x => x.Request.data)
            .ChildRules(data =>
            {
                data.RuleFor(x => x.hdon_id)
                    .NotEmpty()
                    .WithMessage("ID hóa đơn không được để trống")
                    .Must(BeValidGuid)
                    .WithMessage("ID hóa đơn phải là định dạng GUID hợp lệ");
            });

        RuleFor(x => x.TaxCode)
            .NotEmpty()
            .WithMessage("TaxCode là bắt buộc cho auto authentication");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }
}

/// <summary>
/// Handler cho DeleteUnsignedInvoiceAutoCommand - Chỉ sử dụng Auto Authentication
/// </summary>
public class DeleteUnsignedInvoiceAutoCommandHandler(
    IMobiFoneInvoiceService mobiFoneInvoiceService,
    IMobiFoneAuthenticationCacheService authCacheService,
    ILogger<DeleteUnsignedInvoiceAutoCommandHandler> logger)
    : IRequestHandler<DeleteUnsignedInvoiceAutoCommand, Response<DeleteUnsignedInvoiceResponse>>
{
    private readonly IMobiFoneInvoiceService _mobiFoneInvoiceService = mobiFoneInvoiceService;
    private readonly IMobiFoneAuthenticationCacheService _authCacheService = authCacheService;
    private readonly ILogger<DeleteUnsignedInvoiceAutoCommandHandler> _logger = logger;

    public async Task<Response<DeleteUnsignedInvoiceResponse>> Handle(
        DeleteUnsignedInvoiceAutoCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Delete unsigned invoice with auto authentication for TaxCode: {TaxCode}, InvoiceCount: {Count}",
                request.TaxCode, request.Request.data?.Count ?? 0);

            // 1. Validate TaxCode
            if (string.IsNullOrEmpty(request.TaxCode))
            {
                _logger.LogError("TaxCode is required for auto authentication");
                return new Response<DeleteUnsignedInvoiceResponse>
                {
                    Code = ErrorCodes.BAD_REQUEST_ERROR,
                    Message = "TaxCode is required for auto authentication"
                };
            }

            // 2. Lấy authentication info từ cache
            var authInfo = await _authCacheService.GetAuthenticationAsync(request.TaxCode, cancellationToken);

            if (authInfo == null)
            {
                _logger.LogError("Failed to get authentication for TaxCode: {TaxCode}", request.TaxCode);
                return new Response<DeleteUnsignedInvoiceResponse>
                {
                    Code = ErrorCodes.UNAUTHORIZED_ERROR,
                    Message = $"Failed to authenticate for tax code: {request.TaxCode}"
                };
            }

            _logger.LogInformation("Successfully retrieved authentication for DeleteUnsignedInvoice - TaxCode: {TaxCode}, Token expires in {Minutes} minutes",
                request.TaxCode, authInfo.MinutesUntilExpiry);

            // 3. Gọi service với token và maDvcs từ cache
            var result = await _mobiFoneInvoiceService.DeleteUnsignedInvoiceAsync(
                request.Request,
                authInfo.Token,
                authInfo.MaDvcs,
                cancellationToken);

            _logger.LogInformation("Successfully deleted unsigned invoices for TaxCode: {TaxCode}, InvoiceCount: {Count}, Result: {Code}",
                request.TaxCode, request.Request.data?.Count ?? 0, result.Code);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while deleting unsigned invoice with auto authentication for TaxCode: {TaxCode}",
                request.TaxCode);
            return new Response<DeleteUnsignedInvoiceResponse>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while deleting unsigned invoice with auto authentication."
            };
        }
    }
}
