﻿using Applications.DTOs.MobiFoneInvoice.CreateInvoice.Raws;
using Applications.Interfaces.Services;
using Applications.Interfaces;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;
using Shared.Interfaces;
using Shared.Constants;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using Core.Enumerables;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command Ä‘á»ƒ táº¡o má»›i hóa đơn quy trÃ¬nh thÆ°á»ng trong MobiFone Invoice API
/// </summary>
public record CreateInvoiceCommand(
    SaveListHoadon78Request Request,
    string? TaxCode) : IRequest<Response<List<SaveListHoadon78Response>>>;

// public class ValidateCreateInvoiceCommand : AbstractValidator<CreateInvoiceCommand>
// {
//     public ValidateCreateInvoiceCommand()
//     {
//         RuleFor(x => x.Request)
//             .NotNull()
//             .WithMessage("Request không Ä‘Æ°á»£c null");
//
//         RuleFor(x => x.Request.editmode)
//             .InclusiveBetween(1, 3)
//             .WithMessage("editmode phải là  1 (Táº¡o má»›i), 2 (Sá»­a), hoáº·c 3 (XÃ³a)");
//
//         RuleFor(x => x.Request.data)
//             .NotEmpty()
//             .WithMessage("data không Ä‘Æ°á»£c trá»‘ng");
//
//         RuleFor(x => x.Token)
//             .NotEmpty()
//             .WithMessage("Token là  bắt buộc");
//
//         RuleFor(x => x.MaDvcs)
//             .NotEmpty()
//             .WithMessage("Mã đơn vá»‹ là  bắt buộc");
//     }
// }

/// <summary>
/// Handler cho CreateInvoiceCommand - Chỉ sử dụng Auto Authentication
/// </summary>
public class CreateInvoiceCommandHandler(
    IMobiFoneInvoiceService mobiFoneInvoiceService,
    IMobiFoneAuthenticationCacheService authCacheService,
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    ILogger<CreateInvoiceCommandHandler> logger) : IRequestHandler<CreateInvoiceCommand, Response<List<SaveListHoadon78Response>>>
{
    private readonly IMobiFoneInvoiceService _mobiFoneInvoiceService = mobiFoneInvoiceService;
    private readonly IMobiFoneAuthenticationCacheService _authCacheService = authCacheService;
    private readonly IApplicationDbContext _dbContext = dbContext;
    private readonly ILogger<CreateInvoiceCommandHandler> _logger = logger;

    public async Task<Response<List<SaveListHoadon78Response>>> Handle(CreateInvoiceCommand request, CancellationToken cancellationToken)
    {
        return await HandleAutoAuthentication(request, cancellationToken);
    }

    private async Task<Response<List<SaveListHoadon78Response>>> HandleAutoAuthentication(CreateInvoiceCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Creating invoice with auto authentication for TaxCode: {TaxCode}", request.TaxCode);

            // 1. Validate TaxCode
            if (string.IsNullOrEmpty(request.TaxCode))
            {
                _logger.LogError("TaxCode is required for auto authentication");
                return new Response<List<SaveListHoadon78Response>>
                {
                    Code = ErrorCodes.BAD_REQUEST_ERROR,
                    Message = "TaxCode is required for auto authentication"
                };
            }

            // 2. Validate business logic trước khi authentication
            var validationResult = await ValidateBusinessLogic(request, cancellationToken);
            if (!validationResult.IsSuccess)
            {
                return validationResult;
            }

            // 3. Lấy authentication info từ cache
            var authInfo = await _authCacheService.GetAuthenticationAsync(request.TaxCode, cancellationToken);

            if (authInfo == null)
            {
                _logger.LogError("Failed to get authentication for TaxCode: {TaxCode}", request.TaxCode);
                return new Response<List<SaveListHoadon78Response>>
                {
                    Code = ErrorCodes.UNAUTHORIZED_ERROR,
                    Message = $"Failed to authenticate for tax code: {request.TaxCode}"
                };
            }

            _logger.LogInformation("Successfully retrieved authentication for TaxCode: {TaxCode}, Token expires in {Minutes} minutes",
                request.TaxCode, authInfo.MinutesUntilExpiry);

            // 4. Gọi service với token và maDvcs từ cache (service chỉ gọi API thuần túy)
            var apiResult = await _mobiFoneInvoiceService.CreateInvoiceAsync(
                request.Request,
                authInfo.Token,
                authInfo.MaDvcs,
                request.TaxCode,
                cancellationToken);

            // 5. Post-processing: Save to DB và update quota nếu tạo hóa đơn thành công
            if (apiResult.IsSuccess && apiResult.Data != null && apiResult.Data.Count > 0)
            {
                _logger.LogInformation("API call successful, starting post-processing for TaxCode: {TaxCode}, ResponseCount: {Count}",
                    request.TaxCode, apiResult.Data.Count);
                await ProcessSuccessfulInvoiceCreation(request, apiResult.Data, cancellationToken);
            }
            else
            {
                _logger.LogWarning("Skipping post-processing - API result: IsSuccess={IsSuccess}, DataCount={DataCount}",
                    apiResult.IsSuccess, apiResult.Data?.Count ?? 0);
            }

            return apiResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while creating invoice with auto authentication for TaxCode: {TaxCode}", request.TaxCode);
            return new Response<List<SaveListHoadon78Response>>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while creating invoice with auto authentication."
            };
        }
    }

    /// <summary>
    /// Validate business logic: tax code, quota, etc.
    /// </summary>
    private async Task<Response<List<SaveListHoadon78Response>>> ValidateBusinessLogic(CreateInvoiceCommand request, CancellationToken cancellationToken)
    {
        // 1. Kiểm tra tax code có tồn tại trong bảng MerchantBranchInvoiceAccount
        var merchantBranchAccount = await _dbContext.MerchantBranchInvoiceAccounts
            .FirstOrDefaultAsync(x => x.TaxNumber == request.TaxCode && x.IsActive, cancellationToken);

        if (merchantBranchAccount == null)
        {
            _logger.LogWarning("No active MerchantBranchInvoiceAccount found for TaxCode: {TaxCode}", request.TaxCode);
            return new Response<List<SaveListHoadon78Response>>
            {
                Code = ErrorCodes.NOT_FOUND_DATA,
                Message = $"No active account found for tax code: {request.TaxCode}"
            };
        }

        // 2. Kiểm tra quota hóa đơn của merchant branch
        var currentDate = DateTime.UtcNow;
        var activeOrder = await _dbContext.MerchantInvoiceOrders
            .Where(o => o.MerchantBranchId == merchantBranchAccount.MerchantBranchId &&
                       o.IsActive &&
                       o.EffectiveDateTo >= currentDate)
            .OrderByDescending(o => o.CreatedAt)
            .FirstOrDefaultAsync(cancellationToken);

        if (activeOrder == null)
        {
            _logger.LogWarning("No active MerchantInvoiceOrder found for MerchantBranchId: {MerchantBranchId}",
                merchantBranchAccount.MerchantBranchId);
            return new Response<List<SaveListHoadon78Response>>
            {
                Code = ErrorCodes.BAD_REQUEST_ERROR,
                Message = "No active invoice order found for this merchant branch."
            };
        }

        // 3. Kiểm tra số lượng hóa đơn cần tạo
        var invoiceCount = request.Request.data?.Count ?? 0;
        if (activeOrder.RemainingInvoiceQuantity < invoiceCount)
        {
            _logger.LogWarning("Insufficient invoice quota. Required: {Required}, Available: {Available}",
                invoiceCount, activeOrder.RemainingInvoiceQuantity);
            return new Response<List<SaveListHoadon78Response>>
            {
                Code = ErrorCodes.BAD_REQUEST_ERROR,
                Message = $"Insufficient invoice quota. Required: {invoiceCount}, Available: {activeOrder.RemainingInvoiceQuantity}"
            };
        }

        _logger.LogInformation("Business logic validation passed for TaxCode: {TaxCode}, InvoiceCount: {InvoiceCount}, AvailableQuota: {AvailableQuota}",
            request.TaxCode, invoiceCount, activeOrder.RemainingInvoiceQuantity);

        // Validation passed - return success response
        return new Response<List<SaveListHoadon78Response>>
        {
            Code = "200",
            Message = "Validation passed"
        };
    }

    /// <summary>
    /// Xử lý post-processing sau khi tạo hóa đơn thành công:
    /// 1. Tạo InvoiceInfo records cho từng hóa đơn
    /// 2. Cập nhật RemainingInvoiceQuantity của MerchantInvoiceOrder
    /// </summary>
    private async Task ProcessSuccessfulInvoiceCreation(CreateInvoiceCommand request, List<SaveListHoadon78Response> invoiceResponses, CancellationToken cancellationToken)
    {
        using var transaction = await ((DbContext)_dbContext).Database.BeginTransactionAsync(cancellationToken);
        try
        {
            _logger.LogInformation("Processing successful invoice creation for TaxCode: {TaxCode}, InvoiceCount: {Count}",
                request.TaxCode, invoiceResponses.Count);

            // 1. Lấy MerchantInvoiceOrder để cập nhật quota
            var merchantBranch = await _dbContext.MerchantBranchInvoiceAccounts
                .FirstOrDefaultAsync(m => m.TaxNumber == request.TaxCode, cancellationToken);

            if (merchantBranch == null)
            {
                _logger.LogError("MerchantBranchInvoiceAccount not found for TaxCode: {TaxCode}", request.TaxCode);
                return;
            }

            var activeOrder = await _dbContext.MerchantInvoiceOrders
                .FirstOrDefaultAsync(o => o.MerchantBranchId == merchantBranch.MerchantBranchId
                    && o.IsActive
                    && o.EffectiveDateFrom <= DateTime.UtcNow
                    && o.EffectiveDateTo >= DateTime.UtcNow
                    && o.RemainingInvoiceQuantity > 0, cancellationToken);

            if (activeOrder == null)
            {
                _logger.LogError("Active MerchantInvoiceOrder not found for TaxCode: {TaxCode}", request.TaxCode);
                return;
            }

            // Validate quota consistency - đảm bảo quota đủ cho số hóa đơn thực tế tạo
            var successfulInvoiceCount = invoiceResponses.Count(IsValidInvoiceResponse);
            if (activeOrder.RemainingInvoiceQuantity < successfulInvoiceCount)
            {
                _logger.LogError("Insufficient quota for TaxCode: {TaxCode}. Required: {Required}, Available: {Available}",
                    request.TaxCode, successfulInvoiceCount, activeOrder.RemainingInvoiceQuantity);
                return;
            }

            // 2. Tạo InvoiceInfo records cho từng hóa đơn thành công
            var invoiceInfos = new List<Core.Entities.InvoiceInfo>();
            var validResponseCount = 0;
            var invalidResponseCount = 0;

            foreach (var invoiceResponse in invoiceResponses)
            {
                _logger.LogDebug("Processing invoice response - hdon_id: {HdonId}, ok: {Ok}",
                    invoiceResponse.data?.hdon_id, invoiceResponse.ok);

                // Validate response structure đầy đủ
                if (IsValidInvoiceResponse(invoiceResponse))
                {
                    validResponseCount++;
                    var invoiceInfo = new Core.Entities.InvoiceInfo
                    {
                        Id = Guid.NewGuid(),
                        InvoiceId = invoiceResponse?.data?.hdon_id!, // Safe vì đã validate
                        MerchantInvoiceOrderId = activeOrder.Id,
                        CqtInvoiceStatus = CqtInvoiceStatus.PendingSignature, // Mới tạo thì chờ ký
                        InvoiceStatus = InvoiceStatus.Original, // Hóa đơn gốc
                        RequestData = JsonSerializer.Serialize(request.Request),
                        ResponseData = JsonSerializer.Serialize(invoiceResponse),
                        InvoiceNumber = invoiceResponse?.data?.shdon!, // Safe vì đã validate
                        InvoiceSeries = invoiceResponse?.data?.khieu!, // Safe vì đã validate
                        TotalAmount = (decimal?)invoiceResponse?.data?.tgtttbso,
                        TaxAmount = (decimal?)invoiceResponse?.data?.tgtthue,
                        CustomerName = invoiceResponse?.data?.ten,
                        CustomerTaxCode = invoiceResponse?.data?.mst,
                        InvoiceDate = DateTime.TryParse(invoiceResponse?.data?.tdlap, out var invoiceDate)
                            ? DateTime.SpecifyKind(invoiceDate, DateTimeKind.Utc)
                            : null,
                        CurlString = $"POST /api/MobiFoneInvoice/tao-moi-hoa-don-gtgt-quy-trinh-thuong?taxCode={request.TaxCode}",
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = Guid.NewGuid() // TODO: Get from current user context
                    };

                    invoiceInfos.Add(invoiceInfo);
                }
                else
                {
                    invalidResponseCount++;
                    _logger.LogWarning("Invalid invoice response - hdon_id: {HdonId}, ok: {Ok}, shdon: {Shdon}, khieu: {Khieu}",
                        invoiceResponse.data?.hdon_id, invoiceResponse.ok,
                        invoiceResponse.data?.shdon, invoiceResponse.data?.khieu);
                }
            }

            _logger.LogInformation("Invoice response processing summary for TaxCode: {TaxCode} - Valid: {Valid}, Invalid: {Invalid}, Total: {Total}",
                request.TaxCode, validResponseCount, invalidResponseCount, invoiceResponses.Count);

            // 3. Save InvoiceInfo records
            if (invoiceInfos.Count > 0)
            {
                _logger.LogInformation("Adding {Count} InvoiceInfo records to database for TaxCode: {TaxCode}",
                    invoiceInfos.Count, request.TaxCode);

                await _dbContext.InvoiceInfos.AddRangeAsync(invoiceInfos, cancellationToken);
                _logger.LogInformation("Successfully added {Count} InvoiceInfo records to context for TaxCode: {TaxCode}",
                    invoiceInfos.Count, request.TaxCode);
            }
            else
            {
                _logger.LogWarning("No InvoiceInfo records to save for TaxCode: {TaxCode}", request.TaxCode);
            }

            // 4. Cập nhật RemainingInvoiceQuantity
            activeOrder.RemainingInvoiceQuantity -= invoiceInfos.Count;
            activeOrder.UpdatedAt = DateTime.UtcNow;
            activeOrder.UpdatedBy = Guid.NewGuid(); // TODO: Get from current user context

            // Ensure DateTime fields have UTC Kind để tránh PostgreSQL error
            if (activeOrder.EffectiveDateFrom.Kind != DateTimeKind.Utc)
            {
                activeOrder.EffectiveDateFrom = DateTime.SpecifyKind(activeOrder.EffectiveDateFrom, DateTimeKind.Utc);
            }
            if (activeOrder.EffectiveDateTo.Kind != DateTimeKind.Utc)
            {
                activeOrder.EffectiveDateTo = DateTime.SpecifyKind(activeOrder.EffectiveDateTo, DateTimeKind.Utc);
            }

            _dbContext.MerchantInvoiceOrders.Update(activeOrder);

            // 5. Save all changes
            await _dbContext.SaveChangesAsync(cancellationToken);

            // 6. Commit transaction
            await transaction.CommitAsync(cancellationToken);

            _logger.LogInformation("Successfully processed invoice creation post-processing for TaxCode: {TaxCode}. " +
                "Created {InvoiceCount} invoices, Updated quota from {OldQuota} to {NewQuota}",
                request.TaxCode, invoiceInfos.Count,
                activeOrder.RemainingInvoiceQuantity + invoiceInfos.Count,
                activeOrder.RemainingInvoiceQuantity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing successful invoice creation for TaxCode: {TaxCode}", request.TaxCode);

            // Rollback transaction
            try
            {
                await transaction.RollbackAsync(cancellationToken);
                _logger.LogInformation("Transaction rolled back successfully for TaxCode: {TaxCode}", request.TaxCode);
            }
            catch (Exception rollbackEx)
            {
                _logger.LogError(rollbackEx, "Failed to rollback transaction for TaxCode: {TaxCode}", request.TaxCode);
            }

            // Note: Không throw exception để không ảnh hưởng đến response chính
            // Vì hóa đơn đã được tạo thành công trên MobiFone, chỉ việc sync data bị lỗi
        }
    }

    /// <summary>
    /// Validate invoice response structure để đảm bảo data integrity
    /// </summary>
    private static bool IsValidInvoiceResponse(SaveListHoadon78Response response)
    {
        return response != null &&
               !string.IsNullOrEmpty(response.ok) &&
               response.ok.Equals("true", StringComparison.OrdinalIgnoreCase) &&
               response.data != null &&
               !string.IsNullOrEmpty(response.data.hdon_id) &&
               !string.IsNullOrEmpty(response.data.shdon) &&
               !string.IsNullOrEmpty(response.data.khieu);
    }
}
