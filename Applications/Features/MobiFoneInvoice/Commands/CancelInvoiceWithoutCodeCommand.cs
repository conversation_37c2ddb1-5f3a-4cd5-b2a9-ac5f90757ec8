﻿using Applications.DTOs.MobiFoneInvoice.CancelInvoiceWithoutCode.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command Ä‘á»ƒ há»§y hóa đơn không Mã trong MobiFone Invoice API
/// API 4.12: uploadCanceledInv
/// </summary>
public record CancelInvoiceWithoutCodeCommand(
    CancelInvoiceWithoutCodeRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<CancelInvoiceWithoutCodeResponse>>;

/// <summary>
/// Validator cho CancelInvoiceWithoutCodeCommand
/// </summary>
public class CancelInvoiceWithoutCodeCommandValidator : AbstractValidator<CancelInvoiceWithoutCodeCommand>
{
    public CancelInvoiceWithoutCodeCommandValidator()
    {
        RuleFor(x => x.Request.id)
            .NotEmpty()
            .WithMessage("ID hóa đơn không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng")
            .Must(BeValidGuid)
            .WithMessage("ID hóa đơn phải là  Ä‘á»‹nh dáº¡ng GUID há»£p lá»‡");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vá»‹ không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }
}

/// <summary>
/// Handler cho CancelInvoiceWithoutCodeCommand
/// </summary>
public class CancelInvoiceWithoutCodeCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<CancelInvoiceWithoutCodeCommand, Response<CancelInvoiceWithoutCodeResponse>>
{
    public async Task<Response<CancelInvoiceWithoutCodeResponse>> Handle(
        CancelInvoiceWithoutCodeCommand request,
        CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.CancelInvoiceWithoutCodeAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
