using Applications.DTOs.MobiFoneInvoice.ExportInvoiceXmlPretreatment.Raws;
using Applications.Interfaces.Services;
using Applications.Interfaces;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;
using Shared.Interfaces;
using Shared.Constants;
using Microsoft.Extensions.Logging;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để xuất XML hóa đơn trước khi ký số bằng USB token qua Plugin trong MobiFone Invoice API - Chỉ sử dụng Auto Authentication
/// API 4.22: ExportInvoiceXmlPretreatment
/// </summary>
public record ExportInvoiceXmlPretreatmentAutoCommand(
    string InvoiceId,
    string TaxCode) : IRequest<Response<ExportInvoiceXmlPretreatmentResponse>>;

/// <summary>
/// Validator cho ExportInvoiceXmlPretreatmentAutoCommand
/// </summary>
public class ExportInvoiceXmlPretreatmentAutoCommandValidator : AbstractValidator<ExportInvoiceXmlPretreatmentAutoCommand>
{
    public ExportInvoiceXmlPretreatmentAutoCommandValidator()
    {
        RuleFor(x => x.InvoiceId)
            .NotEmpty()
            .WithMessage("ID hóa đơn không được để trống")
            .Must(BeValidGuid)
            .WithMessage("ID hóa đơn phải là định dạng GUID hợp lệ");

        RuleFor(x => x.TaxCode)
            .NotEmpty()
            .WithMessage("TaxCode là bắt buộc cho auto authentication");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }
}

/// <summary>
/// Handler cho ExportInvoiceXmlPretreatmentAutoCommand - Chỉ sử dụng Auto Authentication
/// </summary>
public class ExportInvoiceXmlPretreatmentAutoCommandHandler(
    IMobiFoneInvoiceService mobiFoneInvoiceService,
    IMobiFoneAuthenticationCacheService authCacheService,
    ILogger<ExportInvoiceXmlPretreatmentAutoCommandHandler> logger)
    : IRequestHandler<ExportInvoiceXmlPretreatmentAutoCommand, Response<ExportInvoiceXmlPretreatmentResponse>>
{
    private readonly IMobiFoneInvoiceService _mobiFoneInvoiceService = mobiFoneInvoiceService;
    private readonly IMobiFoneAuthenticationCacheService _authCacheService = authCacheService;
    private readonly ILogger<ExportInvoiceXmlPretreatmentAutoCommandHandler> _logger = logger;

    public async Task<Response<ExportInvoiceXmlPretreatmentResponse>> Handle(
        ExportInvoiceXmlPretreatmentAutoCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Export invoice XML pretreatment with auto authentication for TaxCode: {TaxCode}, InvoiceId: {InvoiceId}",
                request.TaxCode, request.InvoiceId);

            // 1. Validate TaxCode
            if (string.IsNullOrEmpty(request.TaxCode))
            {
                _logger.LogError("TaxCode is required for auto authentication");
                return new Response<ExportInvoiceXmlPretreatmentResponse>
                {
                    Code = ErrorCodes.BAD_REQUEST_ERROR,
                    Message = "TaxCode is required for auto authentication"
                };
            }

            // 2. Lấy authentication info từ cache
            var authInfo = await _authCacheService.GetAuthenticationAsync(request.TaxCode, cancellationToken);

            if (authInfo == null)
            {
                _logger.LogError("Failed to get authentication for TaxCode: {TaxCode}", request.TaxCode);
                return new Response<ExportInvoiceXmlPretreatmentResponse>
                {
                    Code = ErrorCodes.UNAUTHORIZED_ERROR,
                    Message = $"Failed to authenticate for tax code: {request.TaxCode}"
                };
            }

            _logger.LogInformation("Successfully retrieved authentication for ExportInvoiceXmlPretreatment - TaxCode: {TaxCode}, Token expires in {Minutes} minutes",
                request.TaxCode, authInfo.MinutesUntilExpiry);

            // 3. Gọi service với token và maDvcs từ cache
            var result = await _mobiFoneInvoiceService.ExportInvoiceXmlPretreatmentAsync(
                request.InvoiceId,
                authInfo.Token,
                authInfo.MaDvcs,
                cancellationToken);

            _logger.LogInformation("Successfully exported invoice XML pretreatment for TaxCode: {TaxCode}, InvoiceId: {InvoiceId}, Result: {Code}",
                request.TaxCode, request.InvoiceId, result.Code);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while exporting invoice XML pretreatment with auto authentication for TaxCode: {TaxCode}, InvoiceId: {InvoiceId}",
                request.TaxCode, request.InvoiceId);
            return new Response<ExportInvoiceXmlPretreatmentResponse>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while exporting invoice XML pretreatment with auto authentication."
            };
        }
    }
}
