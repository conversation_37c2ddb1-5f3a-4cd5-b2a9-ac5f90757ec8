﻿using Applications.DTOs.MobiFoneInvoice.CreateInvoiceMTT.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command Ä‘á»ƒ táº¡o má»›i hóa đơn GiÃ¡ trá»‹ gia tÄƒng mÃ¡y tÃ­nh tiá»n sinh Mã trong MobiFone Invoice API
/// </summary>
public record TaoMoiHoaDonGTGTMayTinhTienSinhMaCommand(
    SaveListHoadon78MTTRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<List<SaveListHoadon78MTTResponse>>>;

/// <summary>
/// Validator cho TaoMoiHoaDonGTGTMayTinhTienSinhMaCommand
/// </summary>
public class TaoMoiHoaDonGTGTMayTinhTienSinhMaCommandValidator : AbstractValidator<TaoMoiHoaDonGTGTMayTinhTienSinhMaCommand>
{
    public TaoMoiHoaDonGTGTMayTinhTienSinhMaCommandValidator()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request không Ä‘Æ°á»£c null");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token là  bắt buộc");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vá»‹ là  bắt buộc");
    }
}

/// <summary>
/// Handler cho TaoMoiHoaDonGTGTMayTinhTienSinhMaCommand
/// </summary>
public class TaoMoiHoaDonGTGTMayTinhTienSinhMaCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<TaoMoiHoaDonGTGTMayTinhTienSinhMaCommand, Response<List<SaveListHoadon78MTTResponse>>>
{
    public async Task<Response<List<SaveListHoadon78MTTResponse>>> Handle(TaoMoiHoaDonGTGTMayTinhTienSinhMaCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.TaoMoiHoaDonGTGTMayTinhTienSinhMaAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
