﻿using Applications.DTOs.MobiFoneInvoice.CreateNationalReserveSalesInvoice.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command Ä‘á»ƒ táº¡o má»›i hóa đơn BÃ¡n hÃ ng dá»± trá»¯ quá»‘c gia quy trÃ¬nh thÆ°á»ng trong MobiFone Invoice API
/// </summary>
public record TaoMoiHoaDonBanHangDuTruQuocGiaQuyTrinhThuongCommand(
    SaveListHoadonBanHangDuTruQuocGiaRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<List<SaveListHoadonBanHangDuTruQuocGiaResponse>>>;

/// <summary>
/// Handler cho TaoMoiHoaDonBanHangDuTruQuocGiaQuyTrinhThuongCommand
/// </summary>
public class TaoMoiHoaDonBanHangDuTruQuocGiaQuyTrinhThuongCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<TaoMoiHoaDonBanHangDuTruQuocGiaQuyTrinhThuongCommand, Response<List<SaveListHoadonBanHangDuTruQuocGiaResponse>>>
{
    public async Task<Response<List<SaveListHoadonBanHangDuTruQuocGiaResponse>>> Handle(TaoMoiHoaDonBanHangDuTruQuocGiaQuyTrinhThuongCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.TaoMoiHoaDonBanHangDuTruQuocGiaQuyTrinhThuongAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
