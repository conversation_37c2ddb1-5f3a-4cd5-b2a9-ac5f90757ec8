using Applications.DTOs.MobiFoneInvoice.CancelInvoiceWithoutCode.Raws;
using Applications.Interfaces.Services;
using Applications.Interfaces;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;
using Shared.Interfaces;
using Shared.Constants;
using Microsoft.Extensions.Logging;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để hủy hóa đơn không mã trong MobiFone Invoice API - Chỉ sử dụng Auto Authentication
/// API 4.12: uploadCanceledInv
/// </summary>
public record CancelInvoiceWithoutCodeAutoCommand(
    CancelInvoiceWithoutCodeRequest Request,
    string TaxCode) : IRequest<Response<CancelInvoiceWithoutCodeResponse>>;

/// <summary>
/// Validator cho CancelInvoiceWithoutCodeAutoCommand
/// </summary>
public class CancelInvoiceWithoutCodeAutoCommandValidator : AbstractValidator<CancelInvoiceWithoutCodeAutoCommand>
{
    public CancelInvoiceWithoutCodeAutoCommandValidator()
    {
        RuleFor(x => x.Request.id)
            .NotEmpty()
            .WithMessage("ID hóa đơn không được để trống")
            .Must(BeValidGuid)
            .WithMessage("ID hóa đơn phải là định dạng GUID hợp lệ");

        RuleFor(x => x.TaxCode)
            .NotEmpty()
            .WithMessage("TaxCode là bắt buộc cho auto authentication");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }
}

/// <summary>
/// Handler cho CancelInvoiceWithoutCodeAutoCommand - Chỉ sử dụng Auto Authentication
/// </summary>
public class CancelInvoiceWithoutCodeAutoCommandHandler(
    IMobiFoneInvoiceService mobiFoneInvoiceService,
    IMobiFoneAuthenticationCacheService authCacheService,
    ILogger<CancelInvoiceWithoutCodeAutoCommandHandler> logger)
    : IRequestHandler<CancelInvoiceWithoutCodeAutoCommand, Response<CancelInvoiceWithoutCodeResponse>>
{
    private readonly IMobiFoneInvoiceService _mobiFoneInvoiceService = mobiFoneInvoiceService;
    private readonly IMobiFoneAuthenticationCacheService _authCacheService = authCacheService;
    private readonly ILogger<CancelInvoiceWithoutCodeAutoCommandHandler> _logger = logger;

    public async Task<Response<CancelInvoiceWithoutCodeResponse>> Handle(
        CancelInvoiceWithoutCodeAutoCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Cancel invoice without code with auto authentication for TaxCode: {TaxCode}, InvoiceId: {InvoiceId}",
                request.TaxCode, request.Request.id);

            // 1. Validate TaxCode
            if (string.IsNullOrEmpty(request.TaxCode))
            {
                _logger.LogError("TaxCode is required for auto authentication");
                return new Response<CancelInvoiceWithoutCodeResponse>
                {
                    Code = ErrorCodes.BAD_REQUEST_ERROR,
                    Message = "TaxCode is required for auto authentication"
                };
            }

            // 2. Lấy authentication info từ cache
            var authInfo = await _authCacheService.GetAuthenticationAsync(request.TaxCode, cancellationToken);

            if (authInfo == null)
            {
                _logger.LogError("Failed to get authentication for TaxCode: {TaxCode}", request.TaxCode);
                return new Response<CancelInvoiceWithoutCodeResponse>
                {
                    Code = ErrorCodes.UNAUTHORIZED_ERROR,
                    Message = $"Failed to authenticate for tax code: {request.TaxCode}"
                };
            }

            _logger.LogInformation("Successfully retrieved authentication for CancelInvoiceWithoutCode - TaxCode: {TaxCode}, Token expires in {Minutes} minutes",
                request.TaxCode, authInfo.MinutesUntilExpiry);

            // 3. Gọi service với token và maDvcs từ cache
            var result = await _mobiFoneInvoiceService.CancelInvoiceWithoutCodeAsync(
                request.Request,
                authInfo.Token,
                authInfo.MaDvcs,
                cancellationToken);

            _logger.LogInformation("Successfully cancelled invoice without code for TaxCode: {TaxCode}, InvoiceId: {InvoiceId}, Result: {Code}",
                request.TaxCode, request.Request.id, result.Code);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while cancelling invoice without code with auto authentication for TaxCode: {TaxCode}, InvoiceId: {InvoiceId}",
                request.TaxCode, request.Request.id);
            return new Response<CancelInvoiceWithoutCodeResponse>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while cancelling invoice without code with auto authentication."
            };
        }
    }
}
