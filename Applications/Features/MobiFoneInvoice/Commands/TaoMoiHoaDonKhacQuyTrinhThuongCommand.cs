﻿using Applications.DTOs.MobiFoneInvoice.CreateOtherInvoice.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command Ä‘á»ƒ táº¡o má»›i hóa đơn khÃ¡c (Tem, vÃ©, tháº», phiáº¿u...) quy trÃ¬nh thÆ°á»ng trong MobiFone Invoice API
/// </summary>
public record TaoMoiHoaDonKhacQuyTrinhThuongCommand(
    SaveListHoaDonKhacRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<List<SaveListHoaDonKhacResponse>>>;

/// <summary>
/// Handler cho TaoMoiHoaDonKhacQuyTrinhThuongCommand
/// </summary>
public class TaoMoiHoaDonKhacQuyTrinhThuongCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<TaoMoiHoaDonKhacQuyTrinhThuongCommand, Response<List<SaveListHoaDonKhacResponse>>>
{
    public async Task<Response<List<SaveListHoaDonKhacResponse>>> Handle(TaoMoiHoaDonKhacQuyTrinhThuongCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.TaoMoiHoaDonKhacQuyTrinhThuongAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
