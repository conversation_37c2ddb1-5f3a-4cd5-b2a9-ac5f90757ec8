﻿using Applications.DTOs.MobiFoneInvoice.ExportInvoiceXmlPretreatment.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command Ä‘á»ƒ xuáº¥t XML hóa đơn trÆ°á»›c khi kÃ½ sá»‘ báº±ng usb token qua Plugin trong MobiFone Invoice API
/// API 4.22: ExportInvoiceXmlPretreatment
/// </summary>
public record ExportInvoiceXmlPretreatmentCommand(
    string Id,
    string Token,
    string MaDvcs) : IRequest<Response<ExportInvoiceXmlPretreatmentResponse>>;

/// <summary>
/// Validator cho ExportInvoiceXmlPretreatmentCommand
/// </summary>
public class ExportInvoiceXmlPretreatmentCommandValidator : AbstractValidator<ExportInvoiceXmlPretreatmentCommand>
{
    public ExportInvoiceXmlPretreatmentCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("ID hóa đơn không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng")
            .Must(BeValidGuid)
            .WithMessage("ID hóa đơn phải là  GUID há»£p lá»‡");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vá»‹ không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }
}

/// <summary>
/// Handler cho ExportInvoiceXmlPretreatmentCommand
/// </summary>
public class ExportInvoiceXmlPretreatmentCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<ExportInvoiceXmlPretreatmentCommand, Response<ExportInvoiceXmlPretreatmentResponse>>
{
    public async Task<Response<ExportInvoiceXmlPretreatmentResponse>> Handle(ExportInvoiceXmlPretreatmentCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.ExportInvoiceXmlPretreatmentAsync(
            request.Id,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
