﻿using Applications.DTOs.MobiFoneInvoice.ExportXMLHoadon.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command Ä‘á»ƒ láº¥y thÃ´ng tin XML hóa đơn trong MobiFone Invoice API
/// API 4.15: ExportXMLHoadon
/// </summary>
public record ExportXMLHoadonCommand(
    string Id,
    string Token,
    string MaDvcs) : IRequest<Response<ExportXMLHoadonResponse>>;

/// <summary>
/// Validator cho ExportXMLHoadonCommand
/// </summary>
public class ExportXMLHoadonCommandValidator : AbstractValidator<ExportXMLHoadonCommand>
{
    public ExportXMLHoadonCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("ID hóa đơn không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng")
            .Must(BeValidGuid)
            .WithMessage("ID hóa đơn phải là  GUID há»£p lá»‡");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vá»‹ không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }
}

/// <summary>
/// Handler cho ExportXMLHoadonCommand
/// </summary>
public class ExportXMLHoadonCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<ExportXMLHoadonCommand, Response<ExportXMLHoadonResponse>>
{
    public async Task<Response<ExportXMLHoadonResponse>> Handle(ExportXMLHoadonCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.ExportXMLHoadonAsync(
            request.Id,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
