using Applications.DTOs.MobiFoneInvoice.SendInvoiceToCQT68.Raws;
using Applications.Interfaces.Services;
using Applications.Interfaces;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;
using Shared.Constants;
using Shared.Interfaces;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Core.Enumerables;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để gửi hóa đơn đã ký lên Cơ quan thuế trong MobiFone Invoice API
/// API 4.6: SendInvoiceToCQT68 - Chỉ sử dụng Auto Authentication
/// </summary>
public record SendInvoiceToCQT68Command(
    SendInvoiceToCQT68Request Request,
    string TaxCode) : IRequest<Response<SendInvoiceToCQT68Response>>;

/// <summary>
/// Validator cho SendInvoiceToCQT68Command
/// </summary>
public class SendInvoiceToCQT68CommandValidator : AbstractValidator<SendInvoiceToCQT68Command>
{
    public SendInvoiceToCQT68CommandValidator()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request không được null");

        RuleFor(x => x.Request.invs)
            .NotNull()
            .NotEmpty()
            .WithMessage("invs (Danh sách ID hóa đơn) không được null hoặc rỗng");

        RuleFor(x => x.Request.type_cmd)
            .NotEmpty()
            .WithMessage("type_cmd là bắt buộc")
            .Must(x => x == "200" || x == "203" || x == "206")
            .WithMessage("type_cmd phải là 200 (có Mã), 203 (không Mã), hoặc 206 (có Mã từ MTT)");

        RuleFor(x => x.TaxCode)
            .NotEmpty()
            .WithMessage("TaxCode là bắt buộc cho auto authentication");

        // Validate từng ID hóa đơn trong danh sách
        RuleForEach(x => x.Request.invs)
            .NotEmpty()
            .WithMessage("ID hóa đơn không được rỗng");
    }
}

/// <summary>
/// Handler cho SendInvoiceToCQT68Command - Chỉ sử dụng Auto Authentication
/// </summary>
public class SendInvoiceToCQT68CommandHandler(
    IMobiFoneInvoiceService mobiFoneInvoiceService,
    IMobiFoneAuthenticationCacheService authCacheService,
    IApplicationDbContext dbContext,
    ILogger<SendInvoiceToCQT68CommandHandler> logger)
    : IRequestHandler<SendInvoiceToCQT68Command, Response<SendInvoiceToCQT68Response>>
{
    public async Task<Response<SendInvoiceToCQT68Response>> Handle(SendInvoiceToCQT68Command request, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Send invoice to CQT with auto authentication for TaxCode: {TaxCode}", request.TaxCode);

            // Lấy authentication info từ cache
            var authInfo = await authCacheService.GetAuthenticationAsync(request.TaxCode, cancellationToken);

            if (authInfo == null)
            {
                logger.LogError("Failed to get authentication for TaxCode: {TaxCode}", request.TaxCode);
                return new Response<SendInvoiceToCQT68Response>
                {
                    Code = ErrorCodes.UNAUTHORIZED_ERROR,
                    Message = $"Failed to authenticate for tax code: {request.TaxCode}"
                };
            }

            logger.LogInformation("Successfully retrieved authentication for SendInvoiceToCQT68 - TaxCode: {TaxCode}, Token expires in {Minutes} minutes",
                request.TaxCode, authInfo.MinutesUntilExpiry);

            // Gọi service với token và maDvcs từ cache
            var result = await mobiFoneInvoiceService.SendInvoiceToCQT68Async(
                request.Request,
                authInfo.Token,
                authInfo.MaDvcs,
                request.TaxCode,
                cancellationToken);

            // Nếu API thành công, cập nhật trạng thái hóa đơn
            if (result.IsSuccess && result.Data != null && result.Data.ok)
            {
                await UpdateInvoiceStatusesAsync(request.Request, CqtInvoiceStatus.Sent, cancellationToken);
            }

            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while send invoice to CQT with auto authentication for TaxCode: {TaxCode}", request.TaxCode);
            return new Response<SendInvoiceToCQT68Response>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while send invoice to CQT with auto authentication."
            };
        }
    }

    /// <summary>
    /// Cập nhật trạng thái của các hóa đơn trong database
    /// </summary>
    private async Task UpdateInvoiceStatusesAsync(SendInvoiceToCQT68Request request, CqtInvoiceStatus newStatus, CancellationToken cancellationToken)
    {
        try
        {
            // Lấy invoice IDs từ request
            var invoiceIds = request.invs;

            if (!invoiceIds.Any())
            {
                logger.LogWarning("No invoice IDs found in SendInvoiceToCQT68 request");
                return;
            }

            logger.LogInformation("Updating status to {Status} for {Count} invoices", newStatus, invoiceIds.Count);

            // Tìm các InvoiceInfo dựa trên InvoiceId
            var invoicesToUpdate = await dbContext.InvoiceInfos
                .Where(i => invoiceIds.Contains(i.InvoiceId))
                .ToListAsync(cancellationToken);

            if (!invoicesToUpdate.Any())
            {
                logger.LogWarning("No matching InvoiceInfo records found for the provided invoice IDs");
                return;
            }

            // Cập nhật trạng thái
            foreach (var invoice in invoicesToUpdate)
            {
                invoice.CqtInvoiceStatus = newStatus;
                invoice.SentToCqtDate = DateTime.UtcNow;
                invoice.UpdatedAt = DateTime.UtcNow;
                invoice.UpdatedBy = Guid.NewGuid(); // TODO: Get from current user context

                dbContext.InvoiceInfos.Update(invoice);
            }

            // Lưu changes
            await dbContext.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Successfully updated {Count} invoice statuses to {Status}", invoicesToUpdate.Count, newStatus);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while updating invoice statuses after sending to CQT");
            // Không throw exception để không ảnh hưởng đến response chính
        }
    }
}
