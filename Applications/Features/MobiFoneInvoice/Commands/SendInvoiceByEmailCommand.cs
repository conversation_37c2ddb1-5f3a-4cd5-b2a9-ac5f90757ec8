﻿using Applications.DTOs.MobiFoneInvoice.SendInvoiceByEmail.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command Ä‘á»ƒ gá»­i mail phÃ¡t hÃ nh hóa đơn cho ngÆ°á»i mua trong MobiFone Invoice API
/// API 4.8: AutoSendInvoiceByEmail
/// </summary>
public record SendInvoiceByEmailCommand(
    SendInvoiceByEmailRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<SendInvoiceByEmailResponse>>;

/// <summary>
/// Validator cho SendInvoiceByEmailCommand
/// </summary>
public class SendInvoiceByEmailCommandValidator : AbstractValidator<SendInvoiceByEmailCommand>
{
    public SendInvoiceByEmailCommandValidator()
    {
        RuleFor(x => x.Request.id)
            .NotEmpty()
            .WithMessage("ID hóa đơn không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng")
            .Must(BeValidGuid)
            .WithMessage("ID hóa đơn phải là  Ä‘á»‹nh dáº¡ng GUID há»£p lá»‡");

        RuleFor(x => x.Request.nguoinhan)
            .NotEmpty()
            .WithMessage("Email ngÆ°á»i nháº­n không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng")
            .Must(BeValidEmailList)
            .WithMessage("Email ngÆ°á»i nháº­n phải cÃ³ Ä‘á»‹nh dáº¡ng há»£p lá»‡");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vá»‹ không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }

    private static bool BeValidEmailList(string emails)
    {
        if (string.IsNullOrWhiteSpace(emails))
            return false;

        var emailList = emails.Split(',', StringSplitOptions.RemoveEmptyEntries);
        return emailList.All(email => IsValidEmail(email.Trim()));
    }

    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
}

/// <summary>
/// Handler cho SendInvoiceByEmailCommand
/// </summary>
public class SendInvoiceByEmailCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<SendInvoiceByEmailCommand, Response<SendInvoiceByEmailResponse>>
{
    public async Task<Response<SendInvoiceByEmailResponse>> Handle(
        SendInvoiceByEmailCommand request,
        CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.SendInvoiceByEmailAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
