using Applications.DTOs.MobiFoneInvoice.SendInvoiceByEmail.Raws;
using Applications.Interfaces.Services;
using Applications.Interfaces;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;
using Shared.Interfaces;
using Shared.Constants;
using Microsoft.Extensions.Logging;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để gửi mail phát hành hóa đơn cho người mua trong MobiFone Invoice API - Chỉ sử dụng Auto Authentication
/// API 4.8: AutoSendInvoiceByEmail
/// </summary>
public record SendInvoiceByEmailAutoCommand(
    SendInvoiceByEmailRequest Request,
    string TaxCode) : IRequest<Response<SendInvoiceByEmailResponse>>;

/// <summary>
/// Validator cho SendInvoiceByEmailAutoCommand
/// </summary>
public class SendInvoiceByEmailAutoCommandValidator : AbstractValidator<SendInvoiceByEmailAutoCommand>
{
    public SendInvoiceByEmailAutoCommandValidator()
    {
        RuleFor(x => x.Request.id)
            .NotEmpty()
            .WithMessage("ID hóa đơn không được để trống")
            .Must(BeValidGuid)
            .WithMessage("ID hóa đơn phải là định dạng GUID hợp lệ");

        RuleFor(x => x.Request.nguoinhan)
            .NotEmpty()
            .WithMessage("Email người nhận không được để trống")
            .Must(BeValidEmailList)
            .WithMessage("Email người nhận phải có định dạng hợp lệ");

        RuleFor(x => x.TaxCode)
            .NotEmpty()
            .WithMessage("TaxCode là bắt buộc cho auto authentication");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }

    private static bool BeValidEmailList(string emails)
    {
        if (string.IsNullOrWhiteSpace(emails))
            return false;

        var emailList = emails.Split(',', StringSplitOptions.RemoveEmptyEntries);
        return emailList.All(email => IsValidEmail(email.Trim()));
    }

    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
}

/// <summary>
/// Handler cho SendInvoiceByEmailAutoCommand - Chỉ sử dụng Auto Authentication
/// </summary>
public class SendInvoiceByEmailAutoCommandHandler(
    IMobiFoneInvoiceService mobiFoneInvoiceService,
    IMobiFoneAuthenticationCacheService authCacheService,
    ILogger<SendInvoiceByEmailAutoCommandHandler> logger)
    : IRequestHandler<SendInvoiceByEmailAutoCommand, Response<SendInvoiceByEmailResponse>>
{
    private readonly IMobiFoneInvoiceService _mobiFoneInvoiceService = mobiFoneInvoiceService;
    private readonly IMobiFoneAuthenticationCacheService _authCacheService = authCacheService;
    private readonly ILogger<SendInvoiceByEmailAutoCommandHandler> _logger = logger;

    public async Task<Response<SendInvoiceByEmailResponse>> Handle(
        SendInvoiceByEmailAutoCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Send invoice by email with auto authentication for TaxCode: {TaxCode}, InvoiceId: {InvoiceId}", 
                request.TaxCode, request.Request.id);

            // 1. Validate TaxCode
            if (string.IsNullOrEmpty(request.TaxCode))
            {
                _logger.LogError("TaxCode is required for auto authentication");
                return new Response<SendInvoiceByEmailResponse>
                {
                    Code = ErrorCodes.BAD_REQUEST_ERROR,
                    Message = "TaxCode is required for auto authentication"
                };
            }

            // 2. Lấy authentication info từ cache
            var authInfo = await _authCacheService.GetAuthenticationAsync(request.TaxCode, cancellationToken);

            if (authInfo == null)
            {
                _logger.LogError("Failed to get authentication for TaxCode: {TaxCode}", request.TaxCode);
                return new Response<SendInvoiceByEmailResponse>
                {
                    Code = ErrorCodes.UNAUTHORIZED_ERROR,
                    Message = $"Failed to authenticate for tax code: {request.TaxCode}"
                };
            }

            _logger.LogInformation("Successfully retrieved authentication for SendInvoiceByEmail - TaxCode: {TaxCode}, Token expires in {Minutes} minutes",
                request.TaxCode, authInfo.MinutesUntilExpiry);

            // 3. Gọi service với token và maDvcs từ cache
            var result = await _mobiFoneInvoiceService.SendInvoiceByEmailAsync(
                request.Request,
                authInfo.Token,
                authInfo.MaDvcs,
                cancellationToken);

            _logger.LogInformation("Successfully sent invoice by email for TaxCode: {TaxCode}, InvoiceId: {InvoiceId}, Result: {Code}",
                request.TaxCode, request.Request.id, result.Code);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while sending invoice by email with auto authentication for TaxCode: {TaxCode}, InvoiceId: {InvoiceId}",
                request.TaxCode, request.Request.id);
            return new Response<SendInvoiceByEmailResponse>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while sending invoice by email with auto authentication."
            };
        }
    }
}
