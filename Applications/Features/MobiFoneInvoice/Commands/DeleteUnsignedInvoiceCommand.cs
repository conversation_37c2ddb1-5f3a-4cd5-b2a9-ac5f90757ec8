﻿using Applications.DTOs.MobiFoneInvoice.DeleteUnsignedInvoice.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command Ä‘á»ƒ xÃ³a hóa đơn chÆ°a kÃ½ gá»­i trong MobiFone Invoice API
/// API 4.11: hoadonXoaNhieu
/// </summary>
public record DeleteUnsignedInvoiceCommand(
    DeleteUnsignedInvoiceRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<DeleteUnsignedInvoiceResponse>>;

/// <summary>
/// Validator cho DeleteUnsignedInvoiceCommand
/// </summary>
public class DeleteUnsignedInvoiceCommandValidator : AbstractValidator<DeleteUnsignedInvoiceCommand>
{
    public DeleteUnsignedInvoiceCommandValidator()
    {
        RuleFor(x => x.Request.editmode)
            .Equal(3)
            .WithMessage("Cháº¿ Ä‘á»™ chá»‰nh sá»­a phải là  3 (XÃ³a hóa đơn)");

        RuleFor(x => x.Request.data)
            .NotEmpty()
            .WithMessage("Danh sÃ¡ch hóa đơn cáº§n xÃ³a không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng")
            .Must(x => x.Count > 0)
            .WithMessage("phải cÃ³ Ã­t nháº¥t má»™t hóa đơn cáº§n xÃ³a");

        RuleForEach(x => x.Request.data)
            .ChildRules(data =>
            {
                data.RuleFor(x => x.hdon_id)
                    .NotEmpty()
                    .WithMessage("ID hóa đơn không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng")
                    .Must(BeValidGuid)
                    .WithMessage("ID hóa đơn phải là  Ä‘á»‹nh dáº¡ng GUID há»£p lá»‡");
            });

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vá»‹ không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }
}

/// <summary>
/// Handler cho DeleteUnsignedInvoiceCommand
/// </summary>
public class DeleteUnsignedInvoiceCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<DeleteUnsignedInvoiceCommand, Response<DeleteUnsignedInvoiceResponse>>
{
    public async Task<Response<DeleteUnsignedInvoiceResponse>> Handle(
        DeleteUnsignedInvoiceCommand request,
        CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.DeleteUnsignedInvoiceAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
