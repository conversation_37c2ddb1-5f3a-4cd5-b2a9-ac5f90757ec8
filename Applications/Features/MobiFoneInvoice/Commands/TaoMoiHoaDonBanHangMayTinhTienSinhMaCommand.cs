﻿using Applications.DTOs.MobiFoneInvoice.CreateSalesInvoiceMTT.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command Ä‘á»ƒ táº¡o má»›i hóa đơn BÃ¡n hÃ ng mÃ¡y tÃ­nh tiá»n sinh Mã trong MobiFone Invoice API
/// </summary>
public record TaoMoiHoaDonBanHangMayTinhTienSinhMaCommand(
    SaveListHoadonBanHangMTTRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<List<SaveListHoadonBanHangMTTResponse>>>;

/// <summary>
/// Validator cho TaoMoiHoaDonBanHangMayTinhTienSinhMaCommand
/// </summary>
public class TaoMoiHoaDonBanHangMayTinhTienSinhMaCommandValidator : AbstractValidator<TaoMoiHoaDonBanHangMayTinhTienSinhMaCommand>
{
    public TaoMoiHoaDonBanHangMayTinhTienSinhMaCommandValidator()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request không Ä‘Æ°á»£c null");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token là  bắt buộc");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vá»‹ là  bắt buộc");
    }
}

/// <summary>
/// Handler cho TaoMoiHoaDonBanHangMayTinhTienSinhMaCommand
/// </summary>
public class TaoMoiHoaDonBanHangMayTinhTienSinhMaCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<TaoMoiHoaDonBanHangMayTinhTienSinhMaCommand, Response<List<SaveListHoadonBanHangMTTResponse>>>
{
    public async Task<Response<List<SaveListHoadonBanHangMTTResponse>>> Handle(TaoMoiHoaDonBanHangMayTinhTienSinhMaCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.TaoMoiHoaDonBanHangMayTinhTienSinhMaAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
