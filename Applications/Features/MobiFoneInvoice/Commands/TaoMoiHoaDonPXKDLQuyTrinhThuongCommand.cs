﻿using Applications.DTOs.MobiFoneInvoice.CreatePXKDLInvoice.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command Ä‘á»ƒ táº¡o má»›i Phiáº¿u xuáº¥t kho hÃ ng gá»­i bÃ¡n Ä‘áº¡i là½ (PXKDL) quy trÃ¬nh thÆ°á»ng trong MobiFone Invoice API
/// </summary>
public record TaoMoiHoaDonPXKDLQuyTrinhThuongCommand(
    SaveListHoadonPXKDLRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<List<SaveListHoadonPXKDLResponse>>>;

/// <summary>
/// Validator cho TaoMoiHoaDonPXKDLQuyTrinhThuongCommand
/// </summary>
public class TaoMoiHoaDonPXKDLQuyTrinhThuongCommandValidator : AbstractValidator<TaoMoiHoaDonPXKDLQuyTrinhThuongCommand>
{
    public TaoMoiHoaDonPXKDLQuyTrinhThuongCommandValidator()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request không Ä‘Æ°á»£c null");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token là  bắt buộc");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vá»‹ là  bắt buộc");
    }
}

/// <summary>
/// Handler cho TaoMoiHoaDonPXKDLQuyTrinhThuongCommand
/// </summary>
public class TaoMoiHoaDonPXKDLQuyTrinhThuongCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<TaoMoiHoaDonPXKDLQuyTrinhThuongCommand, Response<List<SaveListHoadonPXKDLResponse>>>
{
    public async Task<Response<List<SaveListHoadonPXKDLResponse>>> Handle(TaoMoiHoaDonPXKDLQuyTrinhThuongCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.TaoMoiHoaDonPXKDLQuyTrinhThuongAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
