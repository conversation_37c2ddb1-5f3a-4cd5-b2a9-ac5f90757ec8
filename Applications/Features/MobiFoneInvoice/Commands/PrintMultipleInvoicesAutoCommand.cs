using Applications.DTOs.MobiFoneInvoice.PrintMultipleInvoices.Raws;
using Applications.Interfaces.Services;
using Applications.Interfaces;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;
using Shared.Interfaces;
using Shared.Constants;
using Microsoft.Extensions.Logging;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để in nhiều hóa đơn trong MobiFone Invoice API - Chỉ sử dụng Auto Authentication
/// API 4.10: InDanhSachHoaDon
/// </summary>
public record PrintMultipleInvoicesAutoCommand(
    PrintMultipleInvoicesRequest Request,
    string TaxCode) : IRequest<Response<byte[]>>;

/// <summary>
/// Validator cho PrintMultipleInvoicesAutoCommand
/// </summary>
public class PrintMultipleInvoicesAutoCommandValidator : AbstractValidator<PrintMultipleInvoicesAutoCommand>
{
    public PrintMultipleInvoicesAutoCommandValidator()
    {
        RuleFor(x => x.Request.invs)
            .NotEmpty()
            .WithMessage("Danh sách hóa đơn cần in không được để trống")
            .Must(x => x.Count > 0)
            .WithMessage("Phải có ít nhất một hóa đơn cần in");

        RuleForEach(x => x.Request.invs)
            .NotEmpty()
            .WithMessage("ID hóa đơn không được để trống")
            .Must(BeValidGuid)
            .WithMessage("ID hóa đơn phải là định dạng GUID hợp lệ");

        RuleFor(x => x.Request.type)
            .NotEmpty()
            .WithMessage("Loại file không được để trống")
            .Must(x => x.Equals("PDF", StringComparison.OrdinalIgnoreCase))
            .WithMessage("Loại file phải là PDF");

        RuleFor(x => x.Request.inchuyendoi)
            .NotEmpty()
            .WithMessage("Tùy chọn in chuyển đổi không được để trống")
            .Must(x => x == "true" || x == "false")
            .WithMessage("Tùy chọn in chuyển đổi phải là 'true' hoặc 'false'");

        RuleFor(x => x.TaxCode)
            .NotEmpty()
            .WithMessage("TaxCode là bắt buộc cho auto authentication");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }
}

/// <summary>
/// Handler cho PrintMultipleInvoicesAutoCommand - Chỉ sử dụng Auto Authentication
/// </summary>
public class PrintMultipleInvoicesAutoCommandHandler(
    IMobiFoneInvoiceService mobiFoneInvoiceService,
    IMobiFoneAuthenticationCacheService authCacheService,
    ILogger<PrintMultipleInvoicesAutoCommandHandler> logger)
    : IRequestHandler<PrintMultipleInvoicesAutoCommand, Response<byte[]>>
{
    private readonly IMobiFoneInvoiceService _mobiFoneInvoiceService = mobiFoneInvoiceService;
    private readonly IMobiFoneAuthenticationCacheService _authCacheService = authCacheService;
    private readonly ILogger<PrintMultipleInvoicesAutoCommandHandler> _logger = logger;

    public async Task<Response<byte[]>> Handle(
        PrintMultipleInvoicesAutoCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Print multiple invoices with auto authentication for TaxCode: {TaxCode}, InvoiceCount: {Count}",
                request.TaxCode, request.Request.invs?.Count ?? 0);

            // 1. Validate TaxCode
            if (string.IsNullOrEmpty(request.TaxCode))
            {
                _logger.LogError("TaxCode is required for auto authentication");
                return new Response<byte[]>
                {
                    Code = ErrorCodes.BAD_REQUEST_ERROR,
                    Message = "TaxCode is required for auto authentication"
                };
            }

            // 2. Lấy authentication info từ cache
            var authInfo = await _authCacheService.GetAuthenticationAsync(request.TaxCode, cancellationToken);

            if (authInfo == null)
            {
                _logger.LogError("Failed to get authentication for TaxCode: {TaxCode}", request.TaxCode);
                return new Response<byte[]>
                {
                    Code = ErrorCodes.UNAUTHORIZED_ERROR,
                    Message = $"Failed to authenticate for tax code: {request.TaxCode}"
                };
            }

            _logger.LogInformation("Successfully retrieved authentication for PrintMultipleInvoices - TaxCode: {TaxCode}, Token expires in {Minutes} minutes",
                request.TaxCode, authInfo.MinutesUntilExpiry);

            // 3. Gọi service với token và maDvcs từ cache
            var result = await _mobiFoneInvoiceService.PrintMultipleInvoicesAsync(
                request.Request,
                authInfo.Token,
                authInfo.MaDvcs,
                cancellationToken);

            _logger.LogInformation("Successfully printed multiple invoices for TaxCode: {TaxCode}, InvoiceCount: {Count}, Result: {Code}",
                request.TaxCode, request.Request.invs?.Count ?? 0, result.Code);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while printing multiple invoices with auto authentication for TaxCode: {TaxCode}",
                request.TaxCode);
            return new Response<byte[]>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while printing multiple invoices with auto authentication."
            };
        }
    }
}
