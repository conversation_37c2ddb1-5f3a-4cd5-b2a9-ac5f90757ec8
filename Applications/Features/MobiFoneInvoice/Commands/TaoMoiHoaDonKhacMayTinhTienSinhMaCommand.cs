﻿using Applications.DTOs.MobiFoneInvoice.CreateOtherInvoiceMTT.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command Ä‘á»ƒ táº¡o má»›i hóa đơn khÃ¡c (Tem, vÃ©, tháº», phiáº¿u...) mÃ¡y tÃ­nh tiá»n sinh Mã trong MobiFone Invoice API
/// </summary>
public record TaoMoiHoaDonKhacMayTinhTienSinhMaCommand(
    SaveListHoaDonKhacMTTRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<List<SaveListHoaDonKhacMTTResponse>>>;

/// <summary>
/// Validator cho TaoMoiHoaDonKhacMayTinhTienSinhMaCommand
/// </summary>
public class TaoMoiHoaDonKhacMayTinhTienSinhMaCommandValidator : AbstractValidator<TaoMoiHoaDonKhacMayTinhTienSinhMaCommand>
{
    public TaoMoiHoaDonKhacMayTinhTienSinhMaCommandValidator()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request không Ä‘Æ°á»£c null");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token là  bắt buộc");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vá»‹ là  bắt buộc");
    }
}

/// <summary>
/// Handler cho TaoMoiHoaDonKhacMayTinhTienSinhMaCommand
/// </summary>
public class TaoMoiHoaDonKhacMayTinhTienSinhMaCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<TaoMoiHoaDonKhacMayTinhTienSinhMaCommand, Response<List<SaveListHoaDonKhacMTTResponse>>>
{
    public async Task<Response<List<SaveListHoaDonKhacMTTResponse>>> Handle(TaoMoiHoaDonKhacMayTinhTienSinhMaCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.TaoMoiHoaDonKhacMayTinhTienSinhMaAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
