﻿using Applications.DTOs.MobiFoneInvoice.PrintMultipleInvoices.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command Ä‘á»ƒ in nhiá»u hóa đơn trong MobiFone Invoice API
/// API 4.10: InDanhSachHoaDon
/// </summary>
public record PrintMultipleInvoicesCommand(
    PrintMultipleInvoicesRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<byte[]>>;

/// <summary>
/// Validator cho PrintMultipleInvoicesCommand
/// </summary>
public class PrintMultipleInvoicesCommandValidator : AbstractValidator<PrintMultipleInvoicesCommand>
{
    public PrintMultipleInvoicesCommandValidator()
    {
        RuleFor(x => x.Request.invs)
            .NotEmpty()
            .WithMessage("Danh sÃ¡ch ID hóa đơn không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng")
            .Must(x => x.Count > 0)
            .WithMessage("phải cÃ³ Ã­t nháº¥t má»™t ID hóa đơn");

        RuleForEach(x => x.Request.invs)
            .NotEmpty()
            .WithMessage("ID hóa đơn không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng")
            .Must(BeValidGuid)
            .WithMessage("ID hóa đơn phải là  Ä‘á»‹nh dáº¡ng GUID há»£p lá»‡");

        RuleFor(x => x.Request.type)
            .NotEmpty()
            .WithMessage("Loáº¡i file không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng")
            .Must(x => x.Equals("PDF", StringComparison.OrdinalIgnoreCase))
            .WithMessage("Loáº¡i file phải là  PDF");

        RuleFor(x => x.Request.inchuyendoi)
            .NotEmpty()
            .WithMessage("TÃ¹y chá»n in chuyá»ƒn Ä‘á»•i không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng")
            .Must(x => x == "true" || x == "false")
            .WithMessage("TÃ¹y chá»n in chuyá»ƒn Ä‘á»•i phải là  'true' hoáº·c 'false'");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vá»‹ không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }
}

/// <summary>
/// Handler cho PrintMultipleInvoicesCommand
/// </summary>
public class PrintMultipleInvoicesCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<PrintMultipleInvoicesCommand, Response<byte[]>>
{
    public async Task<Response<byte[]>> Handle(
        PrintMultipleInvoicesCommand request,
        CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.PrintMultipleInvoicesAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
