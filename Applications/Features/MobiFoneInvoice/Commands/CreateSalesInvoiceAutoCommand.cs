using Applications.DTOs.MobiFoneInvoice.CreateSalesInvoice.Raws;
using Applications.Interfaces.Services;
using Applications.Interfaces;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;
using Shared.Interfaces;
using Shared.Constants;
using Microsoft.Extensions.Logging;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để tạo mới hóa đơn Bán hàng trong MobiFone Invoice API - Chỉ sử dụng Auto Authentication
/// API: SaveListHoadon78 (<PERSON><PERSON> hàng)
/// </summary>
public record CreateSalesInvoiceAutoCommand(
    SaveListHoadonBanHangRequest Request,
    string TaxCode) : IRequest<Response<List<SaveListHoadonBanHangResponse>>>;

/// <summary>
/// Validator cho CreateSalesInvoiceAutoCommand
/// </summary>
public class CreateSalesInvoiceAutoCommandValidator : AbstractValidator<CreateSalesInvoiceAutoCommand>
{
    public CreateSalesInvoiceAutoCommandValidator()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request không được null");

        RuleFor(x => x.Request.editmode)
            .InclusiveBetween(1, 3)
            .WithMessage("editmode phải là 1 (Tạo mới), 2 (Sửa), hoặc 3 (Xóa)");

        RuleFor(x => x.Request.data)
            .NotEmpty()
            .WithMessage("data không được trống");

        // Validation cho từng item trong data
        RuleForEach(x => x.Request.data).ChildRules(data =>
        {
            data.RuleFor(x => x.cctbao_id)
                .NotEmpty()
                .WithMessage("cctbao_id là bắt buộc");

            data.RuleFor(x => x.nlap)
                .NotEmpty()
                .WithMessage("nlap (ngày hóa đơn) là bắt buộc");

            data.RuleFor(x => x.htttoan)
                .NotEmpty()
                .Must(x => new[] { "Tiền mặt", "Chuyển khoản", "Tiền mặt/Chuyển khoản" }.Contains(x))
                .WithMessage("htttoan phải là 'Tiền mặt', 'Chuyển khoản', hoặc 'Tiền mặt/Chuyển khoản'");

            data.RuleFor(x => x.mdvi)
                .NotEmpty()
                .WithMessage("mdvi (Mã đơn vị) là bắt buộc");

            data.RuleFor(x => x.is_hdcma)
                .InclusiveBetween(0, 1)
                .WithMessage("is_hdcma phải là 0 (không Mã) hoặc 1 (Có Mã)");

            // Validation cho tthdon - giới hạn cho hóa đơn Bán hàng
            data.RuleFor(x => x.tthdon)
                .Must(x => x == null || new decimal[] { 0, 2, 19, 21, 23 }.Contains(x.Value))
                .WithMessage("tthdon cho hóa đơn Bán hàng chỉ được là: 0 (Gốc), 2 (Thay thế), 19 (Điều chỉnh tăng), 21 (Điều chỉnh giảm), 23 (Điều chỉnh thông tin)");

            data.RuleFor(x => x.details)
                .NotEmpty()
                .WithMessage("details không được trống");

            // Validation cho địa chỉ khi có MST
            data.RuleFor(x => x.dchi)
                .NotEmpty()
                .When(x => !string.IsNullOrEmpty(x.mst))
                .WithMessage("dchi là bắt buộc khi có mst");

            // Validation cho giảm thuế
            data.RuleFor(x => x.tienthuegtgtgiam)
                .NotNull()
                .When(x => x.giamthuebanhang20 == true)
                .WithMessage("tienthuegtgtgiam là bắt buộc khi giamthuebanhang20 = true");
        });

        RuleFor(x => x.TaxCode)
            .NotEmpty()
            .WithMessage("TaxCode là bắt buộc cho auto authentication");
    }
}

/// <summary>
/// Handler cho CreateSalesInvoiceAutoCommand - Chỉ sử dụng Auto Authentication
/// </summary>
public class CreateSalesInvoiceAutoCommandHandler(
    IMobiFoneInvoiceService mobiFoneInvoiceService,
    IMobiFoneAuthenticationCacheService authCacheService,
    ILogger<CreateSalesInvoiceAutoCommandHandler> logger)
    : IRequestHandler<CreateSalesInvoiceAutoCommand, Response<List<SaveListHoadonBanHangResponse>>>
{
    private readonly IMobiFoneInvoiceService _mobiFoneInvoiceService = mobiFoneInvoiceService;
    private readonly IMobiFoneAuthenticationCacheService _authCacheService = authCacheService;
    private readonly ILogger<CreateSalesInvoiceAutoCommandHandler> _logger = logger;

    public async Task<Response<List<SaveListHoadonBanHangResponse>>> Handle(
        CreateSalesInvoiceAutoCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Create sales invoice with auto authentication for TaxCode: {TaxCode}, InvoiceCount: {Count}",
                request.TaxCode, request.Request.data?.Count ?? 0);

            // 1. Validate TaxCode
            if (string.IsNullOrEmpty(request.TaxCode))
            {
                _logger.LogError("TaxCode is required for auto authentication");
                return new Response<List<SaveListHoadonBanHangResponse>>
                {
                    Code = ErrorCodes.BAD_REQUEST_ERROR,
                    Message = "TaxCode is required for auto authentication"
                };
            }

            // 2. Lấy authentication info từ cache
            var authInfo = await _authCacheService.GetAuthenticationAsync(request.TaxCode, cancellationToken);

            if (authInfo == null)
            {
                _logger.LogError("Failed to get authentication for TaxCode: {TaxCode}", request.TaxCode);
                return new Response<List<SaveListHoadonBanHangResponse>>
                {
                    Code = ErrorCodes.UNAUTHORIZED_ERROR,
                    Message = $"Failed to authenticate for tax code: {request.TaxCode}"
                };
            }

            _logger.LogInformation("Successfully retrieved authentication for CreateSalesInvoice - TaxCode: {TaxCode}, Token expires in {Minutes} minutes",
                request.TaxCode, authInfo.MinutesUntilExpiry);

            // 3. Gọi service với token và maDvcs từ cache
            var result = await _mobiFoneInvoiceService.CreateSalesInvoiceAsync(
                request.Request,
                authInfo.Token,
                authInfo.MaDvcs,
                request.TaxCode,
                cancellationToken);

            _logger.LogInformation("Successfully created sales invoice for TaxCode: {TaxCode}, Result: {Code}, InvoiceCount: {Count}",
                request.TaxCode, result.Code, result.Data?.Count ?? 0);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while creating sales invoice with auto authentication for TaxCode: {TaxCode}",
                request.TaxCode);
            return new Response<List<SaveListHoadonBanHangResponse>>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while creating sales invoice with auto authentication."
            };
        }
    }
}
