﻿using Applications.DTOs.MobiFoneInvoice.Login;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command Ä‘á»ƒ Ä‘Äƒng nháº­p vÃ o MobiFone Invoice API
/// </summary>
public record LoginCommand(LoginRequest Request) : IRequest<Response<LoginResponse>>;

// validation
// public class ValidateLoginCommand : AbstractValidator<LoginCommand>
// {
//     public ValidateLoginCommand()
//     {
//         RuleFor(x => x.Request.Username).NotEmpty().WithMessage("Username is required.");
//         RuleFor(x => x.Request.Password).NotEmpty().WithMessage("Password is required.");
//         RuleFor(x => x.Request.TaxCode).NotEmpty().WithMessage("TaxCode is required.");
//     }
// }

/// <summary>
/// Handler cho LoginCommand
/// </summary>
public class LoginCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService) : IRequestHandler<LoginCommand, Response<LoginResponse>>
{

    public async Task<Response<LoginResponse>> Handle(LoginCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.LoginAsync(request.Request, cancellationToken);
    }
}
