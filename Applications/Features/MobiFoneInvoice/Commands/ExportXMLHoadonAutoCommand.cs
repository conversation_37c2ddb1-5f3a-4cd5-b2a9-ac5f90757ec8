using Applications.DTOs.MobiFoneInvoice.ExportXMLHoadon.Raws;
using Applications.Interfaces.Services;
using Applications.Interfaces;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;
using Shared.Interfaces;
using Shared.Constants;
using Microsoft.Extensions.Logging;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để xuất XML hóa đơn trong MobiFone Invoice API - Chỉ sử dụng Auto Authentication
/// API 4.15: ExportXMLHoadon
/// </summary>
public record ExportXMLHoadonAutoCommand(
    string InvoiceId,
    string TaxCode) : IRequest<Response<ExportXMLHoadonResponse>>;

/// <summary>
/// Validator cho ExportXMLHoadonAutoCommand
/// </summary>
public class ExportXMLHoadonAutoCommandValidator : AbstractValidator<ExportXMLHoadonAutoCommand>
{
    public ExportXMLHoadonAutoCommandValidator()
    {
        RuleFor(x => x.InvoiceId)
            .NotEmpty()
            .WithMessage("ID hóa đơn không được để trống")
            .Must(BeValidGuid)
            .WithMessage("ID hóa đơn phải là định dạng GUID hợp lệ");

        RuleFor(x => x.TaxCode)
            .NotEmpty()
            .WithMessage("TaxCode là bắt buộc cho auto authentication");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }
}

/// <summary>
/// Handler cho ExportXMLHoadonAutoCommand - Chỉ sử dụng Auto Authentication
/// </summary>
public class ExportXMLHoadonAutoCommandHandler(
    IMobiFoneInvoiceService mobiFoneInvoiceService,
    IMobiFoneAuthenticationCacheService authCacheService,
    ILogger<ExportXMLHoadonAutoCommandHandler> logger)
    : IRequestHandler<ExportXMLHoadonAutoCommand, Response<ExportXMLHoadonResponse>>
{
    private readonly IMobiFoneInvoiceService _mobiFoneInvoiceService = mobiFoneInvoiceService;
    private readonly IMobiFoneAuthenticationCacheService _authCacheService = authCacheService;
    private readonly ILogger<ExportXMLHoadonAutoCommandHandler> _logger = logger;

    public async Task<Response<ExportXMLHoadonResponse>> Handle(
        ExportXMLHoadonAutoCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Export XML hoadon with auto authentication for TaxCode: {TaxCode}, InvoiceId: {InvoiceId}",
                request.TaxCode, request.InvoiceId);

            // 1. Validate TaxCode
            if (string.IsNullOrEmpty(request.TaxCode))
            {
                _logger.LogError("TaxCode is required for auto authentication");
                return new Response<ExportXMLHoadonResponse>
                {
                    Code = ErrorCodes.BAD_REQUEST_ERROR,
                    Message = "TaxCode is required for auto authentication"
                };
            }

            // 2. Lấy authentication info từ cache
            var authInfo = await _authCacheService.GetAuthenticationAsync(request.TaxCode, cancellationToken);

            if (authInfo == null)
            {
                _logger.LogError("Failed to get authentication for TaxCode: {TaxCode}", request.TaxCode);
                return new Response<ExportXMLHoadonResponse>
                {
                    Code = ErrorCodes.UNAUTHORIZED_ERROR,
                    Message = $"Failed to authenticate for tax code: {request.TaxCode}"
                };
            }

            _logger.LogInformation("Successfully retrieved authentication for ExportXMLHoadon - TaxCode: {TaxCode}, Token expires in {Minutes} minutes",
                request.TaxCode, authInfo.MinutesUntilExpiry);

            // 3. Gọi service với token và maDvcs từ cache
            var result = await _mobiFoneInvoiceService.ExportXMLHoadonAsync(
                request.InvoiceId,
                authInfo.Token,
                authInfo.MaDvcs,
                cancellationToken);

            _logger.LogInformation("Successfully exported XML hoadon for TaxCode: {TaxCode}, InvoiceId: {InvoiceId}, Result: {Code}",
                request.TaxCode, request.InvoiceId, result.Code);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while exporting XML hoadon with auto authentication for TaxCode: {TaxCode}, InvoiceId: {InvoiceId}",
                request.TaxCode, request.InvoiceId);
            return new Response<ExportXMLHoadonResponse>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while exporting XML hoadon with auto authentication."
            };
        }
    }
}
