﻿using Applications.DTOs.MobiFoneInvoice.CreatePublicAssetSalesInvoice.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command Ä‘á»ƒ táº¡o má»›i hóa đơn BÃ¡n tÃ i sáº£n cÃ´ng quy trÃ¬nh thÆ°á»ng trong MobiFone Invoice API
/// </summary>
public record TaoMoiHoaDonBanTaiSanCongQuyTrinhThuongCommand(
    SaveListHoadonBanTaiSanCongRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<List<SaveListHoadonBanTaiSanCongResponse>>>;

/// <summary>
/// Handler cho TaoMoiHoaDonBanTaiSanCongQuyTrinhThuongCommand
/// </summary>
public class TaoMoiHoaDonBanTaiSanCongQuyTrinhThuongCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<TaoMoiHoaDonBanTaiSanCongQuyTrinhThuongCommand, Response<List<SaveListHoadonBanTaiSanCongResponse>>>
{
    public async Task<Response<List<SaveListHoadonBanTaiSanCongResponse>>> Handle(TaoMoiHoaDonBanTaiSanCongQuyTrinhThuongCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.TaoMoiHoaDonBanTaiSanCongQuyTrinhThuongAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
