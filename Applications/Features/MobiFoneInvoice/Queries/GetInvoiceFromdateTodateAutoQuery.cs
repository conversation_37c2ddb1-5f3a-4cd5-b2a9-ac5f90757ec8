using Applications.DTOs.MobiFoneInvoice.GetInvoiceFromdateTodate.Raws;
using Applications.Interfaces.Services;
using Applications.Interfaces;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;
using Shared.Interfaces;
using Shared.Constants;
using Microsoft.Extensions.Logging;

namespace Applications.Features.MobiFoneInvoice.Queries;

/// <summary>
/// Query để lấy danh sách hóa đơn theo khoảng thời gian trong MobiFone Invoice API - Chỉ sử dụng Auto Authentication
/// API 4.16: GetInvoiceFromdateTodate
/// </summary>
public record GetInvoiceFromdateTodateAutoQuery(
    GetInvoiceFromdateTodateRequest Request,
    string TaxCode) : IRequest<Response<GetInvoiceFromdateTodateResponse>>;

/// <summary>
/// Validator cho GetInvoiceFromdateTodateAutoQuery
/// </summary>
public class GetInvoiceFromdateTodateAutoQueryValidator : AbstractValidator<GetInvoiceFromdateTodateAutoQuery>
{
    public GetInvoiceFromdateTodateAutoQueryValidator()
    {
        RuleFor(x => x.Request.tu_ngay)
            .NotEmpty()
            .WithMessage("tu_ngay là bắt buộc")
            .Must(BeValidDateFormat)
            .WithMessage("tu_ngay phải có định dạng YYYY-MM-DD");

        RuleFor(x => x.Request.den_ngay)
            .NotEmpty()
            .WithMessage("den_ngay là bắt buộc")
            .Must(BeValidDateFormat)
            .WithMessage("den_ngay phải có định dạng YYYY-MM-DD");

        RuleFor(x => x.Request)
            .Must(HaveValidDateRange)
            .WithMessage("den_ngay phải lớn hơn hoặc bằng tu_ngay");

        // Validate date range is not too large (optional business rule)
        RuleFor(x => x.Request)
            .Must(HaveReasonableDateRange)
            .WithMessage("Khoảng thời gian không được vượt quá 365 ngày");

        RuleFor(x => x.TaxCode)
            .NotEmpty()
            .WithMessage("TaxCode là bắt buộc cho auto authentication");
    }

    private static bool BeValidDateFormat(string date)
    {
        return DateTime.TryParseExact(date, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out _);
    }

    private static bool HaveValidDateRange(GetInvoiceFromdateTodateRequest request)
    {
        if (DateTime.TryParseExact(request.tu_ngay, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out var fromDate) &&
            DateTime.TryParseExact(request.den_ngay, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out var toDate))
        {
            return toDate >= fromDate;
        }
        return true; // If parsing fails, let other validators handle it
    }

    private static bool HaveReasonableDateRange(GetInvoiceFromdateTodateRequest request)
    {
        if (DateTime.TryParseExact(request.tu_ngay, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out var fromDate) &&
            DateTime.TryParseExact(request.den_ngay, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out var toDate))
        {
            var daysDifference = (toDate - fromDate).TotalDays;
            return daysDifference <= 365; // Max 1 year range
        }
        return true; // If parsing fails, let other validators handle it
    }


}

/// <summary>
/// Handler cho GetInvoiceFromdateTodateAutoQuery - Chỉ sử dụng Auto Authentication
/// </summary>
public class GetInvoiceFromdateTodateAutoQueryHandler(
    IMobiFoneInvoiceService mobiFoneInvoiceService,
    IMobiFoneAuthenticationCacheService authCacheService,
    ILogger<GetInvoiceFromdateTodateAutoQueryHandler> logger)
    : IRequestHandler<GetInvoiceFromdateTodateAutoQuery, Response<GetInvoiceFromdateTodateResponse>>
{
    private readonly IMobiFoneInvoiceService _mobiFoneInvoiceService = mobiFoneInvoiceService;
    private readonly IMobiFoneAuthenticationCacheService _authCacheService = authCacheService;
    private readonly ILogger<GetInvoiceFromdateTodateAutoQueryHandler> _logger = logger;

    public async Task<Response<GetInvoiceFromdateTodateResponse>> Handle(
        GetInvoiceFromdateTodateAutoQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Get invoice from date to date with auto authentication for TaxCode: {TaxCode}, DateRange: {FromDate} to {ToDate}",
                request.TaxCode, request.Request.tu_ngay, request.Request.den_ngay);

            // 1. Validate TaxCode
            if (string.IsNullOrEmpty(request.TaxCode))
            {
                _logger.LogError("TaxCode is required for auto authentication");
                return new Response<GetInvoiceFromdateTodateResponse>
                {
                    Code = ErrorCodes.BAD_REQUEST_ERROR,
                    Message = "TaxCode is required for auto authentication"
                };
            }

            // 2. Lấy authentication info từ cache
            var authInfo = await _authCacheService.GetAuthenticationAsync(request.TaxCode, cancellationToken);

            if (authInfo == null)
            {
                _logger.LogError("Failed to get authentication for TaxCode: {TaxCode}", request.TaxCode);
                return new Response<GetInvoiceFromdateTodateResponse>
                {
                    Code = ErrorCodes.UNAUTHORIZED_ERROR,
                    Message = $"Failed to authenticate for tax code: {request.TaxCode}"
                };
            }

            _logger.LogInformation("Successfully retrieved authentication for GetInvoiceFromdateTodate - TaxCode: {TaxCode}, Token expires in {Minutes} minutes",
                request.TaxCode, authInfo.MinutesUntilExpiry);

            // 3. Gọi service với token và maDvcs từ cache
            var result = await _mobiFoneInvoiceService.GetInvoiceFromdateTodateAsync(
                request.Request,
                authInfo.Token,
                authInfo.MaDvcs,
                request.TaxCode,
                cancellationToken);

            _logger.LogInformation("Successfully retrieved invoices from date to date for TaxCode: {TaxCode}, Result: {Code}, RecordCount: {Count}",
                request.TaxCode, result.Code, result.Data?.Count ?? 0);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting invoices from date to date with auto authentication for TaxCode: {TaxCode}",
                request.TaxCode);
            return new Response<GetInvoiceFromdateTodateResponse>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while getting invoices from date to date with auto authentication."
            };
        }
    }
}
