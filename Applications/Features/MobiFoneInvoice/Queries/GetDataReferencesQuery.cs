﻿using Applications.DTOs.MobiFoneInvoice.GetDataReferences;
using Applications.Interfaces.Services;
using Applications.Interfaces;
using MediatR;
using BuildingBlocks.Abstractions;
using Shared.Constants;
using Shared.Interfaces;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;

namespace Applications.Features.MobiFoneInvoice.Queries;

/// <summary>
/// Query Ä‘á»ƒ láº¥y thÃ´ng tin dáº£i kÃ½ hiá»‡u máº«u sá»‘ hóa đơn tá»« MobiFone Invoice API
/// </summary>
public record GetDataReferencesQuery(
    GetDataReferencesRequest Request,
    string TaxCode) : IRequest<Response<GetDataReferencesResponse>>;

/// <summary>
/// Handler cho GetDataReferencesQuery - Chỉ sử dụng Auto Authentication
/// </summary>
public class GetDataReferencesQueryHandler(
    IMobiFoneInvoiceService mobiFoneInvoiceService,
    IMobiFoneAuthenticationCacheService authCacheService,
    ILogger<GetDataReferencesQueryHandler> logger)
    : IRequestHandler<GetDataReferencesQuery, Response<GetDataReferencesResponse>>
{
    public async Task<Response<GetDataReferencesResponse>> Handle(GetDataReferencesQuery request, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Getting data references with auto authentication for TaxCode: {TaxCode}", request.TaxCode);

            // Lấy authentication info từ cache
            var authInfo = await authCacheService.GetAuthenticationAsync(request.TaxCode, cancellationToken);

            if (authInfo == null)
            {
                logger.LogError("Failed to get authentication for TaxCode: {TaxCode}", request.TaxCode);
                return new Response<GetDataReferencesResponse>
                {
                    Code = ErrorCodes.UNAUTHORIZED_ERROR,
                    Message = $"Failed to authenticate for tax code: {request.TaxCode}"
                };
            }

            logger.LogInformation("Successfully retrieved authentication for GetDataReferences - TaxCode: {TaxCode}, Token expires in {Minutes} minutes",
                request.TaxCode, authInfo.MinutesUntilExpiry);

            // Gọi service với token và maDvcs từ cache
            return await mobiFoneInvoiceService.GetDataReferencesAsync(
                request.Request,
                authInfo.Token,
                authInfo.MaDvcs,
                request.TaxCode,
                cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting data references with auto authentication for TaxCode: {TaxCode}", request.TaxCode);
            return new Response<GetDataReferencesResponse>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while getting data references with auto authentication."
            };
        }
    }
}
