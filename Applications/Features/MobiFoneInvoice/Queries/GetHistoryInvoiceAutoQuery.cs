using Applications.DTOs.MobiFoneInvoice.GetHistoryInvoice.Raws;
using Applications.Interfaces.Services;
using Applications.Interfaces;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;
using Shared.Interfaces;
using Shared.Constants;
using Microsoft.Extensions.Logging;

namespace Applications.Features.MobiFoneInvoice.Queries;

/// <summary>
/// Query để lấy lịch sử hóa đơn trong MobiFone Invoice API - Chỉ sử dụng Auto Authentication
/// API 4.20: GetHistoryInvoice
/// </summary>
public record GetHistoryInvoiceAutoQuery(
    string InvoiceId,
    string TaxCode) : IRequest<Response<List<GetHistoryInvoiceResponse>>>;

/// <summary>
/// Validator cho GetHistoryInvoiceAutoQuery
/// </summary>
public class GetHistoryInvoiceAutoQueryValidator : AbstractValidator<GetHistoryInvoiceAutoQuery>
{
    public GetHistoryInvoiceAutoQueryValidator()
    {
        RuleFor(x => x.InvoiceId)
            .NotEmpty()
            .WithMessage("ID hóa đơn không được để trống")
            .Must(BeValidGuid)
            .WithMessage("ID hóa đơn phải là định dạng GUID hợp lệ");

        RuleFor(x => x.TaxCode)
            .NotEmpty()
            .WithMessage("TaxCode là bắt buộc cho auto authentication");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }
}

/// <summary>
/// Handler cho GetHistoryInvoiceAutoQuery - Chỉ sử dụng Auto Authentication
/// </summary>
public class GetHistoryInvoiceAutoQueryHandler(
    IMobiFoneInvoiceService mobiFoneInvoiceService,
    IMobiFoneAuthenticationCacheService authCacheService,
    ILogger<GetHistoryInvoiceAutoQueryHandler> logger)
    : IRequestHandler<GetHistoryInvoiceAutoQuery, Response<List<GetHistoryInvoiceResponse>>>
{
    private readonly IMobiFoneInvoiceService _mobiFoneInvoiceService = mobiFoneInvoiceService;
    private readonly IMobiFoneAuthenticationCacheService _authCacheService = authCacheService;
    private readonly ILogger<GetHistoryInvoiceAutoQueryHandler> _logger = logger;

    public async Task<Response<List<GetHistoryInvoiceResponse>>> Handle(
        GetHistoryInvoiceAutoQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Get history invoice with auto authentication for TaxCode: {TaxCode}, InvoiceId: {InvoiceId}",
                request.TaxCode, request.InvoiceId);

            // 1. Validate TaxCode
            if (string.IsNullOrEmpty(request.TaxCode))
            {
                _logger.LogError("TaxCode is required for auto authentication");
                return new Response<List<GetHistoryInvoiceResponse>>
                {
                    Code = ErrorCodes.BAD_REQUEST_ERROR,
                    Message = "TaxCode is required for auto authentication"
                };
            }

            // 2. Lấy authentication info từ cache
            var authInfo = await _authCacheService.GetAuthenticationAsync(request.TaxCode, cancellationToken);

            if (authInfo == null)
            {
                _logger.LogError("Failed to get authentication for TaxCode: {TaxCode}", request.TaxCode);
                return new Response<List<GetHistoryInvoiceResponse>>
                {
                    Code = ErrorCodes.UNAUTHORIZED_ERROR,
                    Message = $"Failed to authenticate for tax code: {request.TaxCode}"
                };
            }

            _logger.LogInformation("Successfully retrieved authentication for GetHistoryInvoice - TaxCode: {TaxCode}, Token expires in {Minutes} minutes",
                request.TaxCode, authInfo.MinutesUntilExpiry);

            // 3. Gọi service với token và maDvcs từ cache
            var result = await _mobiFoneInvoiceService.GetHistoryInvoiceAsync(
                request.InvoiceId,
                authInfo.Token,
                authInfo.MaDvcs,
                cancellationToken);

            _logger.LogInformation("Successfully retrieved history invoice for TaxCode: {TaxCode}, InvoiceId: {InvoiceId}, Result: {Code}, RecordCount: {Count}",
                request.TaxCode, request.InvoiceId, result.Code, result.Data?.Count ?? 0);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting history invoice with auto authentication for TaxCode: {TaxCode}",
                request.TaxCode);
            return new Response<List<GetHistoryInvoiceResponse>>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while getting history invoice with auto authentication."
            };
        }
    }
}
