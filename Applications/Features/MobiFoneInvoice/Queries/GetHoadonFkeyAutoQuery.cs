using Applications.DTOs.MobiFoneInvoice.GetHoadonFkey.Raws;
using Applications.Interfaces.Services;
using Applications.Interfaces;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;
using Shared.Interfaces;
using Shared.Constants;
using Microsoft.Extensions.Logging;

namespace Applications.Features.MobiFoneInvoice.Queries;

/// <summary>
/// Query để lấy danh sách hóa đơn theo FKEY hoặc khoảng thời gian trong MobiFone Invoice API - Chỉ sử dụng Auto Authentication
/// API 4.14: GetHoadonFkey
/// </summary>
public record GetHoadonFkeyAutoQuery(
    GetHoadonFkeyRequest Request,
    string TaxCode) : IRequest<Response<GetHoadonFkeyResponse>>;

/// <summary>
/// Validator cho GetHoadonFkeyAutoQuery
/// </summary>
public class GetHoadonFkeyAutoQueryValidator : AbstractValidator<GetHoadonFkeyAutoQuery>
{
    public GetHoadonFkeyAutoQueryValidator()
    {
        // Validate either hdon_id OR date range is provided
        RuleFor(x => x.Request)
            .Must(HaveValidSearchCriteria)
            .WithMessage("Phải cung cấp hdon_id hoặc khoảng thời gian (tu_ngay và den_ngay)");

        RuleFor(x => x.Request.hdon_id)
            .Must(BeValidGuidOrEmpty)
            .When(x => !string.IsNullOrEmpty(x.Request.hdon_id))
            .WithMessage("hdon_id phải là định dạng GUID hợp lệ");

        RuleFor(x => x.Request.tu_ngay)
            .Must(BeValidDateFormat)
            .When(x => !string.IsNullOrEmpty(x.Request.tu_ngay))
            .WithMessage("tu_ngay phải có định dạng yyyy-MM-dd");

        RuleFor(x => x.Request.den_ngay)
            .Must(BeValidDateFormat)
            .When(x => !string.IsNullOrEmpty(x.Request.den_ngay))
            .WithMessage("den_ngay phải có định dạng yyyy-MM-dd");

        // If using date range, both dates must be provided
        RuleFor(x => x.Request)
            .Must(HaveCompleteDateRange)
            .When(x => !string.IsNullOrEmpty(x.Request.tu_ngay) || !string.IsNullOrEmpty(x.Request.den_ngay))
            .WithMessage("Khi sử dụng khoảng thời gian, phải cung cấp cả tu_ngay và den_ngay");

        // Date range validation
        RuleFor(x => x.Request)
            .Must(HaveValidDateRange)
            .When(x => !string.IsNullOrEmpty(x.Request.tu_ngay) && !string.IsNullOrEmpty(x.Request.den_ngay))
            .WithMessage("den_ngay phải lớn hơn hoặc bằng tu_ngay");

        RuleFor(x => x.TaxCode)
            .NotEmpty()
            .WithMessage("TaxCode là bắt buộc cho auto authentication");
    }

    private static bool HaveValidSearchCriteria(GetHoadonFkeyRequest request)
    {
        // Either hdon_id is provided OR both date fields are provided
        var hasHdonId = !string.IsNullOrEmpty(request.hdon_id);
        var hasDateRange = !string.IsNullOrEmpty(request.tu_ngay) && !string.IsNullOrEmpty(request.den_ngay);
        return hasHdonId || hasDateRange;
    }

    private static bool BeValidGuidOrEmpty(string? id)
    {
        return string.IsNullOrEmpty(id) || Guid.TryParse(id, out _);
    }

    private static bool BeValidDateFormat(string? date)
    {
        return string.IsNullOrEmpty(date) || DateTime.TryParseExact(date, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out _);
    }

    private static bool HaveCompleteDateRange(GetHoadonFkeyRequest request)
    {
        var hasTuNgay = !string.IsNullOrEmpty(request.tu_ngay);
        var hasDenNgay = !string.IsNullOrEmpty(request.den_ngay);
        
        // If one is provided, both must be provided
        if (hasTuNgay || hasDenNgay)
        {
            return hasTuNgay && hasDenNgay;
        }
        
        return true; // Both empty is fine
    }

    private static bool HaveValidDateRange(GetHoadonFkeyRequest request)
    {
        if (DateTime.TryParseExact(request.tu_ngay, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out var fromDate) &&
            DateTime.TryParseExact(request.den_ngay, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out var toDate))
        {
            return toDate >= fromDate;
        }
        return true; // If parsing fails, let other validators handle it
    }
}

/// <summary>
/// Handler cho GetHoadonFkeyAutoQuery - Chỉ sử dụng Auto Authentication
/// </summary>
public class GetHoadonFkeyAutoQueryHandler(
    IMobiFoneInvoiceService mobiFoneInvoiceService,
    IMobiFoneAuthenticationCacheService authCacheService,
    ILogger<GetHoadonFkeyAutoQueryHandler> logger)
    : IRequestHandler<GetHoadonFkeyAutoQuery, Response<GetHoadonFkeyResponse>>
{
    private readonly IMobiFoneInvoiceService _mobiFoneInvoiceService = mobiFoneInvoiceService;
    private readonly IMobiFoneAuthenticationCacheService _authCacheService = authCacheService;
    private readonly ILogger<GetHoadonFkeyAutoQueryHandler> _logger = logger;

    public async Task<Response<GetHoadonFkeyResponse>> Handle(
        GetHoadonFkeyAutoQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            var searchType = !string.IsNullOrEmpty(request.Request.hdon_id) ? "FKEY" : "DateRange";
            var searchValue = !string.IsNullOrEmpty(request.Request.hdon_id) 
                ? request.Request.hdon_id 
                : $"{request.Request.tu_ngay} to {request.Request.den_ngay}";

            _logger.LogInformation("Get hoadon fkey with auto authentication for TaxCode: {TaxCode}, SearchType: {SearchType}, SearchValue: {SearchValue}",
                request.TaxCode, searchType, searchValue);

            // 1. Validate TaxCode
            if (string.IsNullOrEmpty(request.TaxCode))
            {
                _logger.LogError("TaxCode is required for auto authentication");
                return new Response<GetHoadonFkeyResponse>
                {
                    Code = ErrorCodes.BAD_REQUEST_ERROR,
                    Message = "TaxCode is required for auto authentication"
                };
            }

            // 2. Lấy authentication info từ cache
            var authInfo = await _authCacheService.GetAuthenticationAsync(request.TaxCode, cancellationToken);

            if (authInfo == null)
            {
                _logger.LogError("Failed to get authentication for TaxCode: {TaxCode}", request.TaxCode);
                return new Response<GetHoadonFkeyResponse>
                {
                    Code = ErrorCodes.UNAUTHORIZED_ERROR,
                    Message = $"Failed to authenticate for tax code: {request.TaxCode}"
                };
            }

            _logger.LogInformation("Successfully retrieved authentication for GetHoadonFkey - TaxCode: {TaxCode}, Token expires in {Minutes} minutes",
                request.TaxCode, authInfo.MinutesUntilExpiry);

            // 3. Gọi service với token và maDvcs từ cache
            var result = await _mobiFoneInvoiceService.GetHoadonFkeyAsync(
                request.Request,
                authInfo.Token,
                authInfo.MaDvcs,
                cancellationToken);

            _logger.LogInformation("Successfully retrieved hoadon fkey for TaxCode: {TaxCode}, SearchType: {SearchType}, Result: {Code}, RecordCount: {Count}",
                request.TaxCode, searchType, result.Code, result.Data?.Count ?? 0);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting hoadon fkey with auto authentication for TaxCode: {TaxCode}",
                request.TaxCode);
            return new Response<GetHoadonFkeyResponse>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while getting hoadon fkey with auto authentication."
            };
        }
    }
}
