﻿using Applications.DTOs.MobiFoneInvoice.DownloadInvoicePDF.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MobiFoneInvoice.Queries;

/// <summary>
/// Query Ä‘á»ƒ táº£i hóa đơn File .PDF trong MobiFone Invoice API
/// API 4.9: inHoadon
/// </summary>
public record DownloadInvoicePDFQuery(
    DownloadInvoicePDFRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<byte[]>>;

/// <summary>
/// Validator cho DownloadInvoicePDFQuery
/// </summary>
public class DownloadInvoicePDFQueryValidator : AbstractValidator<DownloadInvoicePDFQuery>
{
    public DownloadInvoicePDFQueryValidator()
    {
        RuleFor(x => x.Request.id)
            .NotEmpty()
            .WithMessage("ID hóa đơn không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng")
            .Must(BeValidGuid)
            .WithMessage("ID hóa đơn phải là  Ä‘á»‹nh dáº¡ng GUID há»£p lá»‡");

        RuleFor(x => x.Request.type)
            .NotEmpty()
            .WithMessage("Loáº¡i file không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng")
            .Must(x => x.Equals("PDF", StringComparison.OrdinalIgnoreCase))
            .WithMessage("Loáº¡i file phải là  PDF");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vá»‹ không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }
}

/// <summary>
/// Handler cho DownloadInvoicePDFQuery
/// </summary>
public class DownloadInvoicePDFQueryHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<DownloadInvoicePDFQuery, Response<byte[]>>
{
    public async Task<Response<byte[]>> Handle(
        DownloadInvoicePDFQuery request,
        CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.DownloadInvoicePDFAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
