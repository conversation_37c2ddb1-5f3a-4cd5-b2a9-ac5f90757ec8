﻿using Applications.DTOs.MobiFoneInvoice.GetHoadonFkey.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MobiFoneInvoice.Queries;

/// <summary>
/// Query Ä‘á»ƒ láº¥y danh sÃ¡ch hóa đơn theo FKEY hoáº·c Khoáº£ng thá»i gian trong MobiFone Invoice API
/// API 4.14: GetHoadonFkey
/// </summary>
public record GetHoadonFkeyQuery(
    GetHoadonFkeyRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<GetHoadonFkeyResponse>>;

/// <summary>
/// Validator cho GetHoadonFkeyQuery
/// </summary>
public class GetHoadonFkeyQueryValidator : AbstractValidator<GetHoadonFkeyQuery>
{
    public GetHoadonFkeyQueryValidator()
    {
        RuleFor(x => x.Request)
            .Must(HaveValidSearchCriteria)
            .WithMessage("phải cung cáº¥p hdon_id HOáº¶C cáº£ tu_ngay vÃ  den_ngay");

        When(x => !string.IsNullOrEmpty(x.Request.hdon_id), () =>
        {
            RuleFor(x => x.Request.hdon_id)
                .NotEmpty()
                .WithMessage("FKEY hóa đơn không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng");
        });

        When(x => string.IsNullOrEmpty(x.Request.hdon_id), () =>
        {
            RuleFor(x => x.Request.tu_ngay)
                .NotEmpty()
                .WithMessage("Tá»« ngÃ y không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng khi không cÃ³ FKEY")
                .Must(BeValidDate)
                .WithMessage("Tá»« ngÃ y phải cÃ³ Ä‘á»‹nh dáº¡ng yyyy-MM-dd");

            RuleFor(x => x.Request.den_ngay)
                .NotEmpty()
                .WithMessage("Äáº¿n ngÃ y không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng khi không cÃ³ FKEY")
                .Must(BeValidDate)
                .WithMessage("Äáº¿n ngÃ y phải cÃ³ Ä‘á»‹nh dáº¡ng yyyy-MM-dd");

            RuleFor(x => x.Request)
                .Must(x => BeValidDateRange(x.tu_ngay, x.den_ngay))
                .WithMessage("Tá»« ngÃ y phải nhá» hÆ¡n hoáº·c báº±ng Ä‘áº¿n ngÃ y");
        });

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vá»‹ không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng");
    }

    private static bool HaveValidSearchCriteria(GetHoadonFkeyRequest request)
    {
        return !string.IsNullOrEmpty(request.hdon_id) ||
               (!string.IsNullOrEmpty(request.tu_ngay) && !string.IsNullOrEmpty(request.den_ngay));
    }

    private static bool BeValidDate(string? date)
    {
        if (string.IsNullOrEmpty(date))
            return false;

        return DateTime.TryParseExact(date, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out _);
    }

    private static bool BeValidDateRange(string? tuNgay, string? denNgay)
    {
        if (string.IsNullOrEmpty(tuNgay) || string.IsNullOrEmpty(denNgay))
            return false;

        if (!DateTime.TryParseExact(tuNgay, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out var fromDate) ||
            !DateTime.TryParseExact(denNgay, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out var toDate))
            return false;

        return fromDate <= toDate;
    }
}

/// <summary>
/// Handler cho GetHoadonFkeyQuery
/// </summary>
public class GetHoadonFkeyQueryHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<GetHoadonFkeyQuery, Response<GetHoadonFkeyResponse>>
{
    public async Task<Response<GetHoadonFkeyResponse>> Handle(
        GetHoadonFkeyQuery request,
        CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.GetHoadonFkeyAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
