using Applications.DTOs.MobiFoneInvoice.DownloadInvoicePDF.Raws;
using Applications.Interfaces.Services;
using Applications.Interfaces;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;
using Shared.Interfaces;
using Shared.Constants;
using Microsoft.Extensions.Logging;

namespace Applications.Features.MobiFoneInvoice.Queries;

/// <summary>
/// Query để tải PDF hóa đơn trong MobiFone Invoice API - Chỉ sử dụng Auto Authentication
/// API 4.9: DownloadInvoicePDF
/// </summary>
public record DownloadInvoicePDFAutoQuery(
    string InvoiceId,
    string TaxCode) : IRequest<Response<byte[]>>;

/// <summary>
/// Validator cho DownloadInvoicePDFAutoQuery
/// </summary>
public class DownloadInvoicePDFAutoQueryValidator : AbstractValidator<DownloadInvoicePDFAutoQuery>
{
    public DownloadInvoicePDFAutoQueryValidator()
    {
        RuleFor(x => x.InvoiceId)
            .NotEmpty()
            .WithMessage("ID hóa đơn không được để trống")
            .Must(BeValidGuid)
            .WithMessage("ID hóa đơn phải là định dạng GUID hợp lệ");

        RuleFor(x => x.TaxCode)
            .NotEmpty()
            .WithMessage("TaxCode là bắt buộc cho auto authentication");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }
}

/// <summary>
/// Handler cho DownloadInvoicePDFAutoQuery - Chỉ sử dụng Auto Authentication
/// </summary>
public class DownloadInvoicePDFAutoQueryHandler(
    IMobiFoneInvoiceService mobiFoneInvoiceService,
    IMobiFoneAuthenticationCacheService authCacheService,
    ILogger<DownloadInvoicePDFAutoQueryHandler> logger)
    : IRequestHandler<DownloadInvoicePDFAutoQuery, Response<byte[]>>
{
    private readonly IMobiFoneInvoiceService _mobiFoneInvoiceService = mobiFoneInvoiceService;
    private readonly IMobiFoneAuthenticationCacheService _authCacheService = authCacheService;
    private readonly ILogger<DownloadInvoicePDFAutoQueryHandler> _logger = logger;

    public async Task<Response<byte[]>> Handle(
        DownloadInvoicePDFAutoQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Download invoice PDF with auto authentication for TaxCode: {TaxCode}, InvoiceId: {InvoiceId}",
                request.TaxCode, request.InvoiceId);

            // 1. Validate TaxCode
            if (string.IsNullOrEmpty(request.TaxCode))
            {
                _logger.LogError("TaxCode is required for auto authentication");
                return new Response<byte[]>
                {
                    Code = ErrorCodes.BAD_REQUEST_ERROR,
                    Message = "TaxCode is required for auto authentication"
                };
            }

            // 2. Lấy authentication info từ cache
            var authInfo = await _authCacheService.GetAuthenticationAsync(request.TaxCode, cancellationToken);

            if (authInfo == null)
            {
                _logger.LogError("Failed to get authentication for TaxCode: {TaxCode}", request.TaxCode);
                return new Response<byte[]>
                {
                    Code = ErrorCodes.UNAUTHORIZED_ERROR,
                    Message = $"Failed to authenticate for tax code: {request.TaxCode}"
                };
            }

            _logger.LogInformation("Successfully retrieved authentication for DownloadInvoicePDF - TaxCode: {TaxCode}, Token expires in {Minutes} minutes",
                request.TaxCode, authInfo.MinutesUntilExpiry);

            // 3. Tạo request object
            var downloadRequest = new DownloadInvoicePDFRequest
            {
                id = request.InvoiceId,
                type = "PDF",
                inchuyendoi = false
            };

            // 4. Gọi service với token và maDvcs từ cache
            var result = await _mobiFoneInvoiceService.DownloadInvoicePDFAsync(
                downloadRequest,
                authInfo.Token,
                authInfo.MaDvcs,
                cancellationToken);

            _logger.LogInformation("Successfully downloaded invoice PDF for TaxCode: {TaxCode}, InvoiceId: {InvoiceId}, Result: {Code}, Size: {Size} bytes",
                request.TaxCode, request.InvoiceId, result.Code, result.Data?.Length ?? 0);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while downloading invoice PDF with auto authentication for TaxCode: {TaxCode}, InvoiceId: {InvoiceId}",
                request.TaxCode, request.InvoiceId);
            return new Response<byte[]>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while downloading invoice PDF with auto authentication."
            };
        }
    }
}
