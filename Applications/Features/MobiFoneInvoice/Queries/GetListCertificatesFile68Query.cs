﻿using Applications.DTOs.MobiFoneInvoice.GetListCertificatesFile68;
using Applications.Interfaces.Services;
using Applications.Interfaces;
using MediatR;
using BuildingBlocks.Abstractions;
using Shared.Constants;
using Shared.Interfaces;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;

namespace Applications.Features.MobiFoneInvoice.Queries;

/// <summary>
/// Query Ä‘á»ƒ láº¥y thÃ´ng tin CKS tá»« MobiFone Invoice API
/// </summary>
public record GetListCertificatesFile68Query(
    string TaxCode) : IRequest<Response<GetListCertificatesFile68Response>>;

/// <summary>
/// Handler cho GetListCertificatesFile68Query - Chỉ sử dụng Auto Authentication
/// </summary>
public class GetListCertificatesFile68QueryHandler(
    IMobiFoneInvoiceService mobiFoneInvoiceService,
    IMobiFoneAuthenticationCacheService authCacheService,
    ILogger<GetListCertificatesFile68QueryHandler> logger)
    : IRequestHandler<GetListCertificatesFile68Query, Response<GetListCertificatesFile68Response>>
{
    public async Task<Response<GetListCertificatesFile68Response>> Handle(
        GetListCertificatesFile68Query request,
        CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Getting certificates with auto authentication for TaxCode: {TaxCode}", request.TaxCode);

            // Lấy authentication info từ cache
            var authInfo = await authCacheService.GetAuthenticationAsync(request.TaxCode, cancellationToken);

            if (authInfo == null)
            {
                logger.LogError("Failed to get authentication for TaxCode: {TaxCode}", request.TaxCode);
                return new Response<GetListCertificatesFile68Response>
                {
                    Code = ErrorCodes.UNAUTHORIZED_ERROR,
                    Message = $"Failed to authenticate for tax code: {request.TaxCode}"
                };
            }

            logger.LogInformation("Successfully retrieved authentication for GetListCertificatesFile68 - TaxCode: {TaxCode}, Token expires in {Minutes} minutes",
                request.TaxCode, authInfo.MinutesUntilExpiry);

            // Gọi service với token và maDvcs từ cache
            return await mobiFoneInvoiceService.GetListCertificatesFile68Async(
                authInfo.Token,
                authInfo.MaDvcs,
                request.TaxCode,
                cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting certificates with auto authentication for TaxCode: {TaxCode}", request.TaxCode);
            return new Response<GetListCertificatesFile68Response>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while getting certificates with auto authentication."
            };
        }
    }
}
