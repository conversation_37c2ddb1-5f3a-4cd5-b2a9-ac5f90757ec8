﻿using Applications.DTOs.MobiFoneInvoice.GetHistoryInvoice.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MobiFoneInvoice.Queries;

/// <summary>
/// Query Ä‘á»ƒ láº¥y danh sÃ¡ch lá»‹ch sá»­ hóa đơn theo ID trong MobiFone Invoice API
/// API 4.20: GetHistoryInvoice
/// </summary>
public record GetHistoryInvoiceQuery(
    string Id,
    string Token,
    string MaDvcs) : IRequest<Response<List<GetHistoryInvoiceResponse>>>;

/// <summary>
/// Validator cho GetHistoryInvoiceQuery
/// </summary>
public class GetHistoryInvoiceQueryValidator : AbstractValidator<GetHistoryInvoiceQuery>
{
    public GetHistoryInvoiceQueryValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("Id hóa đơn là  bắt buộc")
            .Must(BeValidGuid)
            .WithMessage("Id hóa đơn phải là  Ä‘á»‹nh dáº¡ng GUID há»£p lá»‡");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token là  bắt buộc");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vá»‹ là  bắt buộc");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }
}

/// <summary>
/// Handler cho GetHistoryInvoiceQuery
/// </summary>
public class GetHistoryInvoiceQueryHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<GetHistoryInvoiceQuery, Response<List<GetHistoryInvoiceResponse>>>
{
    public async Task<Response<List<GetHistoryInvoiceResponse>>> Handle(GetHistoryInvoiceQuery request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.GetHistoryInvoiceAsync(
            request.Id,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
