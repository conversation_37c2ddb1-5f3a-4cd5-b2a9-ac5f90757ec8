using Applications.DTOs.MobiFoneInvoice.GetInvoiceById.Raws;
using Applications.Interfaces.Services;
using Applications.Interfaces;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;
using Shared.Constants;
using Shared.Interfaces;
using Microsoft.Extensions.Logging;

namespace Applications.Features.MobiFoneInvoice.Queries;

/// <summary>
/// Query để lấy thông tin hóa đơn theo ID trong MobiFone Invoice API
/// API 4.13: GetById - Chỉ sử dụng Auto Authentication
/// </summary>
public record GetInvoiceByIdQuery(
    GetInvoiceByIdRequest Request,
    string TaxCode) : IRequest<Response<GetInvoiceByIdResponse>>;

/// <summary>
/// Validator cho GetInvoiceByIdQuery
/// </summary>
public class GetInvoiceByIdQueryValidator : AbstractValidator<GetInvoiceByIdQuery>
{
    public GetInvoiceByIdQueryValidator()
    {
        RuleFor(x => x.Request.id)
            .NotEmpty()
            .WithMessage("ID hóa đơn không được để trống")
            .Must(BeValidGuid)
            .WithMessage("ID hóa đơn phải là định dạng GUID hợp lệ");

        RuleFor(x => x.TaxCode)
            .NotEmpty()
            .WithMessage("TaxCode là bắt buộc cho auto authentication");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }
}

/// <summary>
/// Handler cho GetInvoiceByIdQuery - Chỉ sử dụng Auto Authentication
/// </summary>
public class GetInvoiceByIdQueryHandler(
    IMobiFoneInvoiceService mobiFoneInvoiceService,
    IMobiFoneAuthenticationCacheService authCacheService,
    ILogger<GetInvoiceByIdQueryHandler> logger)
    : IRequestHandler<GetInvoiceByIdQuery, Response<GetInvoiceByIdResponse>>
{
    public async Task<Response<GetInvoiceByIdResponse>> Handle(
        GetInvoiceByIdQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Get invoice by ID with auto authentication for TaxCode: {TaxCode}", request.TaxCode);

            // Lấy authentication info từ cache
            var authInfo = await authCacheService.GetAuthenticationAsync(request.TaxCode, cancellationToken);
            
            if (authInfo == null)
            {
                logger.LogError("Failed to get authentication for TaxCode: {TaxCode}", request.TaxCode);
                return new Response<GetInvoiceByIdResponse>
                {
                    Code = ErrorCodes.UNAUTHORIZED_ERROR,
                    Message = $"Failed to authenticate for tax code: {request.TaxCode}"
                };
            }

            logger.LogInformation("Successfully retrieved authentication for GetInvoiceById - TaxCode: {TaxCode}, Token expires in {Minutes} minutes", 
                request.TaxCode, authInfo.MinutesUntilExpiry);

            // Gọi service với token và maDvcs từ cache
            return await mobiFoneInvoiceService.GetInvoiceByIdAsync(
                request.Request,
                authInfo.Token,
                authInfo.MaDvcs,
                cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while get invoice by ID with auto authentication for TaxCode: {TaxCode}", request.TaxCode);
            return new Response<GetInvoiceByIdResponse>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while get invoice by ID with auto authentication."
            };
        }
    }
}
