using Applications.DTOs.MobiFoneInvoice.GetInvoiceByTimeAndUnit.Raws;
using Applications.Interfaces.Services;
using Applications.Interfaces;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;
using Shared.Interfaces;
using Shared.Constants;
using Microsoft.Extensions.Logging;

namespace Applications.Features.MobiFoneInvoice.Queries;

/// <summary>
/// Query để lấy danh sách hóa đơn theo thời gian, đơn vị và trạng thái trong MobiFone Invoice API - Chỉ sử dụng Auto Authentication
/// API 4.17: GetInvoiceByTimeAndUnit
/// </summary>
public record GetInvoiceByTimeAndUnitAutoQuery(
    GetInvoiceByTimeAndUnitRequest Request,
    string TaxCode) : IRequest<Response<GetInvoiceByTimeAndUnitResponse>>;

/// <summary>
/// Validator cho GetInvoiceByTimeAndUnitAutoQuery
/// </summary>
public class GetInvoiceByTimeAndUnitAutoQueryValidator : AbstractValidator<GetInvoiceByTimeAndUnitAutoQuery>
{
    public GetInvoiceByTimeAndUnitAutoQueryValidator()
    {
        RuleFor(x => x.Request.tu_ngay)
            .NotEmpty()
            .WithMessage("tu_ngay là bắt buộc")
            .Must(BeValidDateFormat)
            .WithMessage("tu_ngay phải có định dạng yyyy-MM-dd");

        RuleFor(x => x.Request.den_ngay)
            .NotEmpty()
            .WithMessage("den_ngay là bắt buộc")
            .Must(BeValidDateFormat)
            .WithMessage("den_ngay phải có định dạng yyyy-MM-dd");

        RuleFor(x => x.Request)
            .Must(HaveValidDateRange)
            .WithMessage("den_ngay phải lớn hơn hoặc bằng tu_ngay");



        RuleFor(x => x.TaxCode)
            .NotEmpty()
            .WithMessage("TaxCode là bắt buộc cho auto authentication");
    }

    private static bool BeValidDateFormat(string date)
    {
        return DateTime.TryParseExact(date, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out _);
    }

    private static bool HaveValidDateRange(GetInvoiceByTimeAndUnitRequest request)
    {
        if (DateTime.TryParseExact(request.tu_ngay, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out var fromDate) &&
            DateTime.TryParseExact(request.den_ngay, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out var toDate))
        {
            return toDate >= fromDate;
        }
        return true; // If parsing fails, let other validators handle it
    }


}

/// <summary>
/// Handler cho GetInvoiceByTimeAndUnitAutoQuery - Chỉ sử dụng Auto Authentication
/// </summary>
public class GetInvoiceByTimeAndUnitAutoQueryHandler(
    IMobiFoneInvoiceService mobiFoneInvoiceService,
    IMobiFoneAuthenticationCacheService authCacheService,
    ILogger<GetInvoiceByTimeAndUnitAutoQueryHandler> logger)
    : IRequestHandler<GetInvoiceByTimeAndUnitAutoQuery, Response<GetInvoiceByTimeAndUnitResponse>>
{
    private readonly IMobiFoneInvoiceService _mobiFoneInvoiceService = mobiFoneInvoiceService;
    private readonly IMobiFoneAuthenticationCacheService _authCacheService = authCacheService;
    private readonly ILogger<GetInvoiceByTimeAndUnitAutoQueryHandler> _logger = logger;

    public async Task<Response<GetInvoiceByTimeAndUnitResponse>> Handle(
        GetInvoiceByTimeAndUnitAutoQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Get invoice by time and unit with auto authentication for TaxCode: {TaxCode}, DateRange: {FromDate} to {ToDate}",
                request.TaxCode, request.Request.tu_ngay, request.Request.den_ngay);

            // 1. Validate TaxCode
            if (string.IsNullOrEmpty(request.TaxCode))
            {
                _logger.LogError("TaxCode is required for auto authentication");
                return new Response<GetInvoiceByTimeAndUnitResponse>
                {
                    Code = ErrorCodes.BAD_REQUEST_ERROR,
                    Message = "TaxCode is required for auto authentication"
                };
            }

            // 2. Lấy authentication info từ cache
            var authInfo = await _authCacheService.GetAuthenticationAsync(request.TaxCode, cancellationToken);

            if (authInfo == null)
            {
                _logger.LogError("Failed to get authentication for TaxCode: {TaxCode}", request.TaxCode);
                return new Response<GetInvoiceByTimeAndUnitResponse>
                {
                    Code = ErrorCodes.UNAUTHORIZED_ERROR,
                    Message = $"Failed to authenticate for tax code: {request.TaxCode}"
                };
            }

            _logger.LogInformation("Successfully retrieved authentication for GetInvoiceByTimeAndUnit - TaxCode: {TaxCode}, Token expires in {Minutes} minutes",
                request.TaxCode, authInfo.MinutesUntilExpiry);

            // 3. Gọi service với token và maDvcs từ cache
            var result = await _mobiFoneInvoiceService.GetInvoiceByTimeAndUnitAsync(
                request.Request,
                authInfo.Token,
                authInfo.MaDvcs,
                cancellationToken);

            _logger.LogInformation("Successfully retrieved invoices by time and unit for TaxCode: {TaxCode}, Result: {Code}, RecordCount: {Count}",
                request.TaxCode, result.Code, result.Data?.Count ?? 0);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting invoices by time and unit with auto authentication for TaxCode: {TaxCode}",
                request.TaxCode);
            return new Response<GetInvoiceByTimeAndUnitResponse>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while getting invoices by time and unit with auto authentication."
            };
        }
    }
}
