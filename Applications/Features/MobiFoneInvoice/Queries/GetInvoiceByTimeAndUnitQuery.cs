﻿using Applications.DTOs.MobiFoneInvoice.GetInvoiceByTimeAndUnit.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MobiFoneInvoice.Queries;

/// <summary>
/// Query Ä‘á»ƒ láº¥y danh sÃ¡ch hoÃ¡ đơn theo thá»i gian, đơn vá»‹ vÃ  tráº¡ng thÃ¡i trong MobiFone Invoice API
/// API 4.17: GetInvoiceByTimeAndUnit
/// </summary>
public record GetInvoiceByTimeAndUnitQuery(
    GetInvoiceByTimeAndUnitRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<GetInvoiceByTimeAndUnitResponse>>;

/// <summary>
/// Validator cho GetInvoiceByTimeAndUnitQuery
/// </summary>
public class GetInvoiceByTimeAndUnitQueryValidator : AbstractValidator<GetInvoiceByTimeAndUnitQuery>
{
    public GetInvoiceByTimeAndUnitQueryValidator()
    {
        RuleFor(x => x.Request.tu_ngay)
            .NotEmpty()
            .WithMessage("Tá»« ngÃ y không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng")
            .Must(BeValidDate)
            .WithMessage("Tá»« ngÃ y phải cÃ³ Ä‘á»‹nh dáº¡ng yyyy-MM-dd");

        RuleFor(x => x.Request.den_ngay)
            .NotEmpty()
            .WithMessage("Äáº¿n ngÃ y không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng")
            .Must(BeValidDate)
            .WithMessage("Äáº¿n ngÃ y phải cÃ³ Ä‘á»‹nh dáº¡ng yyyy-MM-dd");

        RuleFor(x => x.Request)
            .Must(x => BeValidDateRange(x.tu_ngay, x.den_ngay))
            .WithMessage("Tá»« ngÃ y phải nhá» hÆ¡n hoáº·c báº±ng Ä‘áº¿n ngÃ y");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vá»‹ không Ä‘Æ°á»£c Ä‘á»ƒ trá»‘ng");
    }

    private static bool BeValidDate(string date)
    {
        if (string.IsNullOrEmpty(date))
            return false;

        return DateTime.TryParseExact(date, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out _);
    }

    private static bool BeValidDateRange(string tuNgay, string denNgay)
    {
        if (string.IsNullOrEmpty(tuNgay) || string.IsNullOrEmpty(denNgay))
            return false;

        if (!DateTime.TryParseExact(tuNgay, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out var fromDate) ||
            !DateTime.TryParseExact(denNgay, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out var toDate))
            return false;

        return fromDate <= toDate;
    }
}

/// <summary>
/// Handler cho GetInvoiceByTimeAndUnitQuery
/// </summary>
public class GetInvoiceByTimeAndUnitQueryHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<GetInvoiceByTimeAndUnitQuery, Response<GetInvoiceByTimeAndUnitResponse>>
{
    public async Task<Response<GetInvoiceByTimeAndUnitResponse>> Handle(
        GetInvoiceByTimeAndUnitQuery request,
        CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.GetInvoiceByTimeAndUnitAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
