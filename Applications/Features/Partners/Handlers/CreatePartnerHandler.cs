﻿using Applications.DTOs.Partners;
using Applications.Features.Partners.Commands;
using Applications.Interfaces.Repositories;
using Applications.Interfaces.Services.Security;
using Core.Entities.Authentication;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Constants;
using BuildingBlocks.Abstractions;

namespace Applications.Features.Partners.Handlers;

/// <summary>
/// Handler for creating a new partner
/// </summary>
public class CreatePartnerHandler(IPartnerRepository partnerRepository,
    ISecretEncryptionService encryptionService,
    ILogger<CreatePartnerHandler> logger)
    : IRequestHandler<CreatePartnerCommand, Response<PartnerResponse>>
{

    public async Task<Response<PartnerResponse>> Handle(CreatePartnerCommand request, CancellationToken cancellationToken)
    {
        logger.LogInformation("Creating new partner with ClientId: {ClientId}", request.ClientId);

        try
        {
            // 1. Check if ClientId already exists
            var clientIdExists = await partnerRepository.ClientIdExistsAsync(request.ClientId, cancellationToken);

            if (clientIdExists)
            {
                logger.LogWarning("Partner with ClientId {ClientId} already exists", request.ClientId);
                var errorResponse = new Response<PartnerResponse>();
                errorResponse.Code = ErrorCodes.BAD_REQUEST_ERROR;
                errorResponse.Message = "ClientId already exists";
                return errorResponse;
            }

            // 2. Validate IP whitelist format if provided
            if (!string.IsNullOrEmpty(request.IpWhitelist))
            {
                if (!IsValidIpWhitelistFormat(request.IpWhitelist))
                {
                    var errorResponse = new Response<PartnerResponse>();
                    errorResponse.Code = ErrorCodes.BAD_REQUEST_ERROR;
                    errorResponse.Message = "Invalid IP whitelist format. Expected JSON array of IP addresses/CIDR ranges";
                    return errorResponse;
                }
            }

            // 3. Create new partner entity
            var partner = new Partner
            {
                Id = Guid.NewGuid(),
                ClientId = request.ClientId,
                ClientSecretHash = BCrypt.Net.BCrypt.HashPassword(request.ClientSecret), // Hash client secret with BCrypt
                HmacSecretHash = encryptionService.EncryptSecret(request.HmacSecret), // Encrypt HMAC secret with AES-256-GCM
                Name = request.Name,
                ContactEmail = request.ContactEmail,
                ContactPhone = request.ContactPhone,
                Description = request.Description,
                IpWhitelist = request.IpWhitelist ?? "[]",
                EnableIpWhitelist = request.EnableIpWhitelist,
                IsActive = request.IsActive,
                ApiRateLimitPerHour = request.ApiRateLimitPerHour,
                MonthlyInvoiceLimit = request.MonthlyInvoiceLimit,
                CurrentMonthUsage = 0,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                CreatedBy = request.CreatedBy,
                IsDeleted = false
            };

            // 4. Save to database
            await partnerRepository.AddAsync(partner);
            await partnerRepository.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Partner created successfully with ID: {PartnerId}", partner.Id);

            // 5. Map to response DTO (excludes sensitive data)
            var response = partner.Adapt<PartnerResponse>();

            return new Response<PartnerResponse>(response, "Partner created successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error creating partner with ClientId: {ClientId}", request.ClientId);
            var errorResponse = new Response<PartnerResponse>();
            errorResponse.Code = ErrorCodes.INTERNAL_SERVER_ERROR;
            errorResponse.Message = "Internal server error while creating partner";
            return errorResponse;
        }
    }

    /// <summary>
    /// Validates IP whitelist JSON format
    /// </summary>
    private static bool IsValidIpWhitelistFormat(string ipWhitelist)
    {
        try
        {
            var ips = System.Text.Json.JsonSerializer.Deserialize<string[]>(ipWhitelist);
            return ips != null;
        }
        catch
        {
            return false;
        }
    }
}
