using Applications.Features.Partners.Commands;
using FluentValidation;

namespace Applications.Features.Partners.Validators;

/// <summary>
/// Validator for CreatePartnerCommand
/// </summary>
public class CreatePartnerCommandValidator : AbstractValidator<CreatePartnerCommand>
{
    public CreatePartnerCommandValidator()
    {
        RuleFor(x => x.ClientId)
            .NotEmpty().WithMessage("ClientId is required")
            .MaximumLength(100).WithMessage("ClientId cannot exceed 100 characters")
            .Matches("^[a-zA-Z0-9_-]+$").WithMessage("ClientId can only contain letters, numbers, underscores, and hyphens");

        RuleFor(x => x.ClientSecret)
            .NotEmpty().WithMessage("ClientSecret is required")
            .MinimumLength(8).WithMessage("ClientSecret must be at least 8 characters")
            .MaximumLength(100).WithMessage("ClientSecret cannot exceed 100 characters");

        RuleFor(x => x.HmacSecret)
            .NotEmpty().WithMessage("HmacSecret is required")
            .MinimumLength(8).WithMessage("HmacSecret must be at least 8 characters")
            .MaximumLength(100).WithMessage("HmacSecret cannot exceed 100 characters");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Name is required")
            .MaximumLength(200).WithMessage("Name cannot exceed 200 characters");

        RuleFor(x => x.ContactEmail)
            .EmailAddress().WithMessage("Invalid email format")
            .MaximumLength(200).WithMessage("ContactEmail cannot exceed 200 characters")
            .When(x => !string.IsNullOrEmpty(x.ContactEmail));

        RuleFor(x => x.ContactPhone)
            .MaximumLength(20).WithMessage("ContactPhone cannot exceed 20 characters")
            .When(x => !string.IsNullOrEmpty(x.ContactPhone));

        RuleFor(x => x.Description)
            .MaximumLength(1000).WithMessage("Description cannot exceed 1000 characters")
            .When(x => !string.IsNullOrEmpty(x.Description));

        RuleFor(x => x.IpWhitelist)
            .Must(BeValidJsonArrayOrEmpty).WithMessage("IpWhitelist must be a valid JSON array of IP addresses/CIDR ranges")
            .When(x => !string.IsNullOrEmpty(x.IpWhitelist));

        RuleFor(x => x.ApiRateLimitPerHour)
            .GreaterThan(0).WithMessage("ApiRateLimitPerHour must be greater than 0")
            .LessThanOrEqualTo(100000).WithMessage("ApiRateLimitPerHour cannot exceed 100000");

        RuleFor(x => x.MonthlyInvoiceLimit)
            .GreaterThanOrEqualTo(0).WithMessage("MonthlyInvoiceLimit must be greater than or equal to 0")
            .LessThanOrEqualTo(10000000).WithMessage("MonthlyInvoiceLimit cannot exceed 10000000");

        RuleFor(x => x.CreatedBy)
            .NotEmpty().WithMessage("CreatedBy is required");
    }

    /// <summary>
    /// Validates if the string is a valid JSON array or empty/null
    /// </summary>
    private static bool BeValidJsonArrayOrEmpty(string? jsonString)
    {
        // Allow null or empty
        if (string.IsNullOrEmpty(jsonString))
            return true;

        try
        {
            // Try to deserialize as string array
            var array = System.Text.Json.JsonSerializer.Deserialize<string[]>(jsonString);
            return array != null;
        }
        catch (System.Text.Json.JsonException)
        {
            // If JSON parsing fails, return false
            return false;
        }
        catch (Exception)
        {
            // For any other exception, return false
            return false;
        }
    }
}
