using Applications.DTOs.MerchantBranchInvoiceAccount;
using Applications.Interfaces;
using Applications.Interfaces.Services;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Shared.Constants;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MerchantBranchInvoiceAccount.Queries;

/// <summary>
/// Query to get a merchant branch invoice account by ID
/// </summary>
public record GetMerchantBranchInvoiceAccountByIdQuery(Guid Id) : IRequest<Response<MerchantBranchInvoiceAccountResponseDto>>;

/// <summary>
/// Handler for GetMerchantBranchInvoiceAccountByIdQuery
/// </summary>
public class GetMerchantBranchInvoiceAccountByIdHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    ILogger<GetMerchantBranchInvoiceAccountByIdHandler> logger)
    : IRequestHandler<GetMerchantBranchInvoiceAccountByIdQuery, Response<MerchantBranchInvoiceAccountResponseDto>>
{
    public async Task<Response<MerchantBranchInvoiceAccountResponseDto>> Handle(
        GetMerchantBranchInvoiceAccountByIdQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Retrieving merchant branch invoice account with Id: {Id}", request.Id);

            // Get current partner ID
            var currentPartnerId = currentUserService.GetCurrentPartnerId();
            if (currentPartnerId == null)
            {
                logger.LogWarning("No current partner ID found for retrieving merchant branch invoice account");
                return new Response<MerchantBranchInvoiceAccountResponseDto>
                {
                    Code = ErrorCodes.UNAUTHORIZED_ERROR,
                    Message = "User is not authenticated or partner ID not found"
                };
            }

            // Filter by current partner ID and specific ID
            var entity = await dbContext.MerchantBranchInvoiceAccounts
                .FirstOrDefaultAsync(x => x.Id == request.Id && x.PartnerId == currentPartnerId.Value, cancellationToken);

            if (entity == null)
            {
                logger.LogWarning("Merchant branch invoice account with Id {Id} not found for partner {PartnerId}", 
                    request.Id, currentPartnerId);
                return new Response<MerchantBranchInvoiceAccountResponseDto>
                {
                    Code = ErrorCodes.NOT_FOUND_DATA,
                    Message = "Merchant branch invoice account not found"
                };
            }

            logger.LogInformation("Successfully retrieved merchant branch invoice account with Id: {Id} for partner {PartnerId}", 
                request.Id, currentPartnerId);

            var response = entity.Adapt<MerchantBranchInvoiceAccountResponseDto>();

            return new Response<MerchantBranchInvoiceAccountResponseDto>(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving merchant branch invoice account with Id: {Id}", request.Id);
            return new Response<MerchantBranchInvoiceAccountResponseDto>
            {
                Code = ErrorCodes.INTERNAL_SERVER_ERROR,
                Message = "An error occurred while retrieving the merchant branch invoice account"
            };
        }
    }
}
