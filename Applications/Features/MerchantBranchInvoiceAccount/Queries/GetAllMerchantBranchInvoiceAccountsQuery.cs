﻿using Applications.DTOs.MerchantBranchInvoiceAccount;
using Applications.Interfaces;
using Applications.Interfaces.Services;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Shared.Constants;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MerchantBranchInvoiceAccount.Queries;

/// <summary>
/// Query to get all merchant branch invoice accounts
/// </summary>
public record GetAllMerchantBranchInvoiceAccountsQuery() : IRequest<Response<List<MerchantBranchInvoiceAccountResponseDto>>>;

/// <summary>
/// Handler for GetAllMerchantBranchInvoiceAccountsQuery
/// </summary>
public class GetAllMerchantBranchInvoiceAccountsHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    ILogger<GetAllMerchantBranchInvoiceAccountsHandler> logger)
    : IRequestHandler<GetAllMerchantBranchInvoiceAccountsQuery, Response<List<MerchantBranchInvoiceAccountResponseDto>>>
{
    public async Task<Response<List<MerchantBranchInvoiceAccountResponseDto>>> Handle(
        GetAllMerchantBranchInvoiceAccountsQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Retrieving all merchant branch invoice accounts");

            // Get current partner ID
            var currentPartnerId = currentUserService.GetCurrentPartnerId()
                    ?? throw new UnauthorizedAccessException("User is not authenticated or partner ID not found");

            // Filter by current partner ID
            var entities = await dbContext.MerchantBranchInvoiceAccounts
                .Where(x => x.PartnerId == currentPartnerId)
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync(cancellationToken);

            logger.LogInformation("Found {Count} merchant branch invoice accounts for partner {PartnerId}",
                entities.Count, currentPartnerId);

            var responses = entities.Adapt<List<MerchantBranchInvoiceAccountResponseDto>>();

            return new Response<List<MerchantBranchInvoiceAccountResponseDto>>(responses);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving merchant branch invoice accounts");
            var errorResponse = new Response<List<MerchantBranchInvoiceAccountResponseDto>>
            {
                Code = ErrorCodes.INTERNAL_SERVER_ERROR,
                Message = "An error occurred while retrieving merchant branch invoice accounts"
            };
            return errorResponse;
        }
    }
}
