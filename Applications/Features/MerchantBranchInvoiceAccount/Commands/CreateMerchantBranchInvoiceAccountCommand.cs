﻿using Applications.DTOs.MerchantBranchInvoiceAccount;
using Applications.Interfaces;
using Applications.Interfaces.Services;
using FluentValidation;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Shared.Constants;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MerchantBranchInvoiceAccount.Commands;

/// <summary>
/// Command to create a new merchant branch invoice account
/// </summary>
public record CreateMerchantBranchInvoiceAccountCommand(
    MerchantBranchInvoiceAccountDto Request) : IRequest<Response<Guid>>;

/// <summary>
/// Validator for CreateMerchantBranchInvoiceAccountCommand
/// </summary>
public class CreateMerchantBranchInvoiceAccountCommandValidator : AbstractValidator<CreateMerchantBranchInvoiceAccountCommand>
{
    public CreateMerchantBranchInvoiceAccountCommandValidator()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request cannot be null");

        When(x => x.Request != null, () =>
        {
            RuleFor(x => x.Request.TaxNumber)
                .NotEmpty()
                .WithMessage("TaxNumber is required")
                .MaximumLength(50)
                .WithMessage("TaxNumber cannot exceed 50 characters");

            RuleFor(x => x.Request.InvoiceAccountUserName)
                .NotEmpty()
                .WithMessage("InvoiceAccountUserName is required")
                .MaximumLength(100)
                .WithMessage("InvoiceAccountUserName cannot exceed 100 characters");

            RuleFor(x => x.Request.InvoiceAccountPassword)
                .NotEmpty()
                .WithMessage("InvoiceAccountPassword is required")
                .MinimumLength(6)
                .WithMessage("InvoiceAccountPassword must be at least 6 characters")
                .MaximumLength(500)
                .WithMessage("InvoiceAccountPassword cannot exceed 500 characters");

            RuleFor(x => x.Request.EffectiveDate)
                .NotEmpty()
                .WithMessage("EffectiveDate is required");

            RuleFor(x => x.Request.ExpirationDate)
                .NotEmpty()
                .WithMessage("ExpirationDate is required")
                .GreaterThan(x => x.Request.EffectiveDate)
                .WithMessage("ExpirationDate must be after EffectiveDate");

            RuleFor(x => x.Request.MerchantBranchName)
                .NotEmpty()
                .WithMessage("MerchantBranchName is required")
                .MaximumLength(200)
                .WithMessage("MerchantBranchName cannot exceed 200 characters");

            RuleFor(x => x.Request.InvoiceAccountProvider)
                .Equal("MBF")
                .WithMessage("InvoiceAccountProvider must be 'MBF'");
        });
    }
}

/// <summary>
/// Handler for CreateMerchantBranchInvoiceAccountCommand
/// </summary>
public class CreateMerchantBranchInvoiceAccountHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    ILogger<CreateMerchantBranchInvoiceAccountHandler> logger)
    : IRequestHandler<CreateMerchantBranchInvoiceAccountCommand, Response<Guid>>
{
    public async Task<Response<Guid>> Handle(
        CreateMerchantBranchInvoiceAccountCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Creating new merchant branch invoice account for TaxNumber: {TaxNumber}",
                request.Request.TaxNumber);

            // Get current partner ID
            var currentPartnerId = currentUserService.GetCurrentPartnerId()
                ?? throw new UnauthorizedAccessException("User is not authenticated or partner ID not found");
            // if (currentPartnerId == null)
            // {
            //     logger.LogWarning("No current partner ID found for creating merchant branch invoice account");
            //     return new Response<MerchantBranchInvoiceAccountResponseDto>
            //     {
            //         Code = ErrorCodes.UNAUTHORIZED_ERROR,
            //         Message = "User is not authenticated or partner ID not found"
            //     };
            // }

            // Check if account with same TaxNumber already exists for current partner
            var existingAccount = await dbContext.MerchantBranchInvoiceAccounts
                .FirstOrDefaultAsync(x => x.TaxNumber == request.Request.TaxNumber && x.PartnerId == currentPartnerId, cancellationToken);

            if (existingAccount != null)
            {
                logger.LogWarning("Merchant branch invoice account with TaxNumber {TaxNumber} already exists for partner {PartnerId}",
                    request.Request.TaxNumber, currentPartnerId);
                return new Response<Guid>
                {
                    Code = ErrorCodes.BAD_REQUEST_ERROR,
                    Message = "An account with this tax number already exists"
                };
            }

            // Create new entity
            var entity = new Core.Entities.MerchantBranchInvoiceAccount
            {
                Id = Guid.NewGuid(),
                TaxNumber = request.Request.TaxNumber,
                InvoiceAccountUserName = request.Request.InvoiceAccountUserName,
                InvoiceAccountPassword = request.Request.InvoiceAccountPassword,
                InvoiceAccountProvider = "MBF",
                EffectiveDate = request.Request.EffectiveDate,
                ExpirationDate = request.Request.ExpirationDate,
                MerchantBranchId = Guid.NewGuid(),
                MerchantBranchName = request.Request.MerchantBranchName,
                PartnerId = currentPartnerId,
                IsActive = request.Request.IsActive,
                CreatedAt = DateTime.UtcNow
            };

            // Save to database
            await dbContext.MerchantBranchInvoiceAccounts.AddAsync(entity, cancellationToken);
            await dbContext.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Successfully created merchant branch invoice account with Id: {Id} for partner {PartnerId}",
                entity.Id, currentPartnerId);

            return new Response<Guid>(entity.Id);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error creating merchant branch invoice account for TaxNumber: {TaxNumber}",
                request.Request.TaxNumber);
            var errorResponse = new Response<Guid>
            {
                Code = ErrorCodes.INTERNAL_SERVER_ERROR,
                Message = "An error occurred while creating the merchant branch invoice account"
            };
            return errorResponse;
        }
    }
}
