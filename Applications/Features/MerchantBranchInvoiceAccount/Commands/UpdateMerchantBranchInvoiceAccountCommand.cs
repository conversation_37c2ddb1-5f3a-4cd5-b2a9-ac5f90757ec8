using Applications.DTOs.MerchantBranchInvoiceAccount;
using Applications.Interfaces;
using Applications.Interfaces.Services;
using FluentValidation;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Shared.Constants;
using BuildingBlocks.Abstractions;

namespace Applications.Features.MerchantBranchInvoiceAccount.Commands;

/// <summary>
/// Command to update an existing merchant branch invoice account
/// </summary>
public record UpdateMerchantBranchInvoiceAccountCommand(
    Guid Id,
    MerchantBranchInvoiceAccountDto Request) : IRequest<Response<MerchantBranchInvoiceAccountResponseDto>>;

/// <summary>
/// Validator for UpdateMerchantBranchInvoiceAccountCommand
/// </summary>
public class UpdateMerchantBranchInvoiceAccountCommandValidator : AbstractValidator<UpdateMerchantBranchInvoiceAccountCommand>
{
    public UpdateMerchantBranchInvoiceAccountCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("Id is required");

        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request cannot be null");

        When(x => x.Request != null, () =>
        {
            RuleFor(x => x.Request.TaxNumber)
                .NotEmpty()
                .WithMessage("TaxNumber is required")
                .MaximumLength(50)
                .WithMessage("TaxNumber cannot exceed 50 characters");

            RuleFor(x => x.Request.InvoiceAccountUserName)
                .NotEmpty()
                .WithMessage("InvoiceAccountUserName is required")
                .MaximumLength(100)
                .WithMessage("InvoiceAccountUserName cannot exceed 100 characters");

            RuleFor(x => x.Request.InvoiceAccountPassword)
                .NotEmpty()
                .WithMessage("InvoiceAccountPassword is required")
                .MinimumLength(6)
                .WithMessage("InvoiceAccountPassword must be at least 6 characters")
                .MaximumLength(500)
                .WithMessage("InvoiceAccountPassword cannot exceed 500 characters");

            RuleFor(x => x.Request.EffectiveDate)
                .NotEmpty()
                .WithMessage("EffectiveDate is required");

            RuleFor(x => x.Request.ExpirationDate)
                .NotEmpty()
                .WithMessage("ExpirationDate is required")
                .GreaterThan(x => x.Request.EffectiveDate)
                .WithMessage("ExpirationDate must be after EffectiveDate");

            RuleFor(x => x.Request.MerchantBranchName)
                .NotEmpty()
                .WithMessage("MerchantBranchName is required")
                .MaximumLength(200)
                .WithMessage("MerchantBranchName cannot exceed 200 characters");

            RuleFor(x => x.Request.InvoiceAccountProvider)
                .Equal("MBF")
                .WithMessage("InvoiceAccountProvider must be 'MBF'");
        });
    }
}

/// <summary>
/// Handler for UpdateMerchantBranchInvoiceAccountCommand
/// </summary>
public class UpdateMerchantBranchInvoiceAccountHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,
    ILogger<UpdateMerchantBranchInvoiceAccountHandler> logger)
    : IRequestHandler<UpdateMerchantBranchInvoiceAccountCommand, Response<MerchantBranchInvoiceAccountResponseDto>>
{
    public async Task<Response<MerchantBranchInvoiceAccountResponseDto>> Handle(
        UpdateMerchantBranchInvoiceAccountCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Updating merchant branch invoice account with Id: {Id}", request.Id);

            // Get current partner ID
            var currentPartnerId = currentUserService.GetCurrentPartnerId();
            if (currentPartnerId == null)
            {
                logger.LogWarning("No current partner ID found for updating merchant branch invoice account");
                return new Response<MerchantBranchInvoiceAccountResponseDto>
                {
                    Code = ErrorCodes.UNAUTHORIZED_ERROR,
                    Message = "User is not authenticated or partner ID not found"
                };
            }

            // Find existing entity and ensure it belongs to current partner
            var existingEntity = await dbContext.MerchantBranchInvoiceAccounts
                .FirstOrDefaultAsync(x => x.Id == request.Id && x.PartnerId == currentPartnerId.Value, cancellationToken);

            if (existingEntity == null)
            {
                logger.LogWarning("Merchant branch invoice account with Id {Id} not found for partner {PartnerId}", 
                    request.Id, currentPartnerId);
                return new Response<MerchantBranchInvoiceAccountResponseDto>
                {
                    Code = ErrorCodes.NOT_FOUND_DATA,
                    Message = "Merchant branch invoice account not found"
                };
            }

            // Check if TaxNumber is being changed and if it conflicts with another account
            if (existingEntity.TaxNumber != request.Request.TaxNumber)
            {
                var duplicateAccount = await dbContext.MerchantBranchInvoiceAccounts
                    .FirstOrDefaultAsync(x => x.TaxNumber == request.Request.TaxNumber && 
                                            x.PartnerId == currentPartnerId.Value && 
                                            x.Id != request.Id, cancellationToken);

                if (duplicateAccount != null)
                {
                    logger.LogWarning("Another merchant branch invoice account with TaxNumber {TaxNumber} already exists for partner {PartnerId}",
                        request.Request.TaxNumber, currentPartnerId);
                    return new Response<MerchantBranchInvoiceAccountResponseDto>
                    {
                        Code = ErrorCodes.BAD_REQUEST_ERROR,
                        Message = "An account with this tax number already exists"
                    };
                }
            }

            // Update entity properties
            existingEntity.TaxNumber = request.Request.TaxNumber;
            existingEntity.InvoiceAccountUserName = request.Request.InvoiceAccountUserName;
            existingEntity.InvoiceAccountPassword = request.Request.InvoiceAccountPassword;
            existingEntity.InvoiceAccountProvider = request.Request.InvoiceAccountProvider;
            existingEntity.EffectiveDate = request.Request.EffectiveDate;
            existingEntity.ExpirationDate = request.Request.ExpirationDate;
            existingEntity.MerchantBranchName = request.Request.MerchantBranchName;
            existingEntity.IsActive = request.Request.IsActive;
            existingEntity.UpdatedAt = DateTime.UtcNow;
            // Note: UpdatedBy should be set by audit interceptor if available

            // Save changes
            await dbContext.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Successfully updated merchant branch invoice account with Id: {Id} for partner {PartnerId}", 
                request.Id, currentPartnerId);

            // Map to response DTO using Mapster
            var response = existingEntity.Adapt<MerchantBranchInvoiceAccountResponseDto>();

            return new Response<MerchantBranchInvoiceAccountResponseDto>(
                response,
                "Merchant branch invoice account updated successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating merchant branch invoice account with Id: {Id}", request.Id);
            return new Response<MerchantBranchInvoiceAccountResponseDto>
            {
                Code = ErrorCodes.INTERNAL_SERVER_ERROR,
                Message = "An error occurred while updating the merchant branch invoice account"
            };
        }
    }
}
