﻿using BuildingBlocks.Abstractions;
using Core.Enumerables;
using MediatR;

namespace Applications.Features.ClientCredentials.Commands
{
    public class CreateClientCredentialCommand : IRequest<Response<Guid>>
    {
        public string ClientId { get; set; } = null!;
        public string ClientSecret { get; set; } = null!;
        public string Description { get; set; } = null!;
        public string Role { get; set; } = RoleEnum.Unknown;
    }
}
