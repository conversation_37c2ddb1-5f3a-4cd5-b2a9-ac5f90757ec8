﻿using Applications.Features.ClientCredentials.Commands;
using Applications.Interfaces.Repositories;
using BuildingBlocks.Abstractions;
using MediatR;
using System.Diagnostics;
using System.Security.Cryptography;

namespace Applications.Features.ClientCredentials.Handlers
{
    public class ResetClientSecretHandler : IRequestHandler<ResetClientSecretCommand, Response<string>>
    {
        private readonly IClientCredentialRepository _repository;

        public ResetClientSecretHandler(IClientCredentialRepository repository)
        {
            _repository = repository;
        }

        public async Task<Response<string>> Handle(ResetClientSecretCommand request, CancellationToken cancellationToken)
        {
            var traceId = Activity.Current?.Id ?? Guid.NewGuid().ToString();

            var entity = await _repository.GetByIdAsync(request.Id, cancellationToken);
            if (entity == null)
            {
                var errorResponse = new Response<string>();
                errorResponse.Code = "404";
                errorResponse.Message = "Client credential not found";
                errorResponse.TraceId = traceId;
                return errorResponse;
            }

            try
            {
                var newSecret = Convert.ToBase64String(RandomNumberGenerator.GetBytes(32));
                entity.ClientSecretHash = newSecret;
                _repository.Update(entity);
                await _repository.SaveChangesAsync(cancellationToken);

                return new Response<string>(newSecret, "Secret reset successfully");
            }
            catch (Exception ex)
            {
                var errorResponse = new Response<string>();
                errorResponse.Code = "500";
                errorResponse.Message = $"Failed to reset secret: {ex.Message}";
                errorResponse.TraceId = traceId;
                return errorResponse;
            }
        }
    }

}
