﻿using Applications.DTOs;
using Applications.Features.ClientCredentials.Queries;
using Applications.Interfaces.Repositories;
using AutoMapper;
using BuildingBlocks.Abstractions;
using MediatR;
using System.Diagnostics;

namespace Applications.Features.ClientCredentials.Handlers
{
    public class GetClientCredentialByIdHandler : IRequestHandler<GetClientCredentialByIdQuery, Response<ClientCredentialDto>>
    {
        private readonly IClientCredentialRepository _repository;
        private readonly IMapper _mapper;

        public GetClientCredentialByIdHandler(IClientCredentialRepository repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }

        public async Task<Response<ClientCredentialDto>> Handle(GetClientCredentialByIdQuery request, CancellationToken cancellationToken)
        {
            var traceId = Activity.Current?.Id ?? Guid.NewGuid().ToString();

            var entity = await _repository.GetByIdAsync(request.Id, cancellationToken);
            if (entity == null)
            {
                var errorResponse = new Response<ClientCredentialDto>();
                errorResponse.Code = "404";
                errorResponse.Message = "Client credential not found";
                errorResponse.TraceId = traceId;
                return errorResponse;
            }

            var dto = _mapper.Map<ClientCredentialDto>(entity);
            return new Response<ClientCredentialDto>(dto, "Retrieved successfully");
        }
    }

}
