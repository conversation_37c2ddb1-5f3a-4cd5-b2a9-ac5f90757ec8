﻿using Applications.DTOs;
using Applications.Features.ClientCredentials.Queries;
using Applications.Interfaces.Repositories;
using AutoMapper;
using BuildingBlocks.Abstractions;
using MediatR;

namespace Applications.Features.ClientCredentials.Handlers
{
    public class GetClientCredentialListHandler : IRequestHandler<GetClientCredentialListQuery, Response<List<ClientCredentialDto>>>
    {
        private readonly IClientCredentialRepository _repository;
        private readonly IMapper _mapper;

        public GetClientCredentialListHandler(IClientCredentialRepository repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }

        public async Task<Response<List<ClientCredentialDto>>> Handle(GetClientCredentialListQuery request, CancellationToken cancellationToken)
        {
            var data = await _repository.GetAllAsync();
            var list = _mapper.Map<List<ClientCredentialDto>>(data);
            return new Response<List<ClientCredentialDto>>(list);
        }
    }
}
