﻿using Applications.Features.ClientCredentials.Commands;
using Applications.Interfaces.Repositories;
using AutoMapper;
using BuildingBlocks.Abstractions;
using Core.Entities;
using MediatR;

namespace Applications.Features.ClientCredentials.Handlers
{
    public class CreateClientCredentialHandler : IRequestHandler<CreateClientCredentialCommand, Response<Guid>>
    {
        private readonly IClientCredentialRepository _repository;
        private readonly IMapper _mapper;

        public CreateClientCredentialHandler(IClientCredentialRepository repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }

        public async Task<Response<Guid>> Handle(CreateClientCredentialCommand request, CancellationToken cancellationToken)
        {
            // Ki<PERSON>m tra ClientId trùng
            var existing = await _repository.GetByClientIdAsync(request.ClientId);
            if (existing != null)
            {
                var errorResponse = new Response<Guid>();
                errorResponse.Code = "400";
                errorResponse.Message = "ClientId already exists";
                return errorResponse;
            }

            // Map sang entity và mã hoá mật khẩu
            var entity = _mapper.Map<ClientCredential>(request);
            entity.ClientSecretHash = BCrypt.Net.BCrypt.HashPassword(request.ClientSecret);

            await _repository.AddAsync(entity);
            await _repository.SaveChangesAsync();

            return new Response<Guid>(entity.Id, "Created successfully");
        }
    }
}
