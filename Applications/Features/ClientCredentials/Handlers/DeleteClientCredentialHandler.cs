﻿using Applications.Features.ClientCredentials.Commands;
using Applications.Interfaces.Repositories;
using BuildingBlocks.Abstractions;
using MediatR;
using System.Diagnostics;

namespace Applications.Features.ClientCredentials.Handlers
{
    public class DeleteClientCredentialHandler : IRequestHandler<DeleteClientCredentialCommand, Response<bool>>
    {
        private readonly IClientCredentialRepository _repository;

        public DeleteClientCredentialHandler(IClientCredentialRepository repository)
        {
            _repository = repository;
        }

        public async Task<Response<bool>> <PERSON>le(DeleteClientCredentialCommand request, CancellationToken cancellationToken)
        {
            var traceId = Activity.Current?.Id ?? Guid.NewGuid().ToString();

            var entity = await _repository.GetByIdAsync(request.Id, cancellationToken);
            if (entity == null)
            {
                var errorResponse = new Response<bool>();
                errorResponse.Code = "404";
                errorResponse.Message = "Client credential not found";
                errorResponse.TraceId = traceId;
                return errorResponse;
            }

            try
            {
                _repository.Delete(entity);
                await _repository.SaveChangesAsync(cancellationToken);

                return new Response<bool>(true, "Deleted successfully");
            }
            catch (Exception ex)
            {
                var errorResponse = new Response<bool>();
                errorResponse.Code = "500";
                errorResponse.Message = $"An error occurred while deleting: {ex.Message}";
                errorResponse.TraceId = traceId;
                return errorResponse;
            }
        }
    }

}
