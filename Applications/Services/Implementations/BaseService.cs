﻿using Applications.Interfaces.Repositories;
using Applications.Interfaces.Services;
using AutoMapper;
using BuildingBlocks.Abstractions;
using Core;
using System.Diagnostics;
using System.Linq.Expressions;

namespace Applications.Services.Implementations
{
    public class BaseService<TDto, TEntity> : IBaseService<TDto, TEntity>
     where TEntity : Audit
     where TDto : class
    {
        protected readonly IBaseRepository<TEntity> _repository;
        protected readonly IMapper _mapper;

        public BaseService(IBaseRepository<TEntity> repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }

        public virtual async Task<Response<TDto>> GetByIdAsync(Guid id)
        {
            var traceId = Activity.Current?.Id ?? Guid.NewGuid().ToString();

            var entity = await _repository.GetByIdAsync(id);
            if (entity == null)
            {
                var errorResponse = new Response<TDto>();
                errorResponse.Code = "404";
                errorResponse.Message = "Entity not found";
                errorResponse.TraceId = traceId;
                return errorResponse;
            }

            return new Response<TDto>(_mapper.Map<TDto>(entity), "Retrieved successfully");
        }

        public virtual async Task<Response<IEnumerable<TDto>>> GetAllAsync()
        {
            var traceId = Activity.Current?.Id ?? Guid.NewGuid().ToString();

            var entities = await _repository.GetAllAsync();
            return new Response<IEnumerable<TDto>>(_mapper.Map<IEnumerable<TDto>>(entities), "List retrieved");
        }

        public virtual async Task<Response<TDto>> CreateAsync(TDto dto)
        {
            var traceId = Activity.Current?.Id ?? Guid.NewGuid().ToString();

            if (dto == null)
            {
                var errorResponse = new Response<TDto>();
                errorResponse.Code = "400";
                errorResponse.Message = "Input is null";
                errorResponse.TraceId = traceId;
                return errorResponse;
            }

            try
            {
                var entity = _mapper.Map<TEntity>(dto);
                await _repository.AddAsync(entity);
                await _repository.SaveChangesAsync();

                var createdDto = _mapper.Map<TDto>(entity);
                return new Response<TDto>(createdDto, "Created successfully");
            }
            catch (Exception ex)
            {
                var errorResponse = new Response<TDto>();
                errorResponse.Code = "500";
                errorResponse.Message = $"An error occurred: {ex.Message}";
                errorResponse.TraceId = traceId;
                return errorResponse;
            }
        }

        public virtual async Task<Response<TDto>> UpdateAsync(Guid id, TDto dto)
        {
            var traceId = Activity.Current?.Id ?? Guid.NewGuid().ToString();

            if (dto == null)
            {
                var errorResponse = new Response<TDto>();
                errorResponse.Code = "400";
                errorResponse.Message = "Input is null";
                errorResponse.TraceId = traceId;
                return errorResponse;
            }

            var entity = await _repository.GetByIdAsync(id);
            if (entity == null)
            {
                var errorResponse = new Response<TDto>();
                errorResponse.Code = "404";
                errorResponse.Message = "Entity not found";
                errorResponse.TraceId = traceId;
                return errorResponse;
            }

            try
            {
                _mapper.Map(dto, entity);
                _repository.Update(entity);
                await _repository.SaveChangesAsync();

                return new Response<TDto>(_mapper.Map<TDto>(entity), "Updated successfully");
            }
            catch (Exception ex)
            {
                var errorResponse = new Response<TDto>();
                errorResponse.Code = "500";
                errorResponse.Message = $"An error occurred: {ex.Message}";
                errorResponse.TraceId = traceId;
                return errorResponse;
            }
        }

        public virtual async Task<Response<bool>> DeleteAsync(Guid id)
        {
            var traceId = Activity.Current?.Id ?? Guid.NewGuid().ToString();

            var entity = await _repository.GetByIdAsync(id);
            if (entity == null)
            {
                var errorResponse = new Response<bool>();
                errorResponse.Code = "404";
                errorResponse.Message = "Entity not found";
                errorResponse.TraceId = traceId;
                return errorResponse;
            }

            try
            {
                _repository.Delete(entity);
                await _repository.SaveChangesAsync();

                return new Response<bool>(true, "Deleted successfully");
            }
            catch (Exception ex)
            {
                var errorResponse = new Response<bool>();
                errorResponse.Code = "500";
                errorResponse.Message = $"An error occurred: {ex.Message}";
                errorResponse.TraceId = traceId;
                return errorResponse;
            }
        }

        public virtual async Task<Response<IEnumerable<TDto>>> FindAsync(Expression<Func<TEntity, bool>> predicate)
        {
            var traceId = Activity.Current?.Id ?? Guid.NewGuid().ToString();

            var results = await _repository.FindAsync(predicate);
            return new Response<IEnumerable<TDto>>(_mapper.Map<IEnumerable<TDto>>(results), "Filtered list");
        }

        public virtual async Task<bool> ExistsAsync(Guid id)
        {
            return await _repository.AnyAsync(x => x.Id == id);
        }
    }

}
