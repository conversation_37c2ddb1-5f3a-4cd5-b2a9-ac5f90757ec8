﻿using BuildingBlocks.Abstractions;
using Core;
using System.Linq.Expressions;

namespace Applications.Interfaces.Services
{
    public interface IBaseService<TDto, TEntity>
        where TEntity : Audit
        where TDto : class
    {
        Task<Response<TDto>> GetByIdAsync(Guid id);
        Task<Response<IEnumerable<TDto>>> GetAllAsync();
        Task<Response<TDto>> CreateAsync(TDto dto);
        Task<Response<TDto>> UpdateAsync(Guid id, TDto dto);
        Task<Response<bool>> DeleteAsync(Guid id);
        Task<Response<IEnumerable<TDto>>> FindAsync(Expression<Func<TEntity, bool>> predicate);
        Task<bool> ExistsAsync(Guid id);
    }
}
