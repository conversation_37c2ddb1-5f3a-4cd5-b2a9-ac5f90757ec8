namespace Applications.DTOs.MerchantInvoiceOrder;

/// <summary>
/// DTO for MerchantInvoiceOrder requests (Create/Update)
/// </summary>
public class MerchantInvoiceOrderDto
{
    /// <summary>
    /// Foreign key to MerchantBranchInvoiceAccount (merchant_branch_id)
    /// References the merchant branch that this invoice order belongs to
    /// </summary>
    public Guid? MerchantBranchId { get; set; }

    /// <summary>
    /// Total number of invoices allocated in this order
    /// </summary>
    public int TotalInvoiceQuantity { get; set; }

    /// <summary>
    /// Start date of the validity period for this invoice order
    /// </summary>
    public DateTime EffectiveDateFrom { get; set; }

    /// <summary>
    /// End date of the validity period for this invoice order
    /// </summary>
    public DateTime EffectiveDateTo { get; set; }

    /// <summary>
    /// Optional description or notes about this invoice order
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Whether this invoice order is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Order reference number or identifier from external system
    /// </summary>
    public string? OrderReference { get; set; }
}

public class MerchantInvoiceOrderResponseDto : MerchantInvoiceOrderDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public Guid? UpdatedBy { get; set; }
}