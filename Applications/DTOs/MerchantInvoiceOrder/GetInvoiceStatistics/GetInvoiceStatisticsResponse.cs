namespace Applications.DTOs.MerchantInvoiceOrder.GetInvoiceStatistics;

/// <summary>
/// Response chứa thống kê số lượng hóa đơn của MerchantBranch
/// </summary>
public class GetInvoiceStatisticsResponse
{
    /// <summary>
    /// ID của MerchantBranch
    /// </summary>
    public Guid MerchantBranchId { get; set; }

    /// <summary>
    /// Tổng số lượng hóa đơn đã mua
    /// </summary>
    public int TotalPurchasedInvoices { get; set; }

    /// <summary>
    /// Số lượng hóa đơn đã sử dụng
    /// </summary>
    public int UsedInvoices { get; set; }

    /// <summary>
    /// Số lượng hóa đơn có sẵn (có thể sử dụng)
    /// </summary>
    public int AvailableInvoices { get; set; }

    /// <summary>
    /// Message thông báo (nếu có)
    /// </summary>
    public string? Message { get; set; }
}
