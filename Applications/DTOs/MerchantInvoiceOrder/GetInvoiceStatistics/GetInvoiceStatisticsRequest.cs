using System.ComponentModel.DataAnnotations;

namespace Applications.DTOs.MerchantInvoiceOrder.GetInvoiceStatistics;

/// <summary>
/// Request để lấy thống kê số lượng hóa đơn đã sử dụng và có sẵn của MerchantBranch
/// </summary>
public class GetInvoiceStatisticsRequest
{
    /// <summary>
    /// ID của MerchantBranch cần lấy thống kê
    /// </summary>
    [Required(ErrorMessage = "MerchantBranchId is required")]
    public Guid MerchantBranchId { get; set; }
}
