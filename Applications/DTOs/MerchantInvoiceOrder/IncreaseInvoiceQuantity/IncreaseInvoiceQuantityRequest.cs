using System.ComponentModel.DataAnnotations;

namespace Applications.DTOs.MerchantInvoiceOrder.IncreaseInvoiceQuantity;

/// <summary>
/// Request để tăng số lượng hóa đơn khả dụng cho một chi nh<PERSON>h merchant
/// </summary>
public class IncreaseInvoiceQuantityRequest
{
    /// <summary>
    /// ID của MerchantBranch (bắt buộc phải tồn tại trong MerchantBranchInvoiceAccount)
    /// </summary>
    [Required(ErrorMessage = "MerchantBranchId is required")]
    public Guid MerchantBranchId { get; set; }

    /// <summary>
    /// Số lượng hóa đơn cần thêm vào
    /// </summary>
    [Required(ErrorMessage = "Quantity is required")]
    [Range(1, int.MaxValue, ErrorMessage = "Quantity must be greater than 0")]
    public int Quantity { get; set; }

    /// <summary>
    /// <PERSON><PERSON> tả lý do tăng số lượng hóa đơn (optional)
    /// </summary>
    [MaxLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
    public string? Description { get; set; }
}
