namespace Applications.DTOs.MerchantInvoiceOrder.IncreaseInvoiceQuantity;

/// <summary>
/// Response sau khi tăng số lượng hóa đơn thành công
/// </summary>
public class IncreaseInvoiceQuantityResponse
{
    /// <summary>
    /// ID của MerchantBranch
    /// </summary>
    public Guid MerchantBranchId { get; set; }

    /// <summary>
    /// Số lượng hóa đơn đã được thêm vào
    /// </summary>
    public int QuantityAdded { get; set; }

    /// <summary>
    /// Tổng số lượng hóa đơn còn lại sau khi tăng
    /// </summary>
    public int NewRemainingQuantity { get; set; }

    /// <summary>
    /// Tổng số lượng hóa đơn trong order hiện tại
    /// </summary>
    public int NewTotalQuantity { get; set; }

    /// <summary>
    /// ID của MerchantInvoiceOrder đã được cập nhật
    /// </summary>
    public Guid UpdatedOrderId { get; set; }

    /// <summary>
    /// Thời gian cập nhật
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// Mô tả lý do tăng số lượng
    /// </summary>
    public string? Description { get; set; }
}
