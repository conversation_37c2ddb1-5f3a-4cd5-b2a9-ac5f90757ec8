namespace Applications.DTOs.MerchantInvoiceOrder.GetMerchantInvoiceOrderDetail;

/// <summary>
/// Response chứa chi tiết MerchantInvoiceOrder
/// </summary>
public class GetMerchantInvoiceOrderDetailResponse
{
    /// <summary>
    /// ID của MerchantInvoiceOrder
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// ID của MerchantBranch
    /// </summary>
    public Guid MerchantBranchId { get; set; }

    /// <summary>
    /// Tổng số lượng hóa đơn trong order
    /// </summary>
    public int TotalInvoiceQuantity { get; set; }

    /// <summary>
    /// Số lượng hóa đơn còn lại
    /// </summary>
    public int RemainingInvoiceQuantity { get; set; }

    /// <summary>
    /// Ngày bắt đầu hiệu lực
    /// </summary>
    public DateTime EffectiveDateFrom { get; set; }

    /// <summary>
    /// Ngày kết thúc hiệu lực
    /// </summary>
    public DateTime EffectiveDateTo { get; set; }

    /// <summary>
    /// Mô tả
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Trạng thái active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Mã tham chiếu order
    /// </summary>
    public string? OrderReference { get; set; }

    /// <summary>
    /// Ngày tạo
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Ngày cập nhật
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// Thông tin chi tiết MerchantBranch
    /// </summary>
    public MerchantBranchDetail? MerchantBranch { get; set; }

    /// <summary>
    /// Số lượng hóa đơn đã sử dụng
    /// </summary>
    public int UsedInvoiceCount { get; set; }

    /// <summary>
    /// Trạng thái hết hạn
    /// </summary>
    public bool IsExpired { get; set; }
}

/// <summary>
/// Chi tiết thông tin MerchantBranch
/// </summary>
public class MerchantBranchDetail
{
    public Guid Id { get; set; }
    public string? BranchName { get; set; }
    public string? BranchCode { get; set; }
    // public string? Address { get; set; }
    // public string? ContactEmail { get; set; }
    // public string? ContactPhone { get; set; }
}
