namespace Applications.DTOs.MerchantInvoiceOrder.CreateMerchantInvoiceOrder;

/// <summary>
/// Response sau khi tạo MerchantInvoiceOrder thành công
/// </summary>
public class CreateMerchantInvoiceOrderResponse
{
    /// <summary>
    /// ID của MerchantInvoiceOrder vừa tạo
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// ID của MerchantBranch
    /// </summary>
    public Guid MerchantBranchId { get; set; }

    /// <summary>
    /// Tổng số lượng hóa đơn trong order
    /// </summary>
    public int TotalInvoiceQuantity { get; set; }

    /// <summary>
    /// Số lượng hóa đơn còn lại (ban đầu = TotalInvoiceQuantity)
    /// </summary>
    public int RemainingInvoiceQuantity { get; set; }

    /// <summary>
    /// Ngày bắt đầu hiệu lực
    /// </summary>
    public DateTime EffectiveDateFrom { get; set; }

    /// <summary>
    /// Ngày kết thúc hiệu lực
    /// </summary>
    public DateTime EffectiveDateTo { get; set; }

    /// <summary>
    /// Mô tả
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Mã tham chiếu order
    /// </summary>
    public string? OrderReference { get; set; }

    /// <summary>
    /// Ngày tạo
    /// </summary>
    public DateTime CreatedAt { get; set; }
}
