using System.ComponentModel.DataAnnotations;

namespace Applications.DTOs.MerchantInvoiceOrder.CreateMerchantInvoiceOrder;

/// <summary>
/// Request để tạo MerchantInvoiceOrder mới
/// </summary>
public class CreateMerchantInvoiceOrderRequest
{
    /// <summary>
    /// ID của MerchantBranch (bắt buộc phải tồn tại trong MerchantBranchInvoiceAccount)
    /// </summary>
    [Required(ErrorMessage = "MerchantBranchId is required")]
    public Guid MerchantBranchId { get; set; }

    /// <summary>
    /// Tổng số lượng hóa đơn trong order
    /// </summary>
    [Required(ErrorMessage = "TotalInvoiceQuantity is required")]
    [Range(1, int.MaxValue, ErrorMessage = "TotalInvoiceQuantity must be greater than 0")]
    public int TotalInvoiceQuantity { get; set; }

    /// <summary>
    /// Ng<PERSON>y bắt đầu hiệu lực
    /// </summary>
    [Required(ErrorMessage = "EffectiveDateFrom is required")]
    public DateTime EffectiveDateFrom { get; set; }

    /// <summary>
    /// Ngày kết thúc hiệu lực
    /// </summary>
    [Required(ErrorMessage = "EffectiveDateTo is required")]
    public DateTime EffectiveDateTo { get; set; }

    /// <summary>
    /// Mô tả (optional)
    /// </summary>
    [MaxLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
    public string? Description { get; set; }

    /// <summary>
    /// Mã tham chiếu order (optional)
    /// </summary>
    [MaxLength(100, ErrorMessage = "OrderReference cannot exceed 100 characters")]
    public string? OrderReference { get; set; }
}
