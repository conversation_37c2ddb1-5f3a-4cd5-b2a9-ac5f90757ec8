using Core.Enumerables;

namespace Applications.DTOs.MerchantInvoiceOrder.GetMerchantInvoiceOrders;

/// <summary>
/// DTO cho MerchantInvoiceOrder
/// </summary>
public class MerchantInvoiceOrderDto
{
    /// <summary>
    /// ID của MerchantInvoiceOrder
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// ID của MerchantBranch
    /// </summary>
    public Guid MerchantBranchId { get; set; }

    /// <summary>
    /// Tổng số lượng hóa đơn trong order
    /// </summary>
    public int TotalInvoiceQuantity { get; set; }

    /// <summary>
    /// Số lượng hóa đơn còn lại
    /// </summary>
    public int RemainingInvoiceQuantity { get; set; }

    /// <summary>
    /// Ngày bắt đầu hiệu lực
    /// </summary>
    public DateTime EffectiveDateFrom { get; set; }

    /// <summary>
    /// <PERSON><PERSON>y kết thúc hiệu lực
    /// </summary>
    public DateTime EffectiveDateTo { get; set; }

    /// <summary>
    /// Mô tả
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Trạng thái active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Mã tham chiếu order
    /// </summary>
    public string? OrderReference { get; set; }

    /// <summary>
    /// Trạng thái của merchant invoice order
    /// </summary>
    public MerchantInvoiceOrderStatus? Status { get; set; }

    /// <summary>
    /// Ngày tạo
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Ngày cập nhật
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// Thông tin MerchantBranch (nếu cần)
    /// </summary>
    public MerchantBranchSummary? MerchantBranch { get; set; }
}

/// <summary>
/// Summary thông tin MerchantBranch
/// </summary>
public class MerchantBranchSummary
{
    public Guid Id { get; set; }
    public string? BranchName { get; set; }
    public string? BranchCode { get; set; }
}
