using BuildingBlocks.Abstractions;
using System.ComponentModel.DataAnnotations;

namespace Applications.DTOs.MerchantInvoiceOrder.GetMerchantInvoiceOrders;

/// <summary>
/// Request để lấy danh sách MerchantInvoiceOrder với phân trang và filter
/// </summary>
public class GetMerchantInvoiceOrdersRequest
{
    /// <summary>
    /// Page index (1-based)
    /// </summary>
    public int PageIndex { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Filter theo MerchantBranchId (optional)
    /// </summary>
    public Guid? MerchantBranchId { get; set; }

    /// <summary>
    /// Filter theo trạng thái active (optional)
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Filter từ ngày hiệu lực (optional)
    /// </summary>
    public DateTime? FromEffectiveDate { get; set; }

    /// <summary>
    /// Filter đến ngày hiệu lự<PERSON> (optional)
    /// </summary>
    public DateTime? ToEffectiveDate { get; set; }

    /// <summary>
    /// Search term để tìm kiếm theo OrderReference hoặc Description (optional)
    /// </summary>
    public string? SearchTerm { get; set; }
}
