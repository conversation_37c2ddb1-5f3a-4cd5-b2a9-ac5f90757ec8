using System.ComponentModel.DataAnnotations;

namespace Applications.DTOs.MerchantInvoiceOrder.ConfirmMerchantInvoiceOrder;

/// <summary>
/// Request để xác thực MerchantInvoiceOrder (chuyển status từ PENDING sang COMPLETED)
/// </summary>
public class ConfirmMerchantInvoiceOrderRequest
{
    /// <summary>
    /// Ghi chú hoặc lý do xác thực (optional)
    /// </summary>
    [MaxLength(500, ErrorMessage = "Notes cannot exceed 500 characters")]
    public string? Notes { get; set; }
}
