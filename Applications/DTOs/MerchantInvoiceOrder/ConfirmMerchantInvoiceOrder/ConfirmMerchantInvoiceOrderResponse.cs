using Core.Enumerables;

namespace Applications.DTOs.MerchantInvoiceOrder.ConfirmMerchantInvoiceOrder;

/// <summary>
/// Response sau khi xác thực MerchantInvoiceOrder thành công
/// </summary>
public class ConfirmMerchantInvoiceOrderResponse
{
    /// <summary>
    /// ID của MerchantInvoiceOrder
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Status mới sau khi xác thực
    /// </summary>
    public MerchantInvoiceOrderStatus? Status { get; set; }

    /// <summary>
    /// Thời gian xác thực
    /// </summary>
    public DateTime ConfirmedAt { get; set; }

    /// <summary>
    /// Ghi chú xác thực
    /// </summary>
    public string? Notes { get; set; }
}
