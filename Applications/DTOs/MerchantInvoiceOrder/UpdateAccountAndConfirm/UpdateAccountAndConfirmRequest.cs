using System.ComponentModel.DataAnnotations;

namespace Applications.DTOs.MerchantInvoiceOrder.UpdateAccountAndConfirm;

/// <summary>
/// Request để cập nhật account và hoàn tất MerchantInvoiceOrder
/// </summary>
public class UpdateAccountAndConfirmRequest
{
    /// <summary>
    /// Tax number for the merchant branch
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string TaxNumber { get; set; } = null!;

    /// <summary>
    /// Username for the invoice account
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string InvoiceAccountUserName { get; set; } = null!;

    /// <summary>
    /// Password for the invoice account
    /// </summary>
    [Required]
    [MinLength(6)]
    [MaxLength(500)]
    public string InvoiceAccountPassword { get; set; } = null!;

    /// <summary>
    /// When this invoice account becomes effective
    /// </summary>
    [Required]
    public DateTime EffectiveDate { get; set; }

    /// <summary>
    /// When this invoice account expires
    /// </summary>
    [Required]
    public DateTime ExpirationDate { get; set; }

    /// <summary>
    /// Merchant branch name for display purposes
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string MerchantBranchName { get; set; } = null!;

    /// <summary>
    /// Whether this invoice account is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Ghi chú cho việc xác thực order (optional)
    /// </summary>
    [MaxLength(500)]
    public string? ConfirmNotes { get; set; }
}
