using Core.Enumerables;

namespace Applications.DTOs.MerchantInvoiceOrder.UpdateAccountAndConfirm;

/// <summary>
/// Response sau khi tạo account và confirm MerchantInvoiceOrder thành công
/// </summary>
public class UpdateAccountAndConfirmResponse
{
    /// <summary>
    /// ID của MerchantInvoiceOrder đã được confirm
    /// </summary>
    public Guid OrderId { get; set; }

    /// <summary>
    /// ID của MerchantBranchInvoiceAccount vừa tạo
    /// </summary>
    public Guid AccountId { get; set; }

    /// <summary>
    /// MerchantBranchId vừa được tạo và gán cho order
    /// </summary>
    public Guid MerchantBranchId { get; set; }

    /// <summary>
    /// Status mới của order sau khi confirm
    /// </summary>
    public MerchantInvoiceOrderStatus? Status { get; set; }

    /// <summary>
    /// Thời gian thực hiện
    /// </summary>
    public DateTime ProcessedAt { get; set; }

    /// <summary>
    /// Ghi chú confirm
    /// </summary>
    public string? ConfirmNotes { get; set; }

    /// <summary>
    /// Thông tin account vừa tạo
    /// </summary>
    public AccountInfo Account { get; set; } = null!;
}

/// <summary>
/// Thông tin account vừa tạo
/// </summary>
public class AccountInfo
{
    public string TaxNumber { get; set; } = null!;
    public string InvoiceAccountUserName { get; set; } = null!;
    public string MerchantBranchName { get; set; } = null!;
    public DateTime EffectiveDate { get; set; }
    public DateTime ExpirationDate { get; set; }
    public bool IsActive { get; set; }
}
