using Core.Enumerables;

namespace Applications.DTOs.MerchantInvoiceOrder.UpdateAccountAndConfirm;

/// <summary>
/// Response sau khi cập nhật account và hoàn tất MerchantInvoiceOrder thành công
/// </summary>
public class UpdateAccountAndConfirmResponse
{
    /// <summary>
    /// ID của MerchantInvoiceOrder đã được confirm
    /// </summary>
    public Guid OrderId { get; set; }

    /// <summary>
    /// Status mới của order sau khi confirm
    /// </summary>
    public MerchantInvoiceOrderStatus? OrderStatus { get; set; }

    /// <summary>
    /// ID của MerchantBranchInvoiceAccount đã được tạo/cập nhật
    /// </summary>
    public Guid AccountId { get; set; }

    /// <summary>
    /// MerchantBranchId của account đã được tạo/cập nhật
    /// </summary>
    public Guid MerchantBranchId { get; set; }

    /// <summary>
    /// Thời gian thực hiện
    /// </summary>
    public DateTime ProcessedAt { get; set; }

    /// <summary>
    /// Ghi chú xác thực
    /// </summary>
    public string? ConfirmNotes { get; set; }

    /// <summary>
    /// Thông tin account đã được tạo/cập nhật
    /// </summary>
    public AccountInfo Account { get; set; } = null!;
}

/// <summary>
/// Thông tin account trong response
/// </summary>
public class AccountInfo
{
    public string TaxNumber { get; set; } = null!;
    public string InvoiceAccountUserName { get; set; } = null!;
    public string MerchantBranchName { get; set; } = null!;
    public DateTime EffectiveDate { get; set; }
    public DateTime ExpirationDate { get; set; }
    public bool IsActive { get; set; }
}
