using System.ComponentModel.DataAnnotations;
using Core.Enumerables;

namespace Applications.DTOs.InvoiceInfo.GetInvoicesByMerchantBranch;

/// <summary>
/// Request để lấy danh sách Invoice theo MerchantBranchId với phân trang và filter
/// </summary>
public class GetInvoicesByMerchantBranchRequest
{
    /// <summary>
    /// Page index (1-based)
    /// </summary>
    public int PageIndex { get; set; } = 1;

    /// <summary>
    /// Page size
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// ID của MerchantBranch (bắt buộc)
    /// </summary>
    [Required(ErrorMessage = "MerchantBranchId is required")]
    public Guid MerchantBranchId { get; set; }

    /// <summary>
    /// Filter theo trạng thái CQT hóa đơn (optional)
    /// </summary>
    public CqtInvoiceStatus? CqtInvoiceStatus { get; set; }

    /// <summary>
    /// Filter theo trạng thái hóa đơn (optional)
    /// </summary>
    public InvoiceStatus? InvoiceStatus { get; set; }

    /// <summary>
    /// Filter từ ngày tạo (optional)
    /// </summary>
    public DateTime? FromDate { get; set; }

    /// <summary>
    /// Filter đến ngày tạo (optional)
    /// </summary>
    public DateTime? ToDate { get; set; }

    /// <summary>
    /// Search term để tìm kiếm theo InvoiceId hoặc CustomerName (optional)
    /// </summary>
    public string? SearchTerm { get; set; }
}
