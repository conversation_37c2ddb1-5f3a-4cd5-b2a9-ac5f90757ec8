using Core.Enumerables;

namespace Applications.DTOs.InvoiceInfo.GetInvoicesByMerchantBranch;

/// <summary>
/// DTO cho Invoice theo MerchantBranch
/// </summary>
public class InvoiceByMerchantBranchDto
{
    /// <summary>
    /// ID của InvoiceInfo
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Invoice ID
    /// </summary>
    public string InvoiceId { get; set; } = null!;

    /// <summary>
    /// Trạng thái CQT hóa đơn
    /// </summary>
    public CqtInvoiceStatus CqtInvoiceStatus { get; set; }

    /// <summary>
    /// Trạng thái hóa đơn
    /// </summary>
    public InvoiceStatus InvoiceStatus { get; set; }

    /// <summary>
    /// Số hóa đơn
    /// </summary>
    public string? InvoiceNumber { get; set; }

    /// <summary>
    /// Ký hiệu hóa đơn
    /// </summary>
    public string? InvoiceSeries { get; set; }

    /// <summary>
    /// Tổng tiền
    /// </summary>
    public decimal? TotalAmount { get; set; }

    /// <summary>
    /// Tiền thuế
    /// </summary>
    public decimal? TaxAmount { get; set; }

    /// <summary>
    /// Tên khách hàng
    /// </summary>
    public string? CustomerName { get; set; }

    /// <summary>
    /// Mã số thuế khách hàng
    /// </summary>
    public string? CustomerTaxCode { get; set; }

    /// <summary>
    /// Thông báo lỗi (nếu có)
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Ghi chú
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Ngày tạo
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Ngày cập nhật
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// ID của MerchantInvoiceOrder
    /// </summary>
    public Guid MerchantInvoiceOrderId { get; set; }

    /// <summary>
    /// Thông tin MerchantInvoiceOrder (nếu cần)
    /// </summary>
    public MerchantInvoiceOrderSummary? MerchantInvoiceOrder { get; set; }
}

/// <summary>
/// Summary thông tin MerchantInvoiceOrder
/// </summary>
public class MerchantInvoiceOrderSummary
{
    public Guid Id { get; set; }
    public string? OrderReference { get; set; }
    public string? Description { get; set; }
}
