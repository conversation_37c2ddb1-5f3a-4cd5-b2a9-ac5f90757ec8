using System.ComponentModel.DataAnnotations;
using Core.Enumerables;

namespace Applications.DTOs.InvoiceInfo.UpdateInvoiceStatus;

/// <summary>
/// Request để cập nhật trạng thái CQT và trạng thái hóa đơn
/// </summary>
public class UpdateInvoiceStatusRequest
{
    /// <summary>
    /// Trạng thái CQT hóa đơn mới (optional)
    /// </summary>
    public CqtInvoiceStatus? CqtInvoiceStatus { get; set; }

    /// <summary>
    /// Trạng thái hóa đơn mới (optional)
    /// </summary>
    public InvoiceStatus? InvoiceStatus { get; set; }

    /// <summary>
    /// Thông báo lỗi (optional)
    /// </summary>
    [MaxLength(1000, ErrorMessage = "ErrorMessage cannot exceed 1000 characters")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// <PERSON><PERSON> chú (optional)
    /// </summary>
    [MaxLength(1000, ErrorMessage = "Notes cannot exceed 1000 characters")]
    public string? Notes { get; set; }
}
