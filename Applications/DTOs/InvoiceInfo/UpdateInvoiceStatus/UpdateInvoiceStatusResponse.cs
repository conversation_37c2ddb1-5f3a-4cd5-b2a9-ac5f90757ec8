using Core.Enumerables;

namespace Applications.DTOs.InvoiceInfo.UpdateInvoiceStatus;

/// <summary>
/// Response sau khi cập nhật trạng thái hóa đơn thành công
/// </summary>
public class UpdateInvoiceStatusResponse
{
    /// <summary>
    /// ID của InvoiceInfo vừa cập nhật
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Invoice ID
    /// </summary>
    public string InvoiceId { get; set; } = null!;

    /// <summary>
    /// Trạng thái CQT hóa đơn sau khi cập nhật
    /// </summary>
    public CqtInvoiceStatus CqtInvoiceStatus { get; set; }

    /// <summary>
    /// Trạng thái hóa đơn sau khi cập nhật
    /// </summary>
    public InvoiceStatus InvoiceStatus { get; set; }

    /// <summary>
    /// Thông báo lỗi (nếu có)
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Ghi chú
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Ngày cập nhật
    /// </summary>
    public DateTime UpdatedAt { get; set; }
}
