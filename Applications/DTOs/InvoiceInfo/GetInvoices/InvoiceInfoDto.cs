using Core.Enumerables;

namespace Applications.DTOs.InvoiceInfo.GetInvoices;

/// <summary>
/// DTO for invoice information in paginated list
/// </summary>
public class InvoiceInfoDto
{
    /// <summary>
    /// Unique identifier for the invoice
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Invoice ID from the tax authority system
    /// </summary>
    public string? InvoiceId { get; set; }

    /// <summary>
    /// Invoice number
    /// </summary>
    public string? InvoiceNumber { get; set; }

    /// <summary>
    /// Invoice template code
    /// </summary>
    public string? InvoiceTemplateCode { get; set; }

    /// <summary>
    /// Invoice series
    /// </summary>
    public string? InvoiceSeries { get; set; }

    /// <summary>
    /// Customer name
    /// </summary>
    public string? CustomerName { get; set; }

    /// <summary>
    /// Customer tax code
    /// </summary>
    public string? CustomerTaxCode { get; set; }

    /// <summary>
    /// Customer address
    /// </summary>
    public string? CustomerAddress { get; set; }

    /// <summary>
    /// Customer email
    /// </summary>
    public string? CustomerEmail { get; set; }

    /// <summary>
    /// Total amount before tax
    /// </summary>
    public decimal? TotalAmount { get; set; }

    /// <summary>
    /// Tax amount
    /// </summary>
    public decimal? TaxAmount { get; set; }

    /// <summary>
    /// Total amount including tax
    /// </summary>
    public decimal? TotalAmountWithTax { get; set; }

    /// <summary>
    /// Currency code
    /// </summary>
    public string? CurrencyCode { get; set; }

    /// <summary>
    /// Exchange rate
    /// </summary>
    public decimal? ExchangeRate { get; set; }

    /// <summary>
    /// Invoice date
    /// </summary>
    public DateTime? InvoiceDate { get; set; }

    /// <summary>
    /// Tax authority invoice status
    /// </summary>
    public CqtInvoiceStatus CqtInvoiceStatus { get; set; }

    /// <summary>
    /// Business invoice status
    /// </summary>
    public InvoiceStatus InvoiceStatus { get; set; }

    /// <summary>
    /// Error message if any
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Merchant invoice order ID
    /// </summary>
    public Guid MerchantInvoiceOrderId { get; set; }

    /// <summary>
    /// When the invoice was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// When the invoice was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// Related merchant invoice order information
    /// </summary>
    public MerchantInvoiceOrderSummary? MerchantInvoiceOrder { get; set; }
}

/// <summary>
/// Summary information for merchant invoice order
/// </summary>
public class MerchantInvoiceOrderSummary
{
    /// <summary>
    /// Unique identifier
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Order number for display purposes
    /// </summary>
    public string OrderNumber { get; set; } = string.Empty;

    /// <summary>
    /// Merchant branch ID
    /// </summary>
    public Guid MerchantBranchId { get; set; }

    /// <summary>
    /// Whether this order is currently active
    /// </summary>
    public bool IsActive { get; set; }
}
