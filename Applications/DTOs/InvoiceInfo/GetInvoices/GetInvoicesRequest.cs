using BuildingBlocks.Abstractions;
using Core.Enumerables;
using System.ComponentModel.DataAnnotations;

namespace Applications.DTOs.InvoiceInfo.GetInvoices;

/// <summary>
/// Request DTO for getting invoices with filtering capabilities
/// </summary>
public record GetInvoicesRequest : PaginationWithSortRequest
{


    /// <summary>
    /// Filter by merchant branch ID (optional)
    /// </summary>
    public Guid? MerchantBranchId { get; init; }

    /// <summary>
    /// Filter by merchant invoice order ID (optional)
    /// </summary>
    public Guid? MerchantInvoiceOrderId { get; init; }

    /// <summary>
    /// Filter by tax authority invoice status (optional)
    /// </summary>
    public CqtInvoiceStatus? CqtInvoiceStatus { get; init; }

    /// <summary>
    /// Filter by invoice business status (optional)
    /// </summary>
    public InvoiceStatus? InvoiceStatus { get; init; }

    /// <summary>
    /// Filter by creation date from (optional)
    /// </summary>
    public DateTime? FromDate { get; init; }

    /// <summary>
    /// Filter by creation date to (optional)
    /// </summary>
    public DateTime? ToDate { get; init; }
}
