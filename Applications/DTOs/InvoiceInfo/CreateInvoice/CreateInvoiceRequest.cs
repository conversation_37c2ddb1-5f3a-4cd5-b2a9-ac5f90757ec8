using System.ComponentModel.DataAnnotations;
using Core.Enumerables;

namespace Applications.DTOs.InvoiceInfo.CreateInvoice;

/// <summary>
/// Request để tạo InvoiceInfo mới
/// </summary>
public class CreateInvoiceRequest
{
    /// <summary>
    /// Invoice ID (bắt buộc, unique)
    /// </summary>
    [Required(ErrorMessage = "InvoiceId is required")]
    [MaxLength(100, ErrorMessage = "InvoiceId cannot exceed 100 characters")]
    public string InvoiceId { get; set; } = null!;

    /// <summary>
    /// ID của MerchantInvoiceOrder (bắt buộc)
    /// </summary>
    [Required(ErrorMessage = "MerchantInvoiceOrderId is required")]
    public Guid MerchantInvoiceOrderId { get; set; }

    /// <summary>
    /// Trạng thái CQT hóa đơn (bắt buộc)
    /// </summary>
    [Required(ErrorMessage = "CqtInvoiceStatus is required")]
    public CqtInvoiceStatus CqtInvoiceStatus { get; set; }

    /// <summary>
    /// Trạng thái hóa đơn (bắt buộc)
    /// </summary>
    [Required(ErrorMessage = "InvoiceStatus is required")]
    public InvoiceStatus InvoiceStatus { get; set; }

    /// <summary>
    /// Request data (JSON, bắt buộc)
    /// </summary>
    [Required(ErrorMessage = "RequestData is required")]
    public object RequestData { get; set; } = null!;

    /// <summary>
    /// Response data (JSON, bắt buộc)
    /// </summary>
    [Required(ErrorMessage = "ResponseData is required")]
    public object ResponseData { get; set; } = null!;

    /// <summary>
    /// Số hóa đơn (optional)
    /// </summary>
    [MaxLength(50, ErrorMessage = "InvoiceNumber cannot exceed 50 characters")]
    public string? InvoiceNumber { get; set; }

    /// <summary>
    /// Ký hiệu hóa đơn (optional)
    /// </summary>
    [MaxLength(20, ErrorMessage = "InvoiceSeries cannot exceed 20 characters")]
    public string? InvoiceSeries { get; set; }

    /// <summary>
    /// Tổng tiền (optional)
    /// </summary>
    public decimal? TotalAmount { get; set; }

    /// <summary>
    /// Tiền thuế (optional)
    /// </summary>
    public decimal? TaxAmount { get; set; }

    /// <summary>
    /// Tên khách hàng (optional)
    /// </summary>
    [MaxLength(255, ErrorMessage = "CustomerName cannot exceed 255 characters")]
    public string? CustomerName { get; set; }

    /// <summary>
    /// Mã số thuế khách hàng (optional)
    /// </summary>
    [MaxLength(20, ErrorMessage = "CustomerTaxCode cannot exceed 20 characters")]
    public string? CustomerTaxCode { get; set; }

    /// <summary>
    /// Thông báo lỗi (optional)
    /// </summary>
    [MaxLength(1000, ErrorMessage = "ErrorMessage cannot exceed 1000 characters")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Ghi chú (optional)
    /// </summary>
    [MaxLength(1000, ErrorMessage = "Notes cannot exceed 1000 characters")]
    public string? Notes { get; set; }
}
