namespace Applications.DTOs.Partners;

/// <summary>
/// Response DTO for partner information (excludes sensitive data)
/// </summary>
public class PartnerResponse
{
    /// <summary>
    /// Partner unique identifier
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Client ID for OAuth2 authentication (public identifier)
    /// </summary>
    public string ClientId { get; set; } = null!;

    /// <summary>
    /// Partner name/company name
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Contact email for this partner
    /// </summary>
    public string? ContactEmail { get; set; }

    /// <summary>
    /// Contact phone for this partner
    /// </summary>
    public string? ContactPhone { get; set; }

    /// <summary>
    /// Description/notes about this partner
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// JSON array of whitelisted IP addresses/CIDR ranges
    /// </summary>
    public string? IpWhitelist { get; set; }

    /// <summary>
    /// Whether IP whitelist checking is enabled for this partner
    /// </summary>
    public bool EnableIpWhitelist { get; set; }

    /// <summary>
    /// Whether this partner is currently active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// API rate limit per hour for this partner
    /// </summary>
    public int ApiRateLimitPerHour { get; set; }

    /// <summary>
    /// Monthly invoice purchase limit for this partner
    /// </summary>
    public decimal MonthlyInvoiceLimit { get; set; }

    /// <summary>
    /// Current month's invoice usage
    /// </summary>
    public decimal CurrentMonthUsage { get; set; }

    /// <summary>
    /// Partner creation date
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Partner last update date
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// Created by user ID
    /// </summary>
    public Guid CreatedBy { get; set; }
}
