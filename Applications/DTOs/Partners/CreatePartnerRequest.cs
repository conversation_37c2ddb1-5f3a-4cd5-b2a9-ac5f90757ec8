using System.ComponentModel.DataAnnotations;

namespace Applications.DTOs.Partners;

/// <summary>
/// Request DTO for creating a new partner
/// </summary>
public class CreatePartnerRequest
{
    /// <summary>
    /// Client ID for OAuth2 authentication (public identifier)
    /// </summary>
    [Required(ErrorMessage = "ClientId is required")]
    [StringLength(100, ErrorMessage = "ClientId cannot exceed 100 characters")]
    public string ClientId { get; set; } = null!;

    /// <summary>
    /// Client secret for OAuth2 authentication (will be hashed)
    /// </summary>
    [Required(ErrorMessage = "ClientSecret is required")]
    [StringLength(100, MinimumLength = 8, ErrorMessage = "ClientSecret must be between 8 and 100 characters")]
    public string ClientSecret { get; set; } = null!;

    /// <summary>
    /// HMAC secret for payload signature validation (will be stored as plaintext temporarily)
    /// </summary>
    [Required(ErrorMessage = "HmacSecret is required")]
    [StringLength(100, MinimumLength = 8, ErrorMessage = "HmacSecret must be between 8 and 100 characters")]
    public string HmacSecret { get; set; } = null!;

    /// <summary>
    /// Partner name/company name
    /// </summary>
    [Required(ErrorMessage = "Name is required")]
    [StringLength(200, ErrorMessage = "Name cannot exceed 200 characters")]
    public string Name { get; set; } = null!;

    /// <summary>
    /// Contact email for this partner
    /// </summary>
    [EmailAddress(ErrorMessage = "Invalid email format")]
    [StringLength(200, ErrorMessage = "ContactEmail cannot exceed 200 characters")]
    public string? ContactEmail { get; set; }

    /// <summary>
    /// Contact phone for this partner
    /// </summary>
    [StringLength(20, ErrorMessage = "ContactPhone cannot exceed 20 characters")]
    public string? ContactPhone { get; set; }

    /// <summary>
    /// Description/notes about this partner
    /// </summary>
    [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
    public string? Description { get; set; }

    /// <summary>
    /// JSON array of whitelisted IP addresses/CIDR ranges
    /// Example: ["***********/24", "********"]
    /// </summary>
    public string? IpWhitelist { get; set; }

    /// <summary>
    /// Whether IP whitelist checking is enabled for this partner
    /// </summary>
    public bool EnableIpWhitelist { get; set; } = true;

    /// <summary>
    /// Whether this partner is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// API rate limit per hour for this partner
    /// </summary>
    [Range(1, 100000, ErrorMessage = "ApiRateLimitPerHour must be between 1 and 100000")]
    public int ApiRateLimitPerHour { get; set; } = 1000;

    /// <summary>
    /// Monthly invoice purchase limit for this partner
    /// </summary>
    [Range(0, 10000000, ErrorMessage = "MonthlyInvoiceLimit must be between 0 and 10000000")]
    public decimal MonthlyInvoiceLimit { get; set; } = 10000;
}
