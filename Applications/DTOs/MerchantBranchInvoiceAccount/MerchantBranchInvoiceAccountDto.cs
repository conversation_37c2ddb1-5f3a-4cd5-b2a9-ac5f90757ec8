namespace Applications.DTOs.MerchantBranchInvoiceAccount;

public class MerchantBranchInvoiceAccountDto
{
    /// <summary>
    /// Tax number for the merchant branch
    /// </summary>
    public string TaxNumber { get; set; } = null!;

    /// <summary>
    /// Username for the invoice account
    /// </summary>
    public string InvoiceAccountUserName { get; set; } = null!;

    /// <summary>
    /// Password for the invoice account (should be encrypted in production)
    /// </summary>
    public string InvoiceAccountPassword { get; set; } = null!;

    /// <summary>
    /// Invoice account provider (hardcoded as "MBF")
    /// </summary>
    public string InvoiceAccountProvider { get; set; } = "MBF";

    /// <summary>
    /// When this invoice account becomes effective
    /// </summary>
    public DateTime EffectiveDate { get; set; }

    /// <summary>
    /// When this invoice account expires
    /// </summary>
    public DateTime ExpirationDate { get; set; }

    /// <summary>
    /// Foreign key to MerchantBranch (using Guid as per existing patterns)
    /// </summary>
    public Guid MerchantBranchId { get; set; }

    /// <summary>
    /// Merchant branch name for display purposes
    /// </summary>
    public string MerchantBranchName { get; set; } = null!;

    /// <summary>
    /// Foreign key reference to Partner entity's Id
    /// </summary>
    public Guid PartnerId { get; set; }

    /// <summary>
    /// Whether this invoice account is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;
}

public class MerchantBranchInvoiceAccountResponseDto : MerchantBranchInvoiceAccountDto
{
    public Guid Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public Guid? UpdatedBy { get; set; }
}