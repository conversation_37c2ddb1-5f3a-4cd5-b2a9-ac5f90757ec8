using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.SendInvoiceToCQT68.Raws;

/// <summary>
/// Request DTO cho API 4.6 Gửi hóa đơn đã ký lên Cơ quan thuế
/// URL: {{base_url}}/api/Invoice68/SendInvoiceToCQT68
/// Method: POST
/// ContentType: application/json
/// Authorization: Bear Token;ma_dvcs
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class SendInvoiceToCQT68Request
{
    /// <summary>
    /// Danh sách ID hóa đơn cần gửi
    /// Kiểu: Array string, Bắt buộc: X
    /// Mô tả: Danh sách ID hóa đơn cần gửi tới C<PERSON> quan Thuế
    /// Ví dụ: ["fa0281b2-2b9d-448f-a213-dccce06fe13f"]
    /// </summary>
    [JsonPropertyName("invs")]
    public List<string> invs { get; set; } = new();
    
    // /// <summary>
    // /// Chữ ký số
    // /// </summary>
    // [JsonPropertyName("cert_serial")]
    // public string cert_serial { get; set; } = string.Empty;

    /// <summary>
    /// Loại hóa đơn không mã hoặc có mã
    /// Kiểu: String, Bắt buộc: X
    /// Giá trị: 200: có mã, 203: không mã, 206: có mã từ MTT
    /// Lưu ý: Trong ví dụ JSON input sử dụng trường "mltdiep" nhưng mô tả trường là "type_cmd"
    /// </summary>
    [JsonPropertyName("type_cmd")]
    public string type_cmd { get; set; } = string.Empty;

    /// <summary>
    /// Trường mltdiep từ ví dụ JSON (có thể tương ứng với type_cmd)
    /// Kiểu: String
    /// Giá trị: 200: có mã, 203: không mã, 206: có mã từ MTT
    /// </summary>
    // [JsonPropertyName("mltdiep")]
    // public string? mltdiep { get; set; }
}
