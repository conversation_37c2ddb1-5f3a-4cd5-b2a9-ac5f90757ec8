using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.ExportInvoiceXmlPretreatment.Raws;

/// <summary>
/// Response DTO cho API 4.22 Xuất XML hóa đơn trước khi ký số bằng usb token qua Plugin
/// URL: {{base_url}}/api/Invoice68/ExportInvoiceXmlPretreatment?id={hdon_id}
/// Method: GET
/// ContentType: application/json
/// Authorization: Bear Token;ma_dvcs
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class ExportInvoiceXmlPretreatmentResponse
{
    /// <summary>
    /// ID của hóa đơn
    /// Kiểu: String
    /// Mô tả: ID của hóa đơn
    /// Ví dụ: "8d8e04a5-8089-474d-9c7b-403a33cb8897"
    /// </summary>
    [JsonPropertyName("hdon_id")]
    public string? hdon_id { get; set; }

    /// <summary>
    /// Dữ liệu XML của hóa đơn
    /// Kiểu: String
    /// Mô tả: Dữ liệu XML của hóa đơn
    /// Ví dụ: "<HDon></HDon>" (đây là ví dụ ngắn gọn, thực tế sẽ chứa cấu trúc XML đầy đủ của hóa đơn)
    /// </summary>
    [JsonPropertyName("InvoiceXmlData")]
    public string? InvoiceXmlData { get; set; }

    /// <summary>
    /// Dữ liệu XML của bảng kê kèm theo hóa đơn (nếu có)
    /// Kiểu: String
    /// Mô tả: Dữ liệu XML của bảng kê kèm theo hóa đơn (nếu có)
    /// Ví dụ: "<BKe></BKe>"
    /// </summary>
    [JsonPropertyName("InvoiceXmlBangKe")]
    public string? InvoiceXmlBangKe { get; set; }

    /// <summary>
    /// Số hóa đơn
    /// Kiểu: String
    /// Mô tả: Số hóa đơn
    /// Ví dụ: "12"
    /// </summary>
    [JsonPropertyName("shdon")]
    public string? shdon { get; set; }

    /// <summary>
    /// Đơn vị ký
    /// Kiểu: String
    /// Mô tả: Đơn vị ký
    /// Ví dụ: "CÔNG TY DEMO"
    /// </summary>
    [JsonPropertyName("dvky")]
    public string? dvky { get; set; }
}

/// <summary>
/// Error Response DTO cho API 4.22 khi có lỗi
/// </summary>
public class ExportInvoiceXmlPretreatmentErrorResponse
{
    /// <summary>
    /// Thông báo lỗi
    /// Kiểu: String
    /// Mô tả: Thông báo lỗi khi yêu cầu không hợp lệ
    /// Ví dụ: "Không tìm thấy hóa đơn", "Bạn chưa xử lý hóa đơn của ngày hôm trước"
    /// </summary>
    [JsonPropertyName("error")]
    public string? error { get; set; }
}
