using System.Text.Json.Serialization;
using Applications.DTOs.MobiFoneInvoice.Common;

namespace Applications.DTOs.MobiFoneInvoice.GetInvoiceById.Raws;

/// <summary>
/// Response DTO cho API 4.13 L<PERSON>y thông tin hóa đơn theo ID
/// <PERSON><PERSON><PERSON> là DTO raw theo chuẩn tài li<PERSON>, giữ nguyên tên field
/// </summary>
public class GetInvoiceByIdResponse
{
    public string? hdon_id { get; set; }
    public string? cctbao_id { get; set; }
    public object? hdlket_id { get; set; }
    public string? tthai { get; set; }
    public int tthdon { get; set; }
    public string? khieu { get; set; }
    public string? shdon { get; set; }
    public DateTime tdlap { get; set; }
    public string? dvtte { get; set; }
    public double tgia { get; set; }
    public object? gchu { get; set; }
    public string? tnmua { get; set; }
    public string? mnmua { get; set; }
    public string? ten { get; set; }
    public string? mst { get; set; }
    public string? dchi { get; set; }
    public string? email { get; set; }
    public string? sdtnmua { get; set; }
    public string? stknmua { get; set; }
    public string? htttoan { get; set; }
    public string? stknban { get; set; }
    public string? sbmat { get; set; }
    public string? mdvi { get; set; }
    public string? nglap { get; set; }
    public DateTime nlap { get; set; }
    public object? ngsua { get; set; }
    public object? nsua { get; set; }
    public double tgtcthue { get; set; }
    public double tgtthue { get; set; }
    public object? ttcktmai { get; set; }
    public double tgtttbso { get; set; }
    public string? tgtttbchu { get; set; }
    public object? dlqrcode { get; set; }
    public string? sdhang { get; set; }
    public object? shdon1 { get; set; }
    public object? mccqthue { get; set; }
    public string? ngky { get; set; }
    public DateTime nky { get; set; }
    public object? signature { get; set; }
    public object? hthdbtthe { get; set; }
    public object? tdlhdbtthe { get; set; }
    public object? khmshdbtthe { get; set; }
    public object? khhdbtthe { get; set; }
    public object? shdbtthe { get; set; }
    public double tgtphi { get; set; }
    public object? tgtcthue0 { get; set; }
    public object? tgtthue0 { get; set; }
    public object? ttcktmai0 { get; set; }
    public object? tgtttbso0 { get; set; }
    public object? tgtcthue5 { get; set; }
    public object? tgtthue5 { get; set; }
    public object? ttcktmai5 { get; set; }
    public object? tgtttbso5 { get; set; }
    public object? tgtcthue10 { get; set; }
    public object? tgtthue10 { get; set; }
    public object? ttcktmai10 { get; set; }
    public object? tgtttbso10 { get; set; }
    public object? tgtcthuekct { get; set; }
    public object? tgtthuekct { get; set; }
    public object? ttcktmaikct { get; set; }
    public object? tgtttbsokct { get; set; }
    public object? tgtcthuekkk { get; set; }
    public object? tgtthuekkk { get; set; }
    public object? ttcktmaikkk { get; set; }
    public object? tgtttbsokkk { get; set; }
    public object? tgtphi0 { get; set; }
    public object? tgtphi5 { get; set; }
    public object? tgtphi10 { get; set; }
    public object? tgtphikct { get; set; }
    public object? tgtphikkk { get; set; }
    public object? lhdon { get; set; }
    public object? lddnbo { get; set; }
    public object? tnvchuyen { get; set; }
    public object? ptvchuyen { get; set; }
    public object? dckhoxuat { get; set; }
    public object? dckhonhap { get; set; }
    public object? tennguoinhanhang { get; set; }
    public object? mstnguoinhanhang { get; set; }
    public object? phongban { get; set; }
    public object? veviec { get; set; }
    public object? sohopdong { get; set; }
    public object? hdon68_id_lk { get; set; }
    public object? mtdiep_cqt { get; set; }
    public string? mtdiep_gui { get; set; }
    public object? tthdon_old { get; set; }
    public object? hdon_id_old { get; set; }
    public int is_hdcma { get; set; }
    public object? hdon_ghichu { get; set; }
    public int tthdon_original { get; set; }
    public double kygui_cqt { get; set; }
    public object? hdktngay { get; set; }
    public string? tnhban { get; set; }
    public string? tnhmua { get; set; }
    public object? hddckptquan { get; set; }
    public object? sbke { get; set; }
    public object? faxban { get; set; }
    public object? webban { get; set; }
    public object? sqdbants { get; set; }
    public object? nqdbants { get; set; }
    public object? cqqdbants { get; set; }
    public object? htbants { get; set; }
    public object? cmndmua { get; set; }
    public object? hdvc { get; set; }
    public object? hvtnxhang { get; set; }
    public object? hdktso { get; set; }
    public object? nbke { get; set; }
    public object? ddvchden { get; set; }
    public object? tgvchdtu { get; set; }
    public object? tgvchdden { get; set; }
    public object? sdtban { get; set; }
    public double tkcktmn { get; set; }
    public double tgtttbso_last { get; set; }
    public string? mdvqhnsach_mua { get; set; }
    public object? mdvqhnsach_ban { get; set; }
    public object? stbao { get; set; }
    public object? ntbao { get; set; }
    public object? kmai { get; set; }
    public object? tgtcthuek { get; set; }
    public object? tgtthuek { get; set; }
    public object? ttcktmaik { get; set; }
    public object? tgtttbsok { get; set; }
    public string? error_status { get; set; }
    public bool issendmail { get; set; }
    public object? docngoaitetv { get; set; }
    public object? giamthuebanhang20 { get; set; }
    public object? tienthuegtgtgiam { get; set; }
    public object? lhdclquan { get; set; }
    public object? khmshdclquan { get; set; }
    public object? khhdclquan { get; set; }
    public object? shdclquan { get; set; }
    public object? nlhdclquan { get; set; }
    public object? tiletienthuegtgtgiam { get; set; }
    public object? tgtkhac { get; set; }
    public object? thdon { get; set; }
    public object? qtich { get; set; }
    public object? cnctru { get; set; }
    public object? ngccmnd { get; set; }
    public object? nccmnd { get; set; }
    public object? ktnhap { get; set; }
    public object? thang { get; set; }
    public object? nam { get; set; }
    public object? msbl_tncn { get; set; }
    public object? tlkthu { get; set; }
    public int loaihd { get; set; }
    public object? cmnd { get; set; }
    public object? dvtinh { get; set; }
    public object? hdtndinh { get; set; }
    public object? hdckhau { get; set; }
    public object? kdbhbbuoc { get; set; }
    public object? ktnhapstr { get; set; }
    public object? cnkctru { get; set; }
    public object? tudongtinhtien { get; set; }
    public object? tgtkcthue { get; set; }
    public object? tthang { get; set; }
    public object? dthang { get; set; }
    public object? ttthanhtoan { get; set; }
    public object? orderid { get; set; }
    public int signing { get; set; }
    public string? shchieu { get; set; }
    public string? mchang { get; set; }
    public string? tchang { get; set; }
    public object? kttndkh { get; set; }
    public List<HoaDon68ChiTiet>? HoaDon68_ChiTiet { get; set; }
    public List<object>? HoaDon68_Phi { get; set; }
    public List<object>? HoaDon68_Khac { get; set; }
}


public class HoaDon68ChiTiet
{
    public string? cthdon_id { get; set; }
    public string? hdon_id { get; set; }
    public object? tchat { get; set; }
    public string? stt { get; set; }
    public string? ma { get; set; }
    public string? ten { get; set; }
    public string? mdvtinh { get; set; }
    public string? dvtinh { get; set; }
    public double sluong { get; set; }
    public double dgia { get; set; }
    public double thtien { get; set; }
    public double tlckhau { get; set; }
    public double stckhau { get; set; }
    public string? tsuat { get; set; }
    public object? ptthue { get; set; }
    public double tthue { get; set; }
    public double tgtien { get; set; }
    public int kmai { get; set; }
    public object? tsuatstr { get; set; }
    public object? loaive { get; set; }
    public object? ngayhl { get; set; }
    public object? ngayhethl { get; set; }
    public int phanloaihd { get; set; }
    public int trangthaiqrcode { get; set; }
    public object? pl_loaive_id { get; set; }
    public object? tiletienthuegtgtgiam { get; set; }
    public object? tienthuegtgtgiam { get; set; }
    public object? loaixang { get; set; }
    public object? pl_petro_id { get; set; }
    public object? tu_ngay { get; set; }
    public object? den_ngay { get; set; }
    public object? lhhdthu { get; set; }
    public string? skhung { get; set; }
    public string? smay { get; set; }
    public string? bksptvchuyen { get; set; }
    public string? tnghang { get; set; }
    public string? dcnghang { get; set; }
    public string? mstnghang { get; set; }
    public string? mddnghang { get; set; }
}

/// <summary>
/// Dữ liệu chi tiết hóa đơn kế thừa từ InvoiceListItem và bổ sung thêm các trường đặc biệt
/// </summary>
public class InvoiceDetailData : InvoiceListItem
{
    /// <summary>
    /// Danh sách chi tiết hóa đơn (tương tự details)
    /// Kiểu: Array
    /// </summary>
    [JsonPropertyName("HoaDon68_ChiTiet")]
    public List<InvoiceDetailItem>? HoaDon68_ChiTiet { get; set; }
}

/// <summary>
/// Chi tiết từng dòng hóa đơn
/// </summary>
public class InvoiceDetailItem
{
    /// <summary>
    /// ID chi tiết hóa đơn
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("cthdon_id")]
    public string? cthdon_id { get; set; }

    /// <summary>
    /// ID hóa đơn
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("hdon_id")]
    public string? hdon_id { get; set; }

    /// <summary>
    /// Tính chất
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("tchat")]
    public string? tchat { get; set; }

    /// <summary>
    /// Số thứ tự dòng của hóa đơn
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("stt")]
    public string? stt { get; set; }

    /// <summary>
    /// Mã hàng
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("ma")]
    public string? ma { get; set; }

    /// <summary>
    /// Tên hàng
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("ten")]
    public string? ten { get; set; }

    /// <summary>
    /// Mã đơn vị tính
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("mdvtinh")]
    public string? mdvtinh { get; set; }

    /// <summary>
    /// Tên đơn vị tính
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("dvtinh")]
    public string? dvtinh { get; set; }

    /// <summary>
    /// Số lượng
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("sluong")]
    public decimal? sluong { get; set; }

    /// <summary>
    /// Đơn giá
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("dgia")]
    public decimal? dgia { get; set; }

    /// <summary>
    /// Tiền trước thuế
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("thtien")]
    public decimal? thtien { get; set; }

    /// <summary>
    /// Phần trăm chiết khấu
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tlckhau")]
    public decimal? tlckhau { get; set; }

    /// <summary>
    /// Tiền chiết khấu
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("stckhau")]
    public decimal? stckhau { get; set; }

    /// <summary>
    /// Mã loại thuế suất
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("tsuat")]
    public string? tsuat { get; set; }

    /// <summary>
    /// Phương thức thuế
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("ptthue")]
    public string? ptthue { get; set; }

    /// <summary>
    /// Tiền thuế
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tthue")]
    public decimal? tthue { get; set; }

    /// <summary>
    /// Tổng tiền
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("tgtien")]
    public decimal? tgtien { get; set; }

    /// <summary>
    /// Tính chất của dòng hàng hóa dịch vụ
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("kmai")]
    public int? kmai { get; set; }

    /// <summary>
    /// Chuỗi thuế suất
    /// Kiểu: String
    /// </summary>
    [JsonPropertyName("tsuatstr")]
    public string? tsuatstr { get; set; }
}

/// <summary>
/// Response wrapper cho trường hợp có lỗi
/// </summary>
public class GetInvoiceByIdErrorResponse
{
    /// <summary>
    /// Thông báo lỗi
    /// Kiểu: String
    /// Các lỗi có thể xảy ra:
    /// - "Không tìm thấy hóa đơn": Xảy ra khi không tìm thấy hóa đơn bằng ID đã truyền vào
    /// - "Id không được bỏ trống": Xảy ra nếu trường id trong tham số truy vấn bị thiếu hoặc rỗng
    /// - "Id không đúng định dạng": Xảy ra nếu id truyền vào không đúng định dạng GUID
    /// </summary>
    [JsonPropertyName("error")]
    public string? error { get; set; }

    /// <summary>
    /// Thông báo lỗi (dạng Message)
    /// Kiểu: String
    /// Ví dụ: "Authorization has been denied for this request."
    /// </summary>
    [JsonPropertyName("Message")]
    public string? Message { get; set; }
}
