using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.ExportXMLHoadon.Raws;

/// <summary>
/// Response DTO cho API 4.15 Lấy thông tin XML hóa đơn
/// URL: {{base_url}}/api/Invoice68/ExportXMLHoadon?id={hdon_id}
/// Method: GET
/// ContentType: application/json
/// Authorization: Bear Token;ma_dvcs
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class ExportXMLHoadonResponse
{
    /// <summary>
    /// ID của hóa đơn
    /// Kiểu: String
    /// Mô tả: ID của hóa đơn
    /// </summary>
    [JsonPropertyName("hdon_id")]
    public string? hdon_id { get; set; }

    /// <summary>
    /// Chuỗi XML đầy đủ của hóa đơn
    /// Kiểu: String
    /// Mô tả: Chứa thông tin chi tiết hóa đơn, thông tin bên bán, bên mua, chi tiết hàng hóa, tổng tiền, QR code, chữ ký số...
    /// Ví dụ: "<HDon><DLHDon Id=\"data\">...</DLHDon><DLQRCode>...</DLQRCode><DSCKS>...</DSCKS></HDon>"
    /// </summary>
    [JsonPropertyName("InvoiceXmlData")]
    public string? InvoiceXmlData { get; set; }

    /// <summary>
    /// Chuỗi XML của bảng kê đi kèm hóa đơn (nếu có)
    /// Kiểu: String
    /// Mô tả: Chuỗi XML của bảng kê đi kèm hóa đơn (nếu có)
    /// Ví dụ: "<BKe></BKe>"
    /// </summary>
    [JsonPropertyName("InvoiceXmlBangKe")]
    public string? InvoiceXmlBangKe { get; set; }

    /// <summary>
    /// Số hóa đơn
    /// Kiểu: String
    /// Mô tả: Số hóa đơn
    /// Ví dụ: "12"
    /// </summary>
    [JsonPropertyName("shdon")]
    public string? shdon { get; set; }

    /// <summary>
    /// Tên đơn vị ký hóa đơn
    /// Kiểu: String
    /// Mô tả: Tên đơn vị ký hóa đơn
    /// Ví dụ: "CÔNG TY DEMO"
    /// </summary>
    [JsonPropertyName("dvky")]
    public string? dvky { get; set; }
}

/// <summary>
/// Error Response DTO cho API 4.15 khi có lỗi
/// </summary>
public class ExportXMLHoadonErrorResponse
{
    /// <summary>
    /// Thông báo lỗi
    /// Kiểu: String
    /// Mô tả: Thông báo lỗi khi yêu cầu không hợp lệ
    /// Ví dụ: "Không tìm thấy hóa đơn", "Bạn chưa xử lý hóa đơn của ngày hôm trước"
    /// </summary>
    [JsonPropertyName("error")]
    public string? error { get; set; }
}
