using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.GetInvoiceFromdateTodate.Raws;

/// <summary>
/// Request DTO cho API 4.16 L<PERSON>y danh sách hóa đơn theo khoảng thời gian
/// URL: {{base_url}}/api/Invoice68/GetInvoiceFromdateTodate
/// Method: POST
/// ContentType: application/json
/// Authorization: Bear Token;ma_dvcs
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class GetInvoiceFromdateTodateRequest
{
    /// <summary>
    /// Ngày bắt đầu của khoảng thời gian tìm kiếm hóa đơn
    /// Kiểu: String
    /// Bắt buộc: X
    /// Mô tả: Ngày bắt đầu của khoảng thời gian tìm kiếm hóa đơn
    /// Định dạng: YYYY-MM-DD
    /// Ví dụ: "2021-12-01"
    /// </summary>
    [JsonPropertyName("tu_ngay")]
    public string tu_ngay { get; set; } = string.Empty;

    /// <summary>
    /// Ngày kết thúc của khoảng thời gian tìm kiếm hóa đơn
    /// Kiểu: String
    /// Bắt buộc: X
    /// Mô tả: Ngày kết thúc của khoảng thời gian tìm kiếm hóa đơn
    /// Định dạng: YYYY-MM-DD
    /// Ví dụ: "2021-12-11"
    /// </summary>
    [JsonPropertyName("den_ngay")]
    public string den_ngay { get; set; } = string.Empty;
}
