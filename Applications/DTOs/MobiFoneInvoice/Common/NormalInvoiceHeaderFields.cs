using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.Common;

/// <summary>
/// Base class chứa tất cả các trường chung cho header của hóa đơn
/// Đ<PERSON>y là những trường xuất hiện ở hầu hết hoặc tất cả các loại hóa đơn
/// </summary>
public class NormalInvoiceHeaderFields
{
    /// <summary>
    /// Là id của dải ký hiệu hóa đơn (Lấy theo trường qlkhsdung_id của API 1.b)
    /// </summary>
    [JsonPropertyName("cctbao_id")]
    public string cctbao_id { get; set; } = string.Empty;

    /// <summary>
    /// Id hóa đơn (Tạo mới: Không truyền lên API, Sửa/Xóa: Bắt buộc truyền lên API)
    /// </summary>
    [JsonPropertyName("hdon_id")]
    public string? hdon_id { get; set; }

    /// <summary>
    /// Ng<PERSON>y hóa đơn (Định dạng yyyy-MM-dd)
    /// </summary>
    [JsonPropertyName("nlap")]
    public string nlap { get; set; } = string.Empty;

    /// <summary>
    /// Số đơn hàng
    /// </summary>
    [JsonPropertyName("sdhang")]
    public string? sdhang { get; set; }

    /// <summary>
    /// Ký hiệu hóa đơn (Dữ liệu hệ thống trả về)
    /// </summary>
    [JsonPropertyName("khieu")]
    public string? khieu { get; set; }

    /// <summary>
    /// Số hóa đơn (Dữ liệu hệ thống trả về)
    /// </summary>
    [JsonPropertyName("shdon")]
    public decimal? shdon { get; set; }

    /// <summary>
    /// Trạng thái gửi Cơ quan thuế (CQT)
    /// "Chờ ký", "Đã ký", "Chờ cấp mã", "TBSS sai định dạng", "CQT Không cấp mã", 
    /// "Chờ phản hồi", "CQT không tiếp nhận HĐ", "Chấp nhận TBSS", "Đã gửi", 
    /// "CQT đã nhận", "Đã cấp mã", "Không chấp nhận TBSS"
    /// </summary>
    [JsonPropertyName("tthai")]
    public string? tthai { get; set; }

    /// <summary>
    /// Trạng thái Hóa đơn
    /// 0: "Gốc", 1: "Giải trình", 2: "Thay thế", 3: "Hủy", 4: "Rà soát", 
    /// 5: "Điều chỉnh", 7: "Chờ điều chỉnh", 11: "Bị điều chỉnh", 
    /// 13: "Chờ hủy", 15: "Chờ thay thế", 17: "Bị thay thế", 
    /// 19: "Điều chỉnh tăng", 21: "Điều chỉnh giảm", 23: "Điều chỉnh thông tin"
    /// </summary>
    [JsonPropertyName("tthdon")]
    public decimal tthdon { get; set; } = 0;

    /// <summary>
    /// Mã của cơ quan thuế (Dữ liệu hệ thống trả về khi ký gửi HĐ)
    /// </summary>
    [JsonPropertyName("mccqthue")]
    public string? mccqthue { get; set; }

    /// <summary>
    /// Mã tra cứu hóa đơn (Dữ liệu hệ thống trả về)
    /// </summary>
    [JsonPropertyName("sbmat")]
    public string? sbmat { get; set; }

    /// <summary>
    /// Mã ngoại tệ (Mặc định là "VND")
    /// </summary>
    [JsonPropertyName("dvtte")]
    public string dvtte { get; set; } = "VND";

    /// <summary>
    /// Tỷ giá (Nếu ngoại tệ là "VND" thì mặc định là 1)
    /// </summary>
    [JsonPropertyName("tgia")]
    public decimal tgia { get; set; } = 1;

    /// <summary>
    /// Phương thức thanh toán
    /// Gồm một trong các giá trị sau: "Tiền mặt", "Chuyển khoản", "Tiền mặt/Chuyển khoản"
    /// </summary>
    [JsonPropertyName("htttoan")]
    public string htttoan { get; set; } = string.Empty;

    /// <summary>
    /// Số điện thoại người bán
    /// </summary>
    [JsonPropertyName("sdtnban")]
    public string? sdtnban { get; set; }

    /// <summary>
    /// Tài khoản ngân hàng bên bán
    /// </summary>
    [JsonPropertyName("stknban")]
    public string? stknban { get; set; }

    /// <summary>
    /// Tên ngân hàng bên bán
    /// </summary>
    [JsonPropertyName("tnhban")]
    public string? tnhban { get; set; }

    /// <summary>
    /// Mã khách hàng
    /// </summary>
    [JsonPropertyName("mnmua")]
    public string? mnmua { get; set; }

    /// <summary>
    /// Mã số thuế bên mua
    /// </summary>
    [JsonPropertyName("mst")]
    public string? mst { get; set; }

    /// <summary>
    /// Tên người mua
    /// </summary>
    [JsonPropertyName("tnmua")]
    public string? tnmua { get; set; }

    /// <summary>
    /// Email của người mua
    /// </summary>
    [JsonPropertyName("email")]
    public string? email { get; set; }

    /// <summary>
    /// Tên đơn vị
    /// </summary>
    [JsonPropertyName("ten")]
    public string? ten { get; set; }

    /// <summary>
    /// Địa chỉ người mua
    /// </summary>
    [JsonPropertyName("dchi")]
    public string? dchi { get; set; }

    /// <summary>
    /// Tài khoản ngân hàng bên mua
    /// </summary>
    [JsonPropertyName("stknmua")]
    public string? stknmua { get; set; }

    /// <summary>
    /// Số điện thoại người mua
    /// </summary>
    [JsonPropertyName("sdtnmua")]
    public string? sdtnmua { get; set; }

    /// <summary>
    /// Tên ngân hàng bên mua
    /// </summary>
    [JsonPropertyName("tnhmua")]
    public string? tnhmua { get; set; }

    /// <summary>
    /// Tên cửa hàng
    /// </summary>
    [JsonPropertyName("tchang")]
    public string? tchang { get; set; }

    /// <summary>
    /// Mã cửa hàng
    /// </summary>
    [JsonPropertyName("mchang")]
    public string? mchang { get; set; }

    /// <summary>
    /// Mã đơn vị quan hệ ngân sách (Mã số đơn vị có quan hệ với ngân sách) - Bắt buộc nếu có
    /// </summary>
    [JsonPropertyName("mdvqhnsach_mua")]
    public string? mdvqhnsach_mua { get; set; }

    /// <summary>
    /// Số hộ chiếu (Số hộ chiếu/Giấy tờ nhập xuất cảnh) - Bắt buộc nếu có
    /// </summary>
    [JsonPropertyName("shchieu")]
    public string? shchieu { get; set; }

    /// <summary>
    /// Tổng tiền trước thuế của cả hóa đơn
    /// </summary>
    [JsonPropertyName("tgtcthue")]
    public decimal tgtcthue { get; set; }

    /// <summary>
    /// Tổng tiền thuế của cả hóa đơn
    /// </summary>
    [JsonPropertyName("tgtthue")]
    public decimal tgtthue { get; set; }

    /// <summary>
    /// Tổng tiền sau thuế của cả hóa đơn
    /// </summary>
    [JsonPropertyName("tgtttbso")]
    public decimal tgtttbso { get; set; }

    /// <summary>
    /// Tổng tiền cuối cùng của cả hóa đơn
    /// </summary>
    [JsonPropertyName("tgtttbso_last")]
    public decimal tgtttbso_last { get; set; }

    /// <summary>
    /// Tổng tiền chiết khấu thương mại - Bắt buộc nếu có
    /// </summary>
    [JsonPropertyName("tkcktmn")]
    public decimal? tkcktmn { get; set; }

    /// <summary>
    /// Tổng tiền phí của hóa đơn - Bắt buộc nếu có
    /// </summary>
    [JsonPropertyName("tgtphi")]
    public decimal? tgtphi { get; set; }

    /// <summary>
    /// Mã đơn vị tạo hóa đơn
    /// </summary>
    [JsonPropertyName("mdvi")]
    public string mdvi { get; set; } = string.Empty;

    /// <summary>
    /// Phân biệt hóa đơn có mã/không có mã
    /// 0: Không mã, 1: Có mã
    /// </summary>
    [JsonPropertyName("is_hdcma")]
    public int is_hdcma { get; set; }

    /// <summary>
    /// Id hóa đơn gốc - Bắt buộc với khi tạo hóa đơn điều chỉnh, thay thế hóa đơn 78. 
    /// Nếu tạo hóa đơn điều chỉnh 32 thì không truyền.
    /// </summary>
    [JsonPropertyName("hdon_id_old")]
    public string? hdon_id_old { get; set; }

    /// <summary>
    /// Loại hóa đơn có liên quan (Loại hóa đơn bị thay thế/điều chỉnh)
    /// Bắt buộc nếu điều chỉnh hóa đơn 32.
    /// </summary>
    [JsonPropertyName("lhdclquan")]
    public decimal? lhdclquan { get; set; }

    /// <summary>
    /// Ký hiệu mẫu số hóa đơn có liên quan (Ký hiệu mẫu số hóa đơn bị thay thế/điều chỉnh)
    /// Bắt buộc nếu điều chỉnh hóa đơn 32.
    /// </summary>
    [JsonPropertyName("khmshdclquan")]
    public string? khmshdclquan { get; set; }

    /// <summary>
    /// Ký hiệu hóa đơn có liên quan (Ký hiệu hóa đơn bị thay thế/điều chỉnh)
    /// Bắt buộc nếu điều chỉnh hóa đơn 32.
    /// </summary>
    [JsonPropertyName("khhdclquan")]
    public string? khhdclquan { get; set; }

    /// <summary>
    /// Số hóa đơn có liên quan (Số hóa đơn bị thay thế/điều chỉnh)
    /// Bắt buộc nếu điều chỉnh hóa đơn 32.
    /// </summary>
    [JsonPropertyName("shdclquan")]
    public string? shdclquan { get; set; }

    /// <summary>
    /// Ngày lập hóa đơn có liên quan (Ngày lập hóa đơn bị thay thế/điều chỉnh)
    /// Bắt buộc nếu điều chỉnh hóa đơn 32. Định dạng yyyy-MM-dd.
    /// </summary>
    [JsonPropertyName("nlhdclquan")]
    public string? nlhdclquan { get; set; }
}
