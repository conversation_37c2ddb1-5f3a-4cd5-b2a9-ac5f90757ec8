using System.Text.Json.Serialization;
using Applications.DTOs.MobiFoneInvoice.Common;

namespace Applications.DTOs.MobiFoneInvoice.SaveAndSignHoadon78.Raws;

/// <summary>
/// Request DTO cho API 4.4 Hàm tạo mới và ký gửi hóa đơn bằng HSM nhà cung cấp khác, file mềm, Sim PKI
/// URL: {{base_url}}/api/Invoice68/SaveAndSignHoadon78
/// Method: POST
/// ContentType: application/json
/// Authorization: Bear Token;ma_dvcs
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// </summary>
public class SaveAndSignHoadon78Request
{
    /// <summary>
    /// Chế độ chỉnh sửa
    /// Kiểu: Int, Độ dài: 1, Bắt buộc: X
    /// Giá trị: 1: <PERSON><PERSON><PERSON> mới, 2: <PERSON><PERSON><PERSON>, 3: <PERSON><PERSON><PERSON> hóa đơn
    /// </summary>
    [JsonPropertyName("editmode")]
    public int editmode { get; set; }

    /// <summary>
    /// Chữ ký số HSM MobiFone
    /// Kiểu: string, Bắt buộc: X
    /// Ghi chú: không cần truyền
    /// </summary>
    [JsonPropertyName("cer_serial")]
    public string? cer_serial { get; set; }

    /// <summary>
    /// Loại hóa đơn không mã hoặc có mã
    /// Kiểu: string, Bắt buộc: X
    /// Giá trị: Có mã: 200, Không mã: 203, Có mã từ MTT: 206
    /// </summary>
    [JsonPropertyName("type_cmd")]
    public string type_cmd { get; set; } = string.Empty;

    /// <summary>
    /// Có gửi CQT?
    /// Kiểu: string, Bắt buộc: X
    /// Giá trị: Notsend: Không gửi
    /// </summary>
    [JsonPropertyName("guiCQT")]
    public string? guiCQT { get; set; }

    /// <summary>
    /// Đơn vị gọi API
    /// Kiểu: string, Bắt buộc: X
    /// Giá trị: 1
    /// </summary>
    [JsonPropertyName("is_api")]
    public string is_api { get; set; } = string.Empty;

    /// <summary>
    /// Mảng dữ liệu chứa thông tin hóa đơn GTGT
    /// Sử dụng GTGTInvoiceFields từ Common
    /// </summary>
    [JsonPropertyName("data")]
    public List<GTGTInvoiceData> data { get; set; } = [];
}

public class GTGTInvoiceData
{
    public string? cctbao_id { get; set; }
    public string? nlap { get; set; }
    public string? khieu { get; set; }
    public string? sdhang { get; set; }
    public string? dvtte { get; set; }
    public int docngoaitetv { get; set; }
    public string? tgia { get; set; }
    public string? htttoan { get; set; }
    public string? stknban { get; set; }
    public string? tnhban { get; set; }
    public string? mnmua { get; set; }
    public string? mst { get; set; }
    public string? tnmua { get; set; }
    public string? email { get; set; }
    public string? ten { get; set; }
    public string? dchi { get; set; }
    public string? stknmua { get; set; }
    public string? tnhmua { get; set; }
    public string? sdtnmua { get; set; }
    public int ttcktmai { get; set; }
    public int tgtcthue { get; set; }
    public int tgtthue { get; set; }
    public int tgtttbso { get; set; }
    public int tkcktmn { get; set; }
    public int tgtphi { get; set; }
    public int tgtttbso_last { get; set; }
    public string? tgtttbchu { get; set; }
    public string? mdvi { get; set; }
    public List<Detail>? details { get; set; }
    public int is_hdcma { get; set; }
}

public class Datum
{
    public string? stt { get; set; }
    public string? ma { get; set; }
    public string? kmai { get; set; }
    public string? ten { get; set; }
    public string? mdvtinh { get; set; }
    public int sluong { get; set; }
    public int dgia { get; set; }
    public int thtien { get; set; }
    public int tlckhau { get; set; }
    public int stckhau { get; set; }
    public int tthue { get; set; }
    public int tgtien { get; set; }
    public string? tsuat { get; set; }
}

public class Detail
{
    public List<Datum>? data { get; set; }
}