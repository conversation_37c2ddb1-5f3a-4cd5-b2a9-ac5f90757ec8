﻿using MediatR;
using Microsoft.AspNetCore.Http;
using BuildingBlocks.Abstractions;

namespace Applications.Behaviors;

public class TraceIdBehavior<TRequest, TResponse>(IHttpContextAccessor httpContextAccessor)
    : IPipelineBehavior<TRequest, TResponse>
    where TRequest : notnull, IRequest<TResponse>
    where TResponse : notnull
{
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var response = await next();

        // Náº¿u response là  Response<T>, set TraceId
        if (response is Response<object> responseObj)
        {
            responseObj.TraceId = httpContextAccessor.HttpContext?.TraceIdentifier;
        }
        // Xá»­ là½ cÃ¡c kiá»ƒu Response khÃ¡c náº¿u cáº§n
        else
        {
            // Sá»­ dá»¥ng reflection Ä‘á»ƒ tÃ¬m vÃ  set TraceId property
            var responseType = response.GetType();
            if (responseType.IsGenericType && responseType.GetGenericTypeDefinition() == typeof(Response<>))
            {
                var traceIdProperty = responseType.GetProperty("TraceId");
                if (traceIdProperty != null && traceIdProperty.CanWrite)
                {
                    traceIdProperty.SetValue(response, httpContextAccessor.HttpContext?.TraceIdentifier);
                }
            }
        }

        return response;
    }
}
