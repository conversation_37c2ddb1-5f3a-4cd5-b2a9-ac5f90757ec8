# Bearer Token Authentication Guide

## Tổng quan

Hệ thống này cung cấp một giải pháp xác thực Bearer Token đơn giản cho các endpoint chỉ cần xác thực token mà không cần các lớp bảo mật phức tạp khác (HMAC signature, IP whitelist, etc.).

## C<PERSON>c thành phần đã tạo

### 1. BearerTokenAuthAttribute (`API/Attributes/BearerTokenAuthAttribute.cs`)

Attribute xác thực Bearer Token đơn giản:
- Chỉ kiểm tra Bearer token trong header Authorization
- Sử dụng TokenService hiện có để validate token
- Thiết lập user context cho request
- Không áp dụng các middleware bảo mật khác

**Cách sử dụng:**
```csharp
[HttpGet("protected")]
[BearerTokenAuth]
public async Task<IActionResult> GetProtectedData()
{
    // Endpoint được bảo vệ bởi Bearer token
    return Ok("Protected data");
}
```

### 2. ICurrentUserService & CurrentUserService

**Interface:** `Applications/Interfaces/Services/ICurrentUserService.cs`
**Implementation:** `Infrastructure/Services/CurrentUserService.cs`

Service cung cấp thông tin về user hiện tại:

```csharp
public interface ICurrentUserService
{
    Guid? GetCurrentPartnerId();
    string? GetCurrentPartnerName();
    Task<Partner?> GetCurrentPartnerAsync();
    Task<Partner?> GetCurrentPartnerWithDetailsAsync();
    bool IsAuthenticated();
    DateTime? GetTokenExpiration();
    bool IsSimpleBearerAuth();
    string[] GetCurrentUserScopes();
}
```

### 3. BearerAuthBaseController (`API/Controllers/BearerAuthBaseController.cs`)

Base controller cung cấp các thuộc tính và phương thức tiện ích:

```csharp
public abstract class BearerAuthBaseController : ControllerBase
{
    protected Guid? CurrentPartnerId { get; }
    protected string? CurrentPartnerName { get; }
    protected bool IsAuthenticated { get; }
    protected DateTime? TokenExpiration { get; }
    protected bool IsSimpleBearerAuth { get; }
    protected string[] CurrentUserScopes { get; }
    
    protected async Task<Partner?> GetCurrentPartnerAsync();
    protected async Task<Partner?> GetCurrentPartnerWithDetailsAsync();
    protected void EnsureAuthenticated();
    protected async Task<Partner> GetCurrentPartnerOrThrowAsync();
}
```

### 4. ExampleBearerController (`API/Controllers/ExampleBearerController.cs`)

Controller ví dụ minh họa cách sử dụng:

**Endpoints:**
- `GET /zenInvoice/api/example-bearer/public` - Endpoint công khai
- `GET /zenInvoice/api/example-bearer/protected` - Endpoint được bảo vệ
- `GET /zenInvoice/api/example-bearer/profile` - Lấy thông tin profile user
- `PUT /zenInvoice/api/example-bearer/profile` - Cập nhật profile user

## Cách sử dụng

### 1. Tạo Controller với Bearer Token Auth

```csharp
[ApiController]
[Route("zenInvoice/api/[controller]")]
public class MyController : BearerAuthBaseController
{
    public MyController(ICurrentUserService currentUserService) 
        : base(currentUserService)
    {
    }

    [HttpGet("data")]
    [BearerTokenAuth]
    public async Task<IActionResult> GetData()
    {
        // Kiểm tra xác thực
        if (!IsAuthenticated)
            return Unauthorized();

        // Lấy thông tin user hiện tại
        var partnerId = CurrentPartnerId;
        var partnerName = CurrentPartnerName;
        var partner = await GetCurrentPartnerAsync();

        return Ok(new { partnerId, partnerName, partner });
    }
}
```

### 2. Sử dụng CurrentUserService trong Service khác

```csharp
public class MyBusinessService
{
    private readonly ICurrentUserService _currentUserService;

    public MyBusinessService(ICurrentUserService currentUserService)
    {
        _currentUserService = currentUserService;
    }

    public async Task<string> DoSomething()
    {
        var partnerId = _currentUserService.GetCurrentPartnerId();
        if (partnerId == null)
            throw new UnauthorizedAccessException();

        var partner = await _currentUserService.GetCurrentPartnerAsync();
        return $"Hello {partner?.Name}";
    }
}
```

### 3. Sử dụng CurrentUserService trong Command Handlers (MediatR)

`ICurrentUserService` hoạt động với **tất cả các loại authentication** (B2B full security, Bearer Token, etc.) vì nó lấy thông tin từ HttpContext:

```csharp
public class CreateInvoiceCommandHandler(
    IApplicationDbContext dbContext,
    ICurrentUserService currentUserService,  // ← Inject service
    ILogger<CreateInvoiceCommandHandler> logger)
    : IRequestHandler<CreateInvoiceCommand, Response<CreateInvoiceResponse>>
{
    public async Task<Response<CreateInvoiceResponse>> Handle(
        CreateInvoiceCommand request,
        CancellationToken cancellationToken)
    {
        // Lấy current user ID để ghi audit log
        var currentUserId = currentUserService.GetCurrentPartnerId() ?? Guid.Empty;
        
        var newInvoice = new InvoiceInfo
        {
            // ... other properties
            CreatedBy = currentUserId,  // ← Sử dụng current user
            CreatedAt = DateTime.UtcNow
        };

        // Khi update entity khác
        merchantInvoiceOrder.UpdatedBy = currentUserId;  // ← Audit trail
        merchantInvoiceOrder.UpdatedAt = DateTime.UtcNow;

        await dbContext.SaveChangesAsync(cancellationToken);
        return new Response<CreateInvoiceResponse>(response);
    }
}
```

**Lưu ý quan trọng:**
- `ICurrentUserService` hoạt động với **mọi loại authentication** (B2B, Bearer Token, etc.)
- Service tự động lấy thông tin từ HttpContext được thiết lập bởi middleware
- Không cần quan tâm controller sử dụng authentication nào
- Phù hợp cho audit logging, authorization checks trong business logic

## Cấu hình

### Program.cs

Service đã được đăng ký trong `Program.cs`:

```csharp
// Current User Service
builder.Services.AddScoped<Applications.Interfaces.Services.ICurrentUserService, Infrastructure.Services.CurrentUserService>();
```

### Skip Authentication cho Public Endpoints

Trong `Program.cs`, các endpoint public được cấu hình để bỏ qua xác thực:

```csharp
options.SkipAuthenticationPaths = [
    // ... other paths
    "/zeninvoice/api/example-bearer/public" // ✅ Skip authentication for public example endpoint
];
```

## So sánh với Full Security Authentication

| Tính năng | Bearer Token Auth | Full Security Auth |
|-----------|-------------------|-------------------|
| Bearer Token Validation | ✅ | ✅ |
| HMAC Signature | ❌ | ✅ |
| IP Whitelist | ❌ | ✅ |
| Usage Tracking | ❌ | ✅ |
| Permission Authorization | ❌ | ✅ |
| Timestamp Validation | ❌ | ✅ |

## Testing

### 1. Lấy Token

Sử dụng endpoint authentication hiện có để lấy token:

```bash
POST /api/authentication/token
{
    "clientId": "your-client-id",
    "clientSecret": "your-client-secret"
}
```

### 2. Sử dụng Token

```bash
# Public endpoint (không cần token)
GET /zenInvoice/api/example-bearer/public

# Protected endpoint (cần token)
GET /zenInvoice/api/example-bearer/protected
Authorization: Bearer your-jwt-token-here

# Get user profile
GET /zenInvoice/api/example-bearer/profile
Authorization: Bearer your-jwt-token-here
```

## Lưu ý quan trọng

1. **Bảo mật:** Bearer Token Auth chỉ kiểm tra token, không có các lớp bảo mật khác
2. **Performance:** Nhanh hơn Full Security Auth vì ít middleware hơn
3. **Use Cases:** Phù hợp cho các endpoint internal hoặc ít nhạy cảm
4. **Token Management:** Vẫn sử dụng hệ thống token management hiện có
5. **Logging:** Tất cả các hoạt động đều được log để audit

## Troubleshooting

### Token không hợp lệ
- Kiểm tra format: `Bearer <token>`
- Kiểm tra token chưa hết hạn
- Kiểm tra partner vẫn active

### Service không inject được
- Đảm bảo đã đăng ký service trong Program.cs
- Kiểm tra namespace import

### Endpoint vẫn bị middleware khác chặn
- Thêm path vào `SkipAuthenticationPaths` trong Program.cs
- Kiểm tra thứ tự middleware trong pipeline

## Kết luận

Hệ thống Bearer Token Authentication đã được triển khai thành công với:
- ✅ Attribute xác thực đơn giản
- ✅ Service quản lý current user
- ✅ Base controller tiện ích
- ✅ Example controller minh họa
- ✅ Cấu hình middleware
- ✅ Documentation đầy đủ

Hệ thống sẵn sàng sử dụng cho các endpoint cần xác thực Bearer Token đơn giản.