﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdateInvoiceSchema : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_MerchantBranchInvoiceAccounts_MerchantBranchId",
                table: "MerchantBranchInvoiceAccounts");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_MerchantBranchInvoiceAccounts_MerchantBranchId",
                table: "MerchantBranchInvoiceAccounts",
                column: "MerchantBranchId");

            migrationBuilder.CreateTable(
                name: "MerchantInvoiceOrders",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    MerchantBranchId = table.Column<Guid>(type: "uuid", nullable: false),
                    TotalInvoiceQuantity = table.Column<int>(type: "integer", nullable: false),
                    RemainingInvoiceQuantity = table.Column<int>(type: "integer", nullable: false),
                    EffectiveDateFrom = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EffectiveDateTo = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    OrderReference = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IpAddress = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true),
                    TraceId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MerchantInvoiceOrders", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MerchantInvoiceOrders_MerchantBranchInvoiceAccounts_MerchantBranchId",
                        column: x => x.MerchantBranchId,
                        principalTable: "MerchantBranchInvoiceAccounts",
                        principalColumn: "MerchantBranchId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "InvoiceInfos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    InvoiceId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CqtInvoiceStatus = table.Column<string>(type: "text", nullable: false),
                    InvoiceStatus = table.Column<int>(type: "integer", nullable: false),
                    RequestData = table.Column<string>(type: "jsonb", nullable: false),
                    ResponseData = table.Column<string>(type: "jsonb", nullable: false),
                    MerchantInvoiceOrderId = table.Column<Guid>(type: "uuid", nullable: false),
                    InvoiceNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    InvoiceSeries = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    TotalAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    TaxAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    CustomerName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    CustomerTaxCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    InvoiceDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    SignedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    SentToCqtDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ErrorMessage = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    MerchantBranchInvoiceAccountId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IpAddress = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true),
                    TraceId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InvoiceInfos", x => x.Id);
                    table.ForeignKey(
                        name: "FK_InvoiceInfos_MerchantBranchInvoiceAccounts_MerchantBranchIn~",
                        column: x => x.MerchantBranchInvoiceAccountId,
                        principalTable: "MerchantBranchInvoiceAccounts",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_InvoiceInfos_MerchantInvoiceOrders_MerchantInvoiceOrderId",
                        column: x => x.MerchantInvoiceOrderId,
                        principalTable: "MerchantInvoiceOrders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_MerchantBranchInvoiceAccounts_MerchantBranchId_Unique",
                table: "MerchantBranchInvoiceAccounts",
                column: "MerchantBranchId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_InvoiceInfos_CqtInvoiceStatus",
                table: "InvoiceInfos",
                column: "CqtInvoiceStatus");

            migrationBuilder.CreateIndex(
                name: "IX_InvoiceInfos_CreatedAt",
                table: "InvoiceInfos",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_InvoiceInfos_CustomerTaxCode",
                table: "InvoiceInfos",
                column: "CustomerTaxCode");

            migrationBuilder.CreateIndex(
                name: "IX_InvoiceInfos_InvoiceDate",
                table: "InvoiceInfos",
                column: "InvoiceDate");

            migrationBuilder.CreateIndex(
                name: "IX_InvoiceInfos_InvoiceId_Unique",
                table: "InvoiceInfos",
                column: "InvoiceId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_InvoiceInfos_InvoiceNumber",
                table: "InvoiceInfos",
                column: "InvoiceNumber");

            migrationBuilder.CreateIndex(
                name: "IX_InvoiceInfos_InvoiceStatus",
                table: "InvoiceInfos",
                column: "InvoiceStatus");

            migrationBuilder.CreateIndex(
                name: "IX_InvoiceInfos_MerchantBranchInvoiceAccountId",
                table: "InvoiceInfos",
                column: "MerchantBranchInvoiceAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_InvoiceInfos_MerchantInvoiceOrderId",
                table: "InvoiceInfos",
                column: "MerchantInvoiceOrderId");

            migrationBuilder.CreateIndex(
                name: "IX_InvoiceInfos_Order_CreatedAt",
                table: "InvoiceInfos",
                columns: new[] { "MerchantInvoiceOrderId", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_MerchantInvoiceOrders_ActiveWithAvailable",
                table: "MerchantInvoiceOrders",
                columns: new[] { "IsActive", "RemainingInvoiceQuantity", "EffectiveDateFrom", "EffectiveDateTo" });

            migrationBuilder.CreateIndex(
                name: "IX_MerchantInvoiceOrders_DateRange",
                table: "MerchantInvoiceOrders",
                columns: new[] { "EffectiveDateFrom", "EffectiveDateTo" });

            migrationBuilder.CreateIndex(
                name: "IX_MerchantInvoiceOrders_IsActive",
                table: "MerchantInvoiceOrders",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_MerchantInvoiceOrders_MerchantBranchId",
                table: "MerchantInvoiceOrders",
                column: "MerchantBranchId");

            migrationBuilder.CreateIndex(
                name: "IX_MerchantInvoiceOrders_OrderReference",
                table: "MerchantInvoiceOrders",
                column: "OrderReference");

            migrationBuilder.CreateIndex(
                name: "IX_MerchantInvoiceOrders_RemainingQuantity",
                table: "MerchantInvoiceOrders",
                column: "RemainingInvoiceQuantity");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "InvoiceInfos");

            migrationBuilder.DropTable(
                name: "MerchantInvoiceOrders");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_MerchantBranchInvoiceAccounts_MerchantBranchId",
                table: "MerchantBranchInvoiceAccounts");

            migrationBuilder.DropIndex(
                name: "IX_MerchantBranchInvoiceAccounts_MerchantBranchId_Unique",
                table: "MerchantBranchInvoiceAccounts");

            migrationBuilder.CreateIndex(
                name: "IX_MerchantBranchInvoiceAccounts_MerchantBranchId",
                table: "MerchantBranchInvoiceAccounts",
                column: "MerchantBranchId");
        }
    }
}
