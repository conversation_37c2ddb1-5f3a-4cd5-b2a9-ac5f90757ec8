﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class MakeMerchantBranchIdNullableConfig : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MerchantInvoiceOrders_MerchantBranchInvoiceAccounts_MerchantBranchId",
                table: "MerchantInvoiceOrders");

            migrationBuilder.AlterColumn<Guid>(
                name: "MerchantBranchId",
                table: "MerchantInvoiceOrders",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AddForeignKey(
                name: "FK_MerchantInvoiceOrders_MerchantBranchInvoiceAccounts_MerchantBranchId",
                table: "MerchantInvoiceOrders",
                column: "MerchantBranchId",
                principalTable: "MerchantBranchInvoiceAccounts",
                principalColumn: "MerchantBranchId",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MerchantInvoiceOrders_MerchantBranchInvoiceAccounts_MerchantBranchId",
                table: "MerchantInvoiceOrders");

            migrationBuilder.AlterColumn<Guid>(
                name: "MerchantBranchId",
                table: "MerchantInvoiceOrders",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("********-0000-0000-0000-************"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_MerchantInvoiceOrders_MerchantBranchInvoiceAccounts_MerchantBranchId",
                table: "MerchantInvoiceOrders",
                column: "MerchantBranchId",
                principalTable: "MerchantBranchInvoiceAccounts",
                principalColumn: "MerchantBranchId",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
