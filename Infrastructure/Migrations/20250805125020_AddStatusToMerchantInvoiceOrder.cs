﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddStatusToMerchantInvoiceOrder : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Status",
                table: "MerchantInvoiceOrders",
                type: "text",
                nullable: true,
                defaultValue: "PENDING");

            migrationBuilder.CreateIndex(
                name: "IX_MerchantInvoiceOrders_Status",
                table: "MerchantInvoiceOrders",
                column: "Status");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_MerchantInvoiceOrders_Status",
                table: "MerchantInvoiceOrders");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "MerchantInvoiceOrders");
        }
    }
}
