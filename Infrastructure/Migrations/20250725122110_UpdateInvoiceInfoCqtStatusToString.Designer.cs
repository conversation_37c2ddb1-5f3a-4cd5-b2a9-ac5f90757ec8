﻿// <auto-generated />
using System;
using Infrastructure.Persistences;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Infrastructure.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20250725122110_UpdateInvoiceInfoCqtStatusToString")]
    partial class UpdateInvoiceInfoCqtStatusToString
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Core.Entities.Authentication.AuthenticationLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AuthenticationMethod")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ClientId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("FailureReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("HttpMethod")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("IpAddress")
                        .IsRequired()
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Metadata")
                        .HasColumnType("text");

                    b.Property<Guid?>("PartnerId")
                        .HasColumnType("uuid");

                    b.Property<string>("RequestPath")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("Success")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("TokenId")
                        .HasColumnType("uuid");

                    b.Property<string>("TraceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.HasKey("Id");

                    b.HasIndex("AuthenticationMethod")
                        .HasDatabaseName("IX_AuthenticationLogs_AuthenticationMethod");

                    b.HasIndex("ClientId")
                        .HasDatabaseName("IX_AuthenticationLogs_ClientId");

                    b.HasIndex("IpAddress")
                        .HasDatabaseName("IX_AuthenticationLogs_IpAddress");

                    b.HasIndex("PartnerId")
                        .HasDatabaseName("IX_AuthenticationLogs_PartnerId");

                    b.HasIndex("Success")
                        .HasDatabaseName("IX_AuthenticationLogs_Success");

                    b.HasIndex("Timestamp")
                        .HasDatabaseName("IX_AuthenticationLogs_Timestamp");

                    b.HasIndex("TokenId");

                    b.HasIndex("IpAddress", "Timestamp")
                        .HasDatabaseName("IX_AuthenticationLogs_IpAddress_Timestamp");

                    b.HasIndex("PartnerId", "Timestamp")
                        .HasDatabaseName("IX_AuthenticationLogs_PartnerId_Timestamp");

                    b.HasIndex("Success", "Timestamp")
                        .HasDatabaseName("IX_AuthenticationLogs_Success_Timestamp");

                    b.ToTable("AuthenticationLogs", (string)null);
                });

            modelBuilder.Entity("Core.Entities.Authentication.Function", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Metadata")
                        .HasColumnType("text");

                    b.Property<string>("Module")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("TraceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("IX_Functions_Code");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_Functions_IsActive");

                    b.HasIndex("Module")
                        .HasDatabaseName("IX_Functions_Module");

                    b.ToTable("Functions", (string)null);
                });

            modelBuilder.Entity("Core.Entities.Authentication.FunctionPermission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Constraints")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<Guid>("FunctionId")
                        .HasColumnType("uuid");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRequired")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<Guid>("PermissionId")
                        .HasColumnType("uuid");

                    b.Property<string>("TraceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("FunctionId")
                        .HasDatabaseName("IX_FunctionPermissions_FunctionId");

                    b.HasIndex("PermissionId")
                        .HasDatabaseName("IX_FunctionPermissions_PermissionId");

                    b.HasIndex("FunctionId", "PermissionId")
                        .IsUnique()
                        .HasDatabaseName("IX_FunctionPermissions_FunctionId_PermissionId");

                    b.ToTable("FunctionPermissions", (string)null);
                });

            modelBuilder.Entity("Core.Entities.Authentication.Partner", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("ApiRateLimitPerHour")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1000);

                    b.Property<string>("ClientId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ClientSecretHash")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ContactEmail")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("ContactPhone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<decimal>("CurrentMonthUsage")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("EnableIpWhitelist")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<string>("HmacSecretHash")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<string>("IpWhitelist")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<decimal>("MonthlyInvoiceLimit")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(10000m);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("TraceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ClientId")
                        .IsUnique()
                        .HasDatabaseName("IX_Partners_ClientId");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_Partners_IsActive");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_Partners_Name");

                    b.ToTable("Partners", (string)null);
                });

            modelBuilder.Entity("Core.Entities.Authentication.PartnerConstraint", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ConstraintType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ConstraintValue")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("PartnerId")
                        .HasColumnType("uuid");

                    b.Property<int>("Priority")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<Guid?>("SetBy")
                        .HasColumnType("uuid");

                    b.Property<string>("TraceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("ValidFrom")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ValidTo")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("ConstraintType")
                        .HasDatabaseName("IX_PartnerConstraints_ConstraintType");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_PartnerConstraints_IsActive");

                    b.HasIndex("PartnerId")
                        .HasDatabaseName("IX_PartnerConstraints_PartnerId");

                    b.HasIndex("Priority")
                        .HasDatabaseName("IX_PartnerConstraints_Priority");

                    b.HasIndex("ValidFrom")
                        .HasDatabaseName("IX_PartnerConstraints_ValidFrom");

                    b.HasIndex("ValidTo")
                        .HasDatabaseName("IX_PartnerConstraints_ValidTo");

                    b.HasIndex("PartnerId", "ConstraintType", "IsActive")
                        .HasDatabaseName("IX_PartnerConstraints_PartnerId_ConstraintType_IsActive");

                    b.HasIndex("PartnerId", "ValidFrom", "ValidTo")
                        .HasDatabaseName("IX_PartnerConstraints_PartnerId_ValidityPeriod");

                    b.ToTable("PartnerConstraints", (string)null);
                });

            modelBuilder.Entity("Core.Entities.Authentication.PartnerFunctionPermission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Constraints")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("FunctionId")
                        .HasColumnType("uuid");

                    b.Property<string>("GrantReason")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<Guid?>("GrantedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsGranted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<Guid>("PartnerId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("PermissionId")
                        .HasColumnType("uuid");

                    b.Property<string>("TraceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ExpiresAt")
                        .HasDatabaseName("IX_PartnerFunctionPermissions_ExpiresAt");

                    b.HasIndex("FunctionId")
                        .HasDatabaseName("IX_PartnerFunctionPermissions_FunctionId");

                    b.HasIndex("PartnerId")
                        .HasDatabaseName("IX_PartnerFunctionPermissions_PartnerId");

                    b.HasIndex("PermissionId")
                        .HasDatabaseName("IX_PartnerFunctionPermissions_PermissionId");

                    b.HasIndex("PartnerId", "IsGranted")
                        .HasDatabaseName("IX_PartnerFunctionPermissions_PartnerId_IsGranted");

                    b.HasIndex("PartnerId", "FunctionId", "PermissionId")
                        .IsUnique()
                        .HasDatabaseName("IX_PartnerFunctionPermissions_PartnerId_FunctionId_PermissionId");

                    b.ToTable("PartnerFunctionPermissions", (string)null);
                });

            modelBuilder.Entity("Core.Entities.Authentication.PartnerRole", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<int>("DefaultApiRateLimitPerHour")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1000);

                    b.Property<decimal>("DefaultMonthlyInvoiceLimit")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(10000m);

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Metadata")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("Priority")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<string>("TraceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("IX_PartnerRoles_Code");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_PartnerRoles_IsActive");

                    b.HasIndex("Priority")
                        .HasDatabaseName("IX_PartnerRoles_Priority");

                    b.ToTable("PartnerRoles", (string)null);
                });

            modelBuilder.Entity("Core.Entities.Authentication.PartnerRoleAssignment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("AssignedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("AssignedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("AssignmentReason")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("PartnerId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid");

                    b.Property<string>("TraceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("AssignedAt")
                        .HasDatabaseName("IX_PartnerRoleAssignments_AssignedAt");

                    b.HasIndex("ExpiresAt")
                        .HasDatabaseName("IX_PartnerRoleAssignments_ExpiresAt");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_PartnerRoleAssignments_IsActive");

                    b.HasIndex("PartnerId")
                        .HasDatabaseName("IX_PartnerRoleAssignments_PartnerId");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("IX_PartnerRoleAssignments_RoleId");

                    b.HasIndex("PartnerId", "RoleId", "IsActive")
                        .HasDatabaseName("IX_PartnerRoleAssignments_PartnerId_RoleId_IsActive");

                    b.ToTable("PartnerRoleAssignments", (string)null);
                });

            modelBuilder.Entity("Core.Entities.Authentication.PartnerToken", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AccessToken")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("ExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("IssuedFromIp")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<string>("IssuedFromUserAgent")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<Guid>("PartnerId")
                        .HasColumnType("uuid");

                    b.Property<string>("RevocationReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("RevokedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Scope")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("TraceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("AccessToken")
                        .IsUnique()
                        .HasDatabaseName("IX_PartnerTokens_AccessToken");

                    b.HasIndex("ExpiresAt")
                        .HasDatabaseName("IX_PartnerTokens_ExpiresAt");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_PartnerTokens_IsActive");

                    b.HasIndex("PartnerId")
                        .HasDatabaseName("IX_PartnerTokens_PartnerId");

                    b.ToTable("PartnerTokens", (string)null);
                });

            modelBuilder.Entity("Core.Entities.Authentication.PartnerUsage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AdditionalMetrics")
                        .HasColumnType("text");

                    b.Property<long>("ApiCallsCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasDefaultValue(0L);

                    b.Property<double>("AverageResponseTimeMs")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("double precision")
                        .HasDefaultValue(0.0);

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<long>("DataTransferBytes")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasDefaultValue(0L);

                    b.Property<long>("FailedApiCalls")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasDefaultValue(0L);

                    b.Property<int>("InvoicesPurchased")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastUpdated")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("PartnerId")
                        .HasColumnType("uuid");

                    b.Property<int>("PeakRequestsPerHour")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<string>("Period")
                        .IsRequired()
                        .HasMaxLength(7)
                        .HasColumnType("character varying(7)");

                    b.Property<long>("SuccessfulApiCalls")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasDefaultValue(0L);

                    b.Property<decimal>("TotalAmount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<string>("TraceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("LastUpdated")
                        .HasDatabaseName("IX_PartnerUsages_LastUpdated");

                    b.HasIndex("PartnerId")
                        .HasDatabaseName("IX_PartnerUsages_PartnerId");

                    b.HasIndex("Period")
                        .HasDatabaseName("IX_PartnerUsages_Period");

                    b.HasIndex("PartnerId", "Period")
                        .IsUnique()
                        .HasDatabaseName("IX_PartnerUsages_PartnerId_Period");

                    b.HasIndex("Period", "TotalAmount")
                        .HasDatabaseName("IX_PartnerUsages_Period_TotalAmount");

                    b.HasIndex("PartnerId", "Period", "LastUpdated")
                        .HasDatabaseName("IX_PartnerUsages_PartnerId_Period_LastUpdated");

                    b.ToTable("PartnerUsages", (string)null);
                });

            modelBuilder.Entity("Core.Entities.Authentication.Permission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("TraceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("IX_Permissions_Code");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_Permissions_IsActive");

                    b.ToTable("Permissions", (string)null);
                });

            modelBuilder.Entity("Core.Entities.Authentication.RoleFunctionPermission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Constraints")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("FunctionId")
                        .HasColumnType("uuid");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsGranted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<Guid>("PermissionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid");

                    b.Property<string>("TraceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ExpiresAt")
                        .HasDatabaseName("IX_RoleFunctionPermissions_ExpiresAt");

                    b.HasIndex("FunctionId")
                        .HasDatabaseName("IX_RoleFunctionPermissions_FunctionId");

                    b.HasIndex("PermissionId")
                        .HasDatabaseName("IX_RoleFunctionPermissions_PermissionId");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("IX_RoleFunctionPermissions_RoleId");

                    b.HasIndex("RoleId", "FunctionId", "PermissionId")
                        .IsUnique()
                        .HasDatabaseName("IX_RoleFunctionPermissions_RoleId_FunctionId_PermissionId");

                    b.ToTable("RoleFunctionPermissions", (string)null);
                });

            modelBuilder.Entity("Core.Entities.ClientCredential", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ClientId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ClientSecretHash")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Role")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TraceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("ClientCredentials");
                });

            modelBuilder.Entity("Core.Entities.InvoiceInfo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CqtInvoiceStatus")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("CustomerName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("CustomerTaxCode")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("InvoiceDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("InvoiceId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("InvoiceNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("InvoiceSeries")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("InvoiceStatus")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("MerchantBranchInvoiceAccountId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("MerchantInvoiceOrderId")
                        .HasColumnType("uuid");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("RequestData")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<string>("ResponseData")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<DateTime?>("SentToCqtDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("SignedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal?>("TaxAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("numeric(18,2)");

                    b.Property<decimal?>("TotalAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("numeric(18,2)");

                    b.Property<string>("TraceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CqtInvoiceStatus")
                        .HasDatabaseName("IX_InvoiceInfos_CqtInvoiceStatus");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_InvoiceInfos_CreatedAt");

                    b.HasIndex("CustomerTaxCode")
                        .HasDatabaseName("IX_InvoiceInfos_CustomerTaxCode");

                    b.HasIndex("InvoiceDate")
                        .HasDatabaseName("IX_InvoiceInfos_InvoiceDate");

                    b.HasIndex("InvoiceId")
                        .IsUnique()
                        .HasDatabaseName("IX_InvoiceInfos_InvoiceId_Unique");

                    b.HasIndex("InvoiceNumber")
                        .HasDatabaseName("IX_InvoiceInfos_InvoiceNumber");

                    b.HasIndex("InvoiceStatus")
                        .HasDatabaseName("IX_InvoiceInfos_InvoiceStatus");

                    b.HasIndex("MerchantBranchInvoiceAccountId");

                    b.HasIndex("MerchantInvoiceOrderId")
                        .HasDatabaseName("IX_InvoiceInfos_MerchantInvoiceOrderId");

                    b.HasIndex("MerchantInvoiceOrderId", "CreatedAt")
                        .HasDatabaseName("IX_InvoiceInfos_Order_CreatedAt");

                    b.ToTable("InvoiceInfos", (string)null);
                });

            modelBuilder.Entity("Core.Entities.MerchantBranchInvoiceAccount", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("EffectiveDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("ExpirationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("InvoiceAccountPassword")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("InvoiceAccountProvider")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasDefaultValue("MBF");

                    b.Property<string>("InvoiceAccountUserName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("MerchantBranchId")
                        .HasColumnType("uuid");

                    b.Property<string>("MerchantBranchName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<Guid>("PartnerId")
                        .HasColumnType("uuid");

                    b.Property<string>("TaxNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("TraceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_MerchantBranchInvoiceAccounts_IsActive");

                    b.HasIndex("MerchantBranchId")
                        .IsUnique()
                        .HasDatabaseName("IX_MerchantBranchInvoiceAccounts_MerchantBranchId_Unique");

                    b.HasIndex("PartnerId")
                        .HasDatabaseName("IX_MerchantBranchInvoiceAccounts_PartnerId");

                    b.HasIndex("TaxNumber")
                        .HasDatabaseName("IX_MerchantBranchInvoiceAccounts_TaxNumber");

                    b.HasIndex("EffectiveDate", "ExpirationDate")
                        .HasDatabaseName("IX_MerchantBranchInvoiceAccounts_DateRange");

                    b.ToTable("MerchantBranchInvoiceAccounts", (string)null);
                });

            modelBuilder.Entity("Core.Entities.MerchantInvoiceOrder", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("EffectiveDateFrom")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("EffectiveDateTo")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("MerchantBranchId")
                        .HasColumnType("uuid");

                    b.Property<string>("OrderReference")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("RemainingInvoiceQuantity")
                        .HasColumnType("integer");

                    b.Property<int>("TotalInvoiceQuantity")
                        .HasColumnType("integer");

                    b.Property<string>("TraceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_MerchantInvoiceOrders_IsActive");

                    b.HasIndex("MerchantBranchId")
                        .HasDatabaseName("IX_MerchantInvoiceOrders_MerchantBranchId");

                    b.HasIndex("OrderReference")
                        .HasDatabaseName("IX_MerchantInvoiceOrders_OrderReference");

                    b.HasIndex("RemainingInvoiceQuantity")
                        .HasDatabaseName("IX_MerchantInvoiceOrders_RemainingQuantity");

                    b.HasIndex("EffectiveDateFrom", "EffectiveDateTo")
                        .HasDatabaseName("IX_MerchantInvoiceOrders_DateRange");

                    b.HasIndex("IsActive", "RemainingInvoiceQuantity", "EffectiveDateFrom", "EffectiveDateTo")
                        .HasDatabaseName("IX_MerchantInvoiceOrders_ActiveWithAvailable");

                    b.ToTable("MerchantInvoiceOrders", (string)null);
                });

            modelBuilder.Entity("Core.Entities.SmsLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("BaseUrl")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastRetryAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Provider")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RawResponse")
                        .HasColumnType("text");

                    b.Property<string>("RequestPayload")
                        .HasColumnType("text");

                    b.Property<long>("ResponseTimeMs")
                        .HasColumnType("bigint");

                    b.Property<int>("RetryCount")
                        .HasColumnType("integer");

                    b.Property<string>("SendSMSPath")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("SentAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("StatusCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("StatusDetail")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TraceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("TransactionId")
                        .HasColumnType("text");

                    b.Property<string>("UnicodeType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("UseCase")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("SmsLogs");
                });

            modelBuilder.Entity("Core.Entities.SmsRetryQueue", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDone")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastTriedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("RetryCount")
                        .HasColumnType("integer");

                    b.Property<Guid>("SmsLogId")
                        .HasColumnType("uuid");

                    b.Property<string>("TraceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("SmsLogId");

                    b.ToTable("SmsRetryQueues");
                });

            modelBuilder.Entity("Core.Entities.Authentication.AuthenticationLog", b =>
                {
                    b.HasOne("Core.Entities.Authentication.Partner", "Partner")
                        .WithMany("AuthenticationLogs")
                        .HasForeignKey("PartnerId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Core.Entities.Authentication.PartnerToken", "Token")
                        .WithMany()
                        .HasForeignKey("TokenId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Partner");

                    b.Navigation("Token");
                });

            modelBuilder.Entity("Core.Entities.Authentication.FunctionPermission", b =>
                {
                    b.HasOne("Core.Entities.Authentication.Function", "Function")
                        .WithMany("FunctionPermissions")
                        .HasForeignKey("FunctionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Core.Entities.Authentication.Permission", "Permission")
                        .WithMany("FunctionPermissions")
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Function");

                    b.Navigation("Permission");
                });

            modelBuilder.Entity("Core.Entities.Authentication.PartnerConstraint", b =>
                {
                    b.HasOne("Core.Entities.Authentication.Partner", "Partner")
                        .WithMany("PartnerConstraints")
                        .HasForeignKey("PartnerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Partner");
                });

            modelBuilder.Entity("Core.Entities.Authentication.PartnerFunctionPermission", b =>
                {
                    b.HasOne("Core.Entities.Authentication.Function", "Function")
                        .WithMany("PartnerFunctionPermissions")
                        .HasForeignKey("FunctionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Core.Entities.Authentication.Partner", "Partner")
                        .WithMany("FunctionPermissions")
                        .HasForeignKey("PartnerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Core.Entities.Authentication.Permission", "Permission")
                        .WithMany("PartnerFunctionPermissions")
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Function");

                    b.Navigation("Partner");

                    b.Navigation("Permission");
                });

            modelBuilder.Entity("Core.Entities.Authentication.PartnerRoleAssignment", b =>
                {
                    b.HasOne("Core.Entities.Authentication.Partner", "Partner")
                        .WithMany("RoleAssignments")
                        .HasForeignKey("PartnerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Core.Entities.Authentication.PartnerRole", "Role")
                        .WithMany("PartnerRoleAssignments")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Partner");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("Core.Entities.Authentication.PartnerToken", b =>
                {
                    b.HasOne("Core.Entities.Authentication.Partner", "Partner")
                        .WithMany("PartnerTokens")
                        .HasForeignKey("PartnerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Partner");
                });

            modelBuilder.Entity("Core.Entities.Authentication.PartnerUsage", b =>
                {
                    b.HasOne("Core.Entities.Authentication.Partner", "Partner")
                        .WithMany("PartnerUsages")
                        .HasForeignKey("PartnerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Partner");
                });

            modelBuilder.Entity("Core.Entities.Authentication.RoleFunctionPermission", b =>
                {
                    b.HasOne("Core.Entities.Authentication.Function", "Function")
                        .WithMany("RoleFunctionPermissions")
                        .HasForeignKey("FunctionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Core.Entities.Authentication.Permission", "Permission")
                        .WithMany("RoleFunctionPermissions")
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Core.Entities.Authentication.PartnerRole", "Role")
                        .WithMany("RoleFunctionPermissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Function");

                    b.Navigation("Permission");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("Core.Entities.InvoiceInfo", b =>
                {
                    b.HasOne("Core.Entities.MerchantBranchInvoiceAccount", null)
                        .WithMany("InvoiceInfos")
                        .HasForeignKey("MerchantBranchInvoiceAccountId");

                    b.HasOne("Core.Entities.MerchantInvoiceOrder", "MerchantInvoiceOrder")
                        .WithMany("InvoiceInfos")
                        .HasForeignKey("MerchantInvoiceOrderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_InvoiceInfos_MerchantInvoiceOrders_MerchantInvoiceOrderId");

                    b.Navigation("MerchantInvoiceOrder");
                });

            modelBuilder.Entity("Core.Entities.MerchantBranchInvoiceAccount", b =>
                {
                    b.HasOne("Core.Entities.Authentication.Partner", "Partner")
                        .WithMany()
                        .HasForeignKey("PartnerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_MerchantBranchInvoiceAccounts_Partners_PartnerId");

                    b.Navigation("Partner");
                });

            modelBuilder.Entity("Core.Entities.MerchantInvoiceOrder", b =>
                {
                    b.HasOne("Core.Entities.MerchantBranchInvoiceAccount", "MerchantBranchInvoiceAccount")
                        .WithMany("MerchantInvoiceOrders")
                        .HasForeignKey("MerchantBranchId")
                        .HasPrincipalKey("MerchantBranchId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_MerchantInvoiceOrders_MerchantBranchInvoiceAccounts_MerchantBranchId");

                    b.Navigation("MerchantBranchInvoiceAccount");
                });

            modelBuilder.Entity("Core.Entities.SmsRetryQueue", b =>
                {
                    b.HasOne("Core.Entities.SmsLog", "SmsLog")
                        .WithMany()
                        .HasForeignKey("SmsLogId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SmsLog");
                });

            modelBuilder.Entity("Core.Entities.Authentication.Function", b =>
                {
                    b.Navigation("FunctionPermissions");

                    b.Navigation("PartnerFunctionPermissions");

                    b.Navigation("RoleFunctionPermissions");
                });

            modelBuilder.Entity("Core.Entities.Authentication.Partner", b =>
                {
                    b.Navigation("AuthenticationLogs");

                    b.Navigation("FunctionPermissions");

                    b.Navigation("PartnerConstraints");

                    b.Navigation("PartnerTokens");

                    b.Navigation("PartnerUsages");

                    b.Navigation("RoleAssignments");
                });

            modelBuilder.Entity("Core.Entities.Authentication.PartnerRole", b =>
                {
                    b.Navigation("PartnerRoleAssignments");

                    b.Navigation("RoleFunctionPermissions");
                });

            modelBuilder.Entity("Core.Entities.Authentication.Permission", b =>
                {
                    b.Navigation("FunctionPermissions");

                    b.Navigation("PartnerFunctionPermissions");

                    b.Navigation("RoleFunctionPermissions");
                });

            modelBuilder.Entity("Core.Entities.MerchantBranchInvoiceAccount", b =>
                {
                    b.Navigation("InvoiceInfos");

                    b.Navigation("MerchantInvoiceOrders");
                });

            modelBuilder.Entity("Core.Entities.MerchantInvoiceOrder", b =>
                {
                    b.Navigation("InvoiceInfos");
                });
#pragma warning restore 612, 618
        }
    }
}
