﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddAuthenticationSystem : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Functions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Code = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Module = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    DisplayOrder = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    Metadata = table.Column<string>(type: "text", nullable: true),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IpAddress = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true),
                    TraceId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Functions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "PartnerRoles",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Code = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Priority = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    DefaultApiRateLimitPerHour = table.Column<int>(type: "integer", nullable: false, defaultValue: 1000),
                    DefaultMonthlyInvoiceLimit = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 10000m),
                    Metadata = table.Column<string>(type: "text", nullable: true),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IpAddress = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true),
                    TraceId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PartnerRoles", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Partners",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ClientId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ClientSecretHash = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    HmacSecretHash = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    ContactEmail = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    ContactPhone = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    IpWhitelist = table.Column<string>(type: "text", nullable: true),
                    EnableIpWhitelist = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    ApiRateLimitPerHour = table.Column<int>(type: "integer", nullable: false, defaultValue: 1000),
                    MonthlyInvoiceLimit = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 10000m),
                    CurrentMonthUsage = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IpAddress = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true),
                    TraceId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Partners", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Permissions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    DisplayOrder = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IpAddress = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true),
                    TraceId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Permissions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "PartnerConstraints",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PartnerId = table.Column<Guid>(type: "uuid", nullable: false),
                    ConstraintType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ConstraintValue = table.Column<string>(type: "text", nullable: false),
                    ValidFrom = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ValidTo = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    Priority = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    SetBy = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IpAddress = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true),
                    TraceId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PartnerConstraints", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PartnerConstraints_Partners_PartnerId",
                        column: x => x.PartnerId,
                        principalTable: "Partners",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PartnerRoleAssignments",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PartnerId = table.Column<Guid>(type: "uuid", nullable: false),
                    RoleId = table.Column<Guid>(type: "uuid", nullable: false),
                    AssignedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ExpiresAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    AssignedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    AssignmentReason = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IpAddress = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true),
                    TraceId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PartnerRoleAssignments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PartnerRoleAssignments_PartnerRoles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "PartnerRoles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PartnerRoleAssignments_Partners_PartnerId",
                        column: x => x.PartnerId,
                        principalTable: "Partners",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PartnerTokens",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PartnerId = table.Column<Guid>(type: "uuid", nullable: false),
                    AccessToken = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    ExpiresAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Scope = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    RevokedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RevocationReason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IssuedFromIp = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true),
                    IssuedFromUserAgent = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IpAddress = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true),
                    TraceId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PartnerTokens", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PartnerTokens_Partners_PartnerId",
                        column: x => x.PartnerId,
                        principalTable: "Partners",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PartnerUsages",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PartnerId = table.Column<Guid>(type: "uuid", nullable: false),
                    Period = table.Column<string>(type: "character varying(7)", maxLength: 7, nullable: false),
                    InvoicesPurchased = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    ApiCallsCount = table.Column<long>(type: "bigint", nullable: false, defaultValue: 0L),
                    SuccessfulApiCalls = table.Column<long>(type: "bigint", nullable: false, defaultValue: 0L),
                    FailedApiCalls = table.Column<long>(type: "bigint", nullable: false, defaultValue: 0L),
                    DataTransferBytes = table.Column<long>(type: "bigint", nullable: false, defaultValue: 0L),
                    PeakRequestsPerHour = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    AverageResponseTimeMs = table.Column<double>(type: "double precision", nullable: false, defaultValue: 0.0),
                    AdditionalMetrics = table.Column<string>(type: "text", nullable: true),
                    LastUpdated = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IpAddress = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true),
                    TraceId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PartnerUsages", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PartnerUsages_Partners_PartnerId",
                        column: x => x.PartnerId,
                        principalTable: "Partners",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "FunctionPermissions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    FunctionId = table.Column<Guid>(type: "uuid", nullable: false),
                    PermissionId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsRequired = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    Constraints = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IpAddress = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true),
                    TraceId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FunctionPermissions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FunctionPermissions_Functions_FunctionId",
                        column: x => x.FunctionId,
                        principalTable: "Functions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_FunctionPermissions_Permissions_PermissionId",
                        column: x => x.PermissionId,
                        principalTable: "Permissions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PartnerFunctionPermissions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PartnerId = table.Column<Guid>(type: "uuid", nullable: false),
                    FunctionId = table.Column<Guid>(type: "uuid", nullable: false),
                    PermissionId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsGranted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    ExpiresAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    GrantedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    GrantReason = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Constraints = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IpAddress = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true),
                    TraceId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PartnerFunctionPermissions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PartnerFunctionPermissions_Functions_FunctionId",
                        column: x => x.FunctionId,
                        principalTable: "Functions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PartnerFunctionPermissions_Partners_PartnerId",
                        column: x => x.PartnerId,
                        principalTable: "Partners",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PartnerFunctionPermissions_Permissions_PermissionId",
                        column: x => x.PermissionId,
                        principalTable: "Permissions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "RoleFunctionPermissions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    RoleId = table.Column<Guid>(type: "uuid", nullable: false),
                    FunctionId = table.Column<Guid>(type: "uuid", nullable: false),
                    PermissionId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsGranted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    ExpiresAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Constraints = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IpAddress = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true),
                    TraceId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RoleFunctionPermissions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RoleFunctionPermissions_Functions_FunctionId",
                        column: x => x.FunctionId,
                        principalTable: "Functions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RoleFunctionPermissions_PartnerRoles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "PartnerRoles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RoleFunctionPermissions_Permissions_PermissionId",
                        column: x => x.PermissionId,
                        principalTable: "Permissions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AuthenticationLogs",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PartnerId = table.Column<Guid>(type: "uuid", nullable: true),
                    ClientId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    IpAddress = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: false),
                    UserAgent = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Success = table.Column<bool>(type: "boolean", nullable: false),
                    FailureReason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    AuthenticationMethod = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Timestamp = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    RequestPath = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    HttpMethod = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    Metadata = table.Column<string>(type: "text", nullable: true),
                    TokenId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    TraceId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AuthenticationLogs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AuthenticationLogs_PartnerTokens_TokenId",
                        column: x => x.TokenId,
                        principalTable: "PartnerTokens",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_AuthenticationLogs_Partners_PartnerId",
                        column: x => x.PartnerId,
                        principalTable: "Partners",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AuthenticationLogs_AuthenticationMethod",
                table: "AuthenticationLogs",
                column: "AuthenticationMethod");

            migrationBuilder.CreateIndex(
                name: "IX_AuthenticationLogs_ClientId",
                table: "AuthenticationLogs",
                column: "ClientId");

            migrationBuilder.CreateIndex(
                name: "IX_AuthenticationLogs_IpAddress",
                table: "AuthenticationLogs",
                column: "IpAddress");

            migrationBuilder.CreateIndex(
                name: "IX_AuthenticationLogs_IpAddress_Timestamp",
                table: "AuthenticationLogs",
                columns: new[] { "IpAddress", "Timestamp" });

            migrationBuilder.CreateIndex(
                name: "IX_AuthenticationLogs_PartnerId",
                table: "AuthenticationLogs",
                column: "PartnerId");

            migrationBuilder.CreateIndex(
                name: "IX_AuthenticationLogs_PartnerId_Timestamp",
                table: "AuthenticationLogs",
                columns: new[] { "PartnerId", "Timestamp" });

            migrationBuilder.CreateIndex(
                name: "IX_AuthenticationLogs_Success",
                table: "AuthenticationLogs",
                column: "Success");

            migrationBuilder.CreateIndex(
                name: "IX_AuthenticationLogs_Success_Timestamp",
                table: "AuthenticationLogs",
                columns: new[] { "Success", "Timestamp" });

            migrationBuilder.CreateIndex(
                name: "IX_AuthenticationLogs_Timestamp",
                table: "AuthenticationLogs",
                column: "Timestamp");

            migrationBuilder.CreateIndex(
                name: "IX_AuthenticationLogs_TokenId",
                table: "AuthenticationLogs",
                column: "TokenId");

            migrationBuilder.CreateIndex(
                name: "IX_FunctionPermissions_FunctionId",
                table: "FunctionPermissions",
                column: "FunctionId");

            migrationBuilder.CreateIndex(
                name: "IX_FunctionPermissions_FunctionId_PermissionId",
                table: "FunctionPermissions",
                columns: new[] { "FunctionId", "PermissionId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FunctionPermissions_PermissionId",
                table: "FunctionPermissions",
                column: "PermissionId");

            migrationBuilder.CreateIndex(
                name: "IX_Functions_Code",
                table: "Functions",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Functions_IsActive",
                table: "Functions",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_Functions_Module",
                table: "Functions",
                column: "Module");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerConstraints_ConstraintType",
                table: "PartnerConstraints",
                column: "ConstraintType");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerConstraints_IsActive",
                table: "PartnerConstraints",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerConstraints_PartnerId",
                table: "PartnerConstraints",
                column: "PartnerId");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerConstraints_PartnerId_ConstraintType_IsActive",
                table: "PartnerConstraints",
                columns: new[] { "PartnerId", "ConstraintType", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_PartnerConstraints_PartnerId_ValidityPeriod",
                table: "PartnerConstraints",
                columns: new[] { "PartnerId", "ValidFrom", "ValidTo" });

            migrationBuilder.CreateIndex(
                name: "IX_PartnerConstraints_Priority",
                table: "PartnerConstraints",
                column: "Priority");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerConstraints_ValidFrom",
                table: "PartnerConstraints",
                column: "ValidFrom");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerConstraints_ValidTo",
                table: "PartnerConstraints",
                column: "ValidTo");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerFunctionPermissions_ExpiresAt",
                table: "PartnerFunctionPermissions",
                column: "ExpiresAt");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerFunctionPermissions_FunctionId",
                table: "PartnerFunctionPermissions",
                column: "FunctionId");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerFunctionPermissions_PartnerId",
                table: "PartnerFunctionPermissions",
                column: "PartnerId");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerFunctionPermissions_PartnerId_FunctionId_PermissionId",
                table: "PartnerFunctionPermissions",
                columns: new[] { "PartnerId", "FunctionId", "PermissionId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PartnerFunctionPermissions_PartnerId_IsGranted",
                table: "PartnerFunctionPermissions",
                columns: new[] { "PartnerId", "IsGranted" });

            migrationBuilder.CreateIndex(
                name: "IX_PartnerFunctionPermissions_PermissionId",
                table: "PartnerFunctionPermissions",
                column: "PermissionId");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerRoleAssignments_AssignedAt",
                table: "PartnerRoleAssignments",
                column: "AssignedAt");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerRoleAssignments_ExpiresAt",
                table: "PartnerRoleAssignments",
                column: "ExpiresAt");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerRoleAssignments_IsActive",
                table: "PartnerRoleAssignments",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerRoleAssignments_PartnerId",
                table: "PartnerRoleAssignments",
                column: "PartnerId");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerRoleAssignments_PartnerId_RoleId_IsActive",
                table: "PartnerRoleAssignments",
                columns: new[] { "PartnerId", "RoleId", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_PartnerRoleAssignments_RoleId",
                table: "PartnerRoleAssignments",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerRoles_Code",
                table: "PartnerRoles",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PartnerRoles_IsActive",
                table: "PartnerRoles",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerRoles_Priority",
                table: "PartnerRoles",
                column: "Priority");

            migrationBuilder.CreateIndex(
                name: "IX_Partners_ClientId",
                table: "Partners",
                column: "ClientId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Partners_IsActive",
                table: "Partners",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_Partners_Name",
                table: "Partners",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerTokens_AccessToken",
                table: "PartnerTokens",
                column: "AccessToken",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PartnerTokens_ExpiresAt",
                table: "PartnerTokens",
                column: "ExpiresAt");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerTokens_IsActive",
                table: "PartnerTokens",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerTokens_PartnerId",
                table: "PartnerTokens",
                column: "PartnerId");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerUsages_LastUpdated",
                table: "PartnerUsages",
                column: "LastUpdated");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerUsages_PartnerId",
                table: "PartnerUsages",
                column: "PartnerId");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerUsages_PartnerId_Period",
                table: "PartnerUsages",
                columns: new[] { "PartnerId", "Period" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PartnerUsages_PartnerId_Period_LastUpdated",
                table: "PartnerUsages",
                columns: new[] { "PartnerId", "Period", "LastUpdated" });

            migrationBuilder.CreateIndex(
                name: "IX_PartnerUsages_Period",
                table: "PartnerUsages",
                column: "Period");

            migrationBuilder.CreateIndex(
                name: "IX_PartnerUsages_Period_TotalAmount",
                table: "PartnerUsages",
                columns: new[] { "Period", "TotalAmount" });

            migrationBuilder.CreateIndex(
                name: "IX_Permissions_Code",
                table: "Permissions",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Permissions_IsActive",
                table: "Permissions",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_RoleFunctionPermissions_ExpiresAt",
                table: "RoleFunctionPermissions",
                column: "ExpiresAt");

            migrationBuilder.CreateIndex(
                name: "IX_RoleFunctionPermissions_FunctionId",
                table: "RoleFunctionPermissions",
                column: "FunctionId");

            migrationBuilder.CreateIndex(
                name: "IX_RoleFunctionPermissions_PermissionId",
                table: "RoleFunctionPermissions",
                column: "PermissionId");

            migrationBuilder.CreateIndex(
                name: "IX_RoleFunctionPermissions_RoleId",
                table: "RoleFunctionPermissions",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_RoleFunctionPermissions_RoleId_FunctionId_PermissionId",
                table: "RoleFunctionPermissions",
                columns: new[] { "RoleId", "FunctionId", "PermissionId" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AuthenticationLogs");

            migrationBuilder.DropTable(
                name: "FunctionPermissions");

            migrationBuilder.DropTable(
                name: "PartnerConstraints");

            migrationBuilder.DropTable(
                name: "PartnerFunctionPermissions");

            migrationBuilder.DropTable(
                name: "PartnerRoleAssignments");

            migrationBuilder.DropTable(
                name: "PartnerUsages");

            migrationBuilder.DropTable(
                name: "RoleFunctionPermissions");

            migrationBuilder.DropTable(
                name: "PartnerTokens");

            migrationBuilder.DropTable(
                name: "Functions");

            migrationBuilder.DropTable(
                name: "PartnerRoles");

            migrationBuilder.DropTable(
                name: "Permissions");

            migrationBuilder.DropTable(
                name: "Partners");
        }
    }
}
