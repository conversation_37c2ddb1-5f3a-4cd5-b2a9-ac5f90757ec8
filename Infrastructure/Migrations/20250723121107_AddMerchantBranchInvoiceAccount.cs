﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddMerchantBranchInvoiceAccount : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "MerchantBranchInvoiceAccounts",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TaxNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    InvoiceAccountUserName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    InvoiceAccountPassword = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    InvoiceAccountProvider = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false, defaultValue: "MBF"),
                    EffectiveDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ExpirationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    MerchantBranchId = table.Column<Guid>(type: "uuid", nullable: false),
                    MerchantBranchName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    PartnerId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IpAddress = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true),
                    TraceId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MerchantBranchInvoiceAccounts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MerchantBranchInvoiceAccounts_Partners_PartnerId",
                        column: x => x.PartnerId,
                        principalTable: "Partners",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_MerchantBranchInvoiceAccounts_DateRange",
                table: "MerchantBranchInvoiceAccounts",
                columns: new[] { "EffectiveDate", "ExpirationDate" });

            migrationBuilder.CreateIndex(
                name: "IX_MerchantBranchInvoiceAccounts_IsActive",
                table: "MerchantBranchInvoiceAccounts",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_MerchantBranchInvoiceAccounts_MerchantBranchId",
                table: "MerchantBranchInvoiceAccounts",
                column: "MerchantBranchId");

            migrationBuilder.CreateIndex(
                name: "IX_MerchantBranchInvoiceAccounts_PartnerId",
                table: "MerchantBranchInvoiceAccounts",
                column: "PartnerId");

            migrationBuilder.CreateIndex(
                name: "IX_MerchantBranchInvoiceAccounts_TaxNumber",
                table: "MerchantBranchInvoiceAccounts",
                column: "TaxNumber");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MerchantBranchInvoiceAccounts");
        }
    }
}
