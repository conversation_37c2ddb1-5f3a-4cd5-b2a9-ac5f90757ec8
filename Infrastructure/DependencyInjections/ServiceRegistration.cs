﻿using Applications.Interfaces;
using Applications.Interfaces.Repositories;
using Applications.Interfaces.Services;
using Infrastructure.Persistences;
using Infrastructure.Persistences.Repositories;
using Infrastructure.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Shared.Interfaces;
using Shared.Services;

namespace Infrastructure.DependencyInjections
{
    //public static class ServiceRegistration
    //{
    //    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, string connectionString)
    //    {
    //        services.AddDbContext<AppDbContext>(options =>
    //            options.UseNpgsql(connectionString));

    //        services.AddScoped(typeof(IBaseRepository<>), typeof(BaseRepository<>));
    //        services.AddScoped<IUnitOfWork, UnitOfWork>();
    //        services.AddRepositories();

    //        return services;
    //    }

    //    public static IServiceCollection AddRepositories(this IServiceCollection services)
    //    {
    //        // Thêm các repository kh<PERSON>c nếu cần
    //        return services;
    //    }
    //}
    public static class ServiceRegistration
    {
        public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, string connectionString)
        {
            // Đăng ký DbContext với Npgsql và chuỗi kết nối
            services.AddDbContext<AppDbContext>(options =>
                options.UseNpgsql(connectionString));

            // Đăng ký IApplicationDbContext interface
            services.AddScoped<IApplicationDbContext>(provider => provider.GetRequiredService<AppDbContext>());

            // Đăng ký các service repository và UnitOfWork (Legacy - sẽ được loại bỏ dần)
            services.AddScoped(typeof(IBaseRepository<>), typeof(BaseRepository<>));
            services.AddScoped<IUnitOfWork, UnitOfWork>();

            // Đăng ký các repositories khác
            services.AddRepositories();

            // Đăng ký MobiFone Authentication services
            services.AddScoped<IMobiFoneCredentialsProvider, MobiFoneCredentialsProvider>();
            services.AddScoped<IMobiFoneLoginService, MobiFoneLoginServiceAdapter>();
            services.AddScoped<IMobiFoneAuthenticationCacheService, MobiFoneAuthenticationCacheService>();

            // Đăng ký Tax Code Validation service
            services.AddScoped<ITaxCodeValidationService, TaxCodeValidationService>();

            return services;
        }

        public static IServiceCollection AddRepositories(this IServiceCollection services)
        {
            // Đăng ký các repository cụ thể
            services.AddScoped<IMerchantInvoiceOrderRepository, MerchantInvoiceOrderRepository>();
            services.AddScoped<IInvoiceInfoRepository, InvoiceInfoRepository>();
            services.AddScoped<IMerchantBranchInvoiceAccountRepository, MerchantBranchInvoiceAccountRepository>();
            services.AddScoped<ISmsLogRepository, SmsLogRepository>();
            services.AddScoped<ISmsRetryQueueRepository, SmsRetryQueueRepository>();
            services.AddScoped<IPartnerRepository, PartnerRepository>();
            services.AddScoped<IClientCredentialRepository, ClientCredentialRepository>();

            return services;
        }
    }

}
