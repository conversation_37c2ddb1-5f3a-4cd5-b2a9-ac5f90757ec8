using Applications.Interfaces.Repositories;
using Core.Entities;

namespace Infrastructure.Persistences.Repositories;

/// <summary>
/// Repository implementation for MerchantBranchInvoiceAccount entity
/// </summary>
public class MerchantBranchInvoiceAccountRepository : BaseRepository<MerchantBranchInvoiceAccount>, IMerchantBranchInvoiceAccountRepository
{
    public MerchantBranchInvoiceAccountRepository(AppDbContext context) : base(context)
    {
    }
}
