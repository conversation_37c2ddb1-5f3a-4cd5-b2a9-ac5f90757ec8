using Applications.Interfaces.Repositories;
using Core.Entities;

namespace Infrastructure.Persistences.Repositories;

/// <summary>
/// Repository implementation for MerchantInvoiceOrder entity
/// </summary>
public class MerchantInvoiceOrderRepository : BaseRepository<MerchantInvoiceOrder>, IMerchantInvoiceOrderRepository
{
    public MerchantInvoiceOrderRepository(AppDbContext context) : base(context)
    {
    }
}
