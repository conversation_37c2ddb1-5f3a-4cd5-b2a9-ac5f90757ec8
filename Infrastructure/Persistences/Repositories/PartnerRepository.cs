using Applications.Interfaces.Repositories;
using Core.Entities.Authentication;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Persistences.Repositories;

/// <summary>
/// Repository implementation for Partner entity
/// </summary>
public class PartnerRepository : BaseRepository<Partner>, IPartnerRepository
{
    public PartnerRepository(AppDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Get partner by ClientId
    /// </summary>
    /// <param name="clientId">Client ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Partner entity or null</returns>
    public async Task<Partner?> GetByClientIdAsync(string clientId, CancellationToken cancellationToken = default)
    {
        return await _context.Partners
            .Where(p => p.ClientId == clientId && !p.IsDeleted)
            .FirstOrDefaultAsync(cancellationToken);
    }

    /// <summary>
    /// Check if ClientId exists
    /// </summary>
    /// <param name="clientId">Client ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if exists</returns>
    public async Task<bool> ClientIdExistsAsync(string clientId, CancellationToken cancellationToken = default)
    {
        return await _context.Partners
            .Where(p => p.ClientId == clientId && !p.IsDeleted)
            .AnyAsync(cancellationToken);
    }
}
