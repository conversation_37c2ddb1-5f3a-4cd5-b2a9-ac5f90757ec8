﻿using Core;
using Core.Entities;
using Core.Entities.Authentication;
using Infrastructure.Configurations;
using Infrastructure.Configurations.Authentication;
using Applications.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System.Reflection;

namespace Infrastructure.Persistences
{
    public class AppDbContext(DbContextOptions<AppDbContext> options, IHttpContextAccessor httpContextAccessor) : DbContext(options), IApplicationDbContext
    {
        private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;

        // DbSets - Legacy (will be removed)
        public DbSet<ClientCredential> ClientCredentials => Set<ClientCredential>();
        public DbSet<SmsLog> SmsLogs => Set<SmsLog>();
        public DbSet<SmsRetryQueue> SmsRetryQueues => Set<SmsRetryQueue>();

        // New Authentication System DbSets
        public DbSet<Partner> Partners => Set<Partner>();
        public DbSet<PartnerToken> PartnerTokens => Set<PartnerToken>();
        public DbSet<Function> Functions => Set<Function>();
        public DbSet<Permission> Permissions => Set<Permission>();
        public DbSet<FunctionPermission> FunctionPermissions => Set<FunctionPermission>();
        public DbSet<PartnerRole> PartnerRoles => Set<PartnerRole>();
        public DbSet<RoleFunctionPermission> RoleFunctionPermissions => Set<RoleFunctionPermission>();
        public DbSet<PartnerRoleAssignment> PartnerRoleAssignments => Set<PartnerRoleAssignment>();
        public DbSet<PartnerFunctionPermission> PartnerFunctionPermissions => Set<PartnerFunctionPermission>();
        public DbSet<PartnerConstraint> PartnerConstraints => Set<PartnerConstraint>();
        public DbSet<AuthenticationLog> AuthenticationLogs => Set<AuthenticationLog>();
        public DbSet<PartnerUsage> PartnerUsages => Set<PartnerUsage>();

        // Business Entity DbSets
        public DbSet<MerchantBranchInvoiceAccount> MerchantBranchInvoiceAccounts => Set<MerchantBranchInvoiceAccount>();
        public DbSet<MerchantInvoiceOrder> MerchantInvoiceOrders => Set<MerchantInvoiceOrder>();
        public DbSet<InvoiceInfo> InvoiceInfos => Set<InvoiceInfo>();
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Áp dụng soft delete filter cho các entity kế thừa Audit
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                if (typeof(Audit).IsAssignableFrom(entityType.ClrType))
                {
                    var method = typeof(AppDbContext)
                        .GetMethod(nameof(SetSoftDeleteFilter), BindingFlags.NonPublic | BindingFlags.Static)!
                        .MakeGenericMethod(entityType.ClrType);
                    method.Invoke(null, new object[] { modelBuilder });
                }
            }

            // Apply entity configurations
            modelBuilder.ApplyConfiguration(new PartnerConfiguration());
            modelBuilder.ApplyConfiguration(new PartnerTokenConfiguration());
            modelBuilder.ApplyConfiguration(new FunctionConfiguration());
            modelBuilder.ApplyConfiguration(new PermissionConfiguration());
            modelBuilder.ApplyConfiguration(new FunctionPermissionConfiguration());
            modelBuilder.ApplyConfiguration(new PartnerRoleConfiguration());
            modelBuilder.ApplyConfiguration(new RoleFunctionPermissionConfiguration());
            modelBuilder.ApplyConfiguration(new PartnerRoleAssignmentConfiguration());
            modelBuilder.ApplyConfiguration(new PartnerFunctionPermissionConfiguration());
            modelBuilder.ApplyConfiguration(new PartnerConstraintConfiguration());
            modelBuilder.ApplyConfiguration(new AuthenticationLogConfiguration());
            modelBuilder.ApplyConfiguration(new PartnerUsageConfiguration());

            // Business Entity Configurations
            modelBuilder.ApplyConfiguration(new MerchantBranchInvoiceAccountConfiguration());
            modelBuilder.ApplyConfiguration(new MerchantInvoiceOrderConfiguration());
            modelBuilder.ApplyConfiguration(new InvoiceInfoConfiguration());

            base.OnModelCreating(modelBuilder);
        }

        private static void SetSoftDeleteFilter<TEntity>(ModelBuilder builder) where TEntity : Audit
        {
            builder.Entity<TEntity>().HasQueryFilter(e => !e.IsDeleted);
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            Guid? userId = null;
            var sub = _httpContextAccessor?.HttpContext?.User?.FindFirst("sub")?.Value;
            if (Guid.TryParse(sub, out var uid)) userId = uid;

            var now = DateTime.UtcNow;
            foreach (var entry in ChangeTracker.Entries<Audit>())
            {
                switch (entry.State)
                {
                    case EntityState.Added:
                        entry.Entity.CreatedAt = now;
                        if (entry.Entity.CreatedBy == null)
                            entry.Entity.CreatedBy = userId;
                        break;
                    case EntityState.Modified:
                        entry.Entity.UpdatedAt = now;
                        entry.Entity.UpdatedBy = userId;
                        break;
                }
            }

            return await base.SaveChangesAsync(cancellationToken);
        }
    }
}
