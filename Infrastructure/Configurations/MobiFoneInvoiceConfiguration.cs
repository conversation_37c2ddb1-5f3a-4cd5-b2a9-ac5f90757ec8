namespace Infrastructure.Configurations;

/// <summary>
/// C<PERSON><PERSON> hình cho MobiFone Invoice API
/// </summary>
public class MobiFoneInvoiceConfiguration
{
    /// <summary>
    /// Section name trong appsettings.json
    /// </summary>
    public const string SectionName = "MobiFoneInvoice";

    /// <summary>
    /// Môi trường hiện tại (sẽ được set tự động từ IHostEnvironment)
    /// Không cần config trong appsettings.json
    /// </summary>
    public string Environment { get; set; } = "Development";

    /// <summary>
    /// Base URL cho môi trường Production
    /// </summary>
    public string ProductionBaseUrl { get; set; } = string.Empty;

    /// <summary>
    /// Base URL cho môi trường Test
    /// </summary>
    public string TestBaseUrl { get; set; } = string.Empty;

    /// <summary>
    /// Mã số thuế (dùng cho production)
    /// </summary>
    public string TaxCode { get; set; } = string.Empty;

    /// <summary>
    /// Username mặc định
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Password mặc định
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// Timeout cho HTTP requests (seconds)
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// RefId cho môi trường Production (mặc định: RF00059)
    /// </summary>
    public string ProductionRefId { get; set; } = "RF00059";

    /// <summary>
    /// RefId cho môi trường Development (mặc định: RF00059)
    /// </summary>
    public string DevelopmentRefId { get; set; } = "RF00059";

    /// <summary>
    /// RefId cho môi trường Staging (mặc định: RF00059)
    /// </summary>
    public string StagingRefId { get; set; } = "RF00059";

    /// <summary>
    /// Lấy base URL dựa trên môi trường hiện tại
    /// Staging sử dụng ProductionBaseUrl giống Production
    /// </summary>
    public string GetBaseUrl()
    {
        var env = Environment.ToLower();
        return (env == "production" || env == "staging") ? ProductionBaseUrl : TestBaseUrl;
    }

    /// <summary>
    /// Lấy RefId dựa trên môi trường hiện tại
    /// </summary>
    public string GetRefId()
    {
        var env = Environment.ToLower();
        return env switch
        {
            "production" => ProductionRefId,
            "staging" => StagingRefId,
            _ => DevelopmentRefId
        };
    }

    /// <summary>
    /// Kiểm tra có phải môi trường production không
    /// </summary>
    public bool IsProduction => Environment.ToLower() == "production";

    /// <summary>
    /// Kiểm tra có phải môi trường production-like không (Production hoặc Staging)
    /// Staging sẽ có logic giống Production: cần tax_code, sử dụng ProductionBaseUrl, ProductionRefId
    /// </summary>
    public bool IsProductionLike => Environment.ToLower() is "production" or "staging";

    /// <summary>
    /// Kiểm tra có phải môi trường development không (chỉ Development là khác biệt)
    /// </summary>
    public bool IsDevelopment => Environment.ToLower() == "development";
}
