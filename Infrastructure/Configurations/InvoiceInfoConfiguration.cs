using Core.Entities;
using Core.Enumerables;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Configurations;

public class InvoiceInfoConfiguration : IEntityTypeConfiguration<InvoiceInfo>
{
    public void Configure(EntityTypeBuilder<InvoiceInfo> builder)
    {
        builder.ToTable("InvoiceInfos");

        // Primary Key
        builder.HasKey(i => i.Id);

        // Filter deleted records
        builder.HasQueryFilter(i => !i.IsDeleted);

        // Properties
        builder.Property(i => i.InvoiceId)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(i => i.CqtInvoiceStatus)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(i => i.InvoiceStatus)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(i => i.RequestData)
            .IsRequired()
            .HasColumnType("jsonb");

        builder.Property(i => i.ResponseData)
            .IsRequired()
            .HasColumnType("jsonb");

        builder.Property(i => i.MerchantInvoiceOrderId)
            .IsRequired();

        builder.Property(i => i.CurlString)
            .IsRequired();

        builder.Property(i => i.InvoiceNumber)
            .HasMaxLength(50);

        builder.Property(i => i.InvoiceSeries)
            .HasMaxLength(20);

        builder.Property(i => i.TotalAmount)
            .HasPrecision(18, 2);

        builder.Property(i => i.TaxAmount)
            .HasPrecision(18, 2);

        builder.Property(i => i.CustomerName)
            .HasMaxLength(255);

        builder.Property(i => i.CustomerTaxCode)
            .HasMaxLength(20);

        builder.Property(i => i.ErrorMessage)
            .HasMaxLength(1000);

        builder.Property(i => i.Notes)
            .HasMaxLength(1000);



        // Indexes for performance optimization
        builder.HasIndex(i => i.InvoiceId)
            .IsUnique()
            .HasDatabaseName("IX_InvoiceInfos_InvoiceId_Unique");

        builder.HasIndex(i => i.MerchantInvoiceOrderId)
            .HasDatabaseName("IX_InvoiceInfos_MerchantInvoiceOrderId");



        builder.HasIndex(i => i.CqtInvoiceStatus)
            .HasDatabaseName("IX_InvoiceInfos_CqtInvoiceStatus");

        builder.HasIndex(i => i.InvoiceStatus)
            .HasDatabaseName("IX_InvoiceInfos_InvoiceStatus");

        builder.HasIndex(i => i.InvoiceNumber)
            .HasDatabaseName("IX_InvoiceInfos_InvoiceNumber");

        builder.HasIndex(i => i.CustomerTaxCode)
            .HasDatabaseName("IX_InvoiceInfos_CustomerTaxCode");

        builder.HasIndex(i => i.InvoiceDate)
            .HasDatabaseName("IX_InvoiceInfos_InvoiceDate");

        builder.HasIndex(i => i.CreatedAt)
            .HasDatabaseName("IX_InvoiceInfos_CreatedAt");

        // Composite indexes for common filtering scenarios

        builder.HasIndex(i => new { i.MerchantInvoiceOrderId, i.CreatedAt })
            .HasDatabaseName("IX_InvoiceInfos_Order_CreatedAt");

        // Foreign Key Relationships
        builder.HasOne(i => i.MerchantInvoiceOrder)
            .WithMany(o => o.InvoiceInfos)
            .HasForeignKey(i => i.MerchantInvoiceOrderId)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("FK_InvoiceInfos_MerchantInvoiceOrders_MerchantInvoiceOrderId");


    }
}
