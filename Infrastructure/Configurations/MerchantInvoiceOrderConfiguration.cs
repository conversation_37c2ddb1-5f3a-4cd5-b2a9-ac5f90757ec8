using Core.Entities;
using Core.Enumerables;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Configurations;

public class MerchantInvoiceOrderConfiguration : IEntityTypeConfiguration<MerchantInvoiceOrder>
{
    public void Configure(EntityTypeBuilder<MerchantInvoiceOrder> builder)
    {
        builder.ToTable("MerchantInvoiceOrders");

        // Primary Key
        builder.HasKey(m => m.Id);

        // Filter deleted records
        builder.HasQueryFilter(m => !m.IsDeleted);

        // Properties
        builder.Property(m => m.MerchantBranchId);

        builder.Property(m => m.TotalInvoiceQuantity)
            .IsRequired();

        builder.Property(m => m.RemainingInvoiceQuantity)
            .IsRequired();

        builder.Property(m => m.EffectiveDateFrom)
            .IsRequired();

        builder.Property(m => m.EffectiveDateTo)
            .IsRequired();

        builder.Property(m => m.Description)
            .HasMaxLength(1000);

        builder.Property(m => m.IsActive)
            .HasDefaultValue(true);

        builder.Property(m => m.OrderReference)
            .HasMaxLength(100);

        builder.Property(m => m.Status)
            .HasConversion<string>()
            .HasDefaultValue(MerchantInvoiceOrderStatus.PENDING);

        // Indexes for performance optimization
        builder.HasIndex(m => m.MerchantBranchId)
            .HasDatabaseName("IX_MerchantInvoiceOrders_MerchantBranchId");

        builder.HasIndex(m => m.IsActive)
            .HasDatabaseName("IX_MerchantInvoiceOrders_IsActive");

        builder.HasIndex(m => new { m.EffectiveDateFrom, m.EffectiveDateTo })
            .HasDatabaseName("IX_MerchantInvoiceOrders_DateRange");

        builder.HasIndex(m => m.RemainingInvoiceQuantity)
            .HasDatabaseName("IX_MerchantInvoiceOrders_RemainingQuantity");

        builder.HasIndex(m => m.OrderReference)
            .HasDatabaseName("IX_MerchantInvoiceOrders_OrderReference");

        builder.HasIndex(m => m.Status)
            .HasDatabaseName("IX_MerchantInvoiceOrders_Status");

        // Composite index for filtering active orders with available invoices
        builder.HasIndex(m => new { m.IsActive, m.RemainingInvoiceQuantity, m.EffectiveDateFrom, m.EffectiveDateTo })
            .HasDatabaseName("IX_MerchantInvoiceOrders_ActiveWithAvailable");

        // Foreign Key Relationships
        builder.HasOne(m => m.MerchantBranchInvoiceAccount)
            .WithMany(mb => mb.MerchantInvoiceOrders)
            .HasForeignKey(m => m.MerchantBranchId)
            .HasPrincipalKey(mb => mb.MerchantBranchId)
            .OnDelete(DeleteBehavior.SetNull)
            .IsRequired(false)
            .HasConstraintName("FK_MerchantInvoiceOrders_MerchantBranchInvoiceAccounts_MerchantBranchId");
    }
}
