using Core.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Configurations;

public class MerchantBranchInvoiceAccountConfiguration : IEntityTypeConfiguration<MerchantBranchInvoiceAccount>
{
    public void Configure(EntityTypeBuilder<MerchantBranchInvoiceAccount> builder)
    {
        builder.ToTable("MerchantBranchInvoiceAccounts");

        // Primary Key
        builder.HasKey(m => m.Id);

        // Filter deleted records
        builder.HasQueryFilter(m => !m.IsDeleted);

        // Properties
        builder.Property(m => m.TaxNumber)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(m => m.InvoiceAccountUserName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(m => m.InvoiceAccountPassword)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(m => m.InvoiceAccountProvider)
            .IsRequired()
            .HasMaxLength(10)
            .HasDefaultValue("MBF");

        builder.Property(m => m.EffectiveDate)
            .IsRequired();

        builder.Property(m => m.ExpirationDate)
            .IsRequired();

        builder.Property(m => m.MerchantBranchId)
            .IsRequired();

        builder.Property(m => m.MerchantBranchName)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(m => m.PartnerId)
            .IsRequired();

        builder.Property(m => m.IsActive)
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(m => m.TaxNumber)
            .HasDatabaseName("IX_MerchantBranchInvoiceAccounts_TaxNumber");

        builder.HasIndex(m => m.MerchantBranchId)
            .IsUnique()
            .HasDatabaseName("IX_MerchantBranchInvoiceAccounts_MerchantBranchId_Unique");

        builder.HasIndex(m => m.PartnerId)
            .HasDatabaseName("IX_MerchantBranchInvoiceAccounts_PartnerId");

        builder.HasIndex(m => m.IsActive)
            .HasDatabaseName("IX_MerchantBranchInvoiceAccounts_IsActive");

        builder.HasIndex(m => new { m.EffectiveDate, m.ExpirationDate })
            .HasDatabaseName("IX_MerchantBranchInvoiceAccounts_DateRange");

        // Foreign Key Relationships
        builder.HasOne(m => m.Partner)
            .WithMany()
            .HasForeignKey(m => m.PartnerId)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("FK_MerchantBranchInvoiceAccounts_Partners_PartnerId");
    }
}
