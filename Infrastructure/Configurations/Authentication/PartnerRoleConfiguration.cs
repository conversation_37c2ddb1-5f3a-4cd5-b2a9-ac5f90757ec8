using Core.Entities.Authentication;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Configurations.Authentication;

public class PartnerRoleConfiguration : IEntityTypeConfiguration<PartnerRole>
{
    public void Configure(EntityTypeBuilder<PartnerRole> builder)
    {
        builder.ToTable("PartnerRoles");

        // Primary Key
        builder.HasKey(pr => pr.Id);

        // Properties
        builder.Property(pr => pr.Code)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(pr => pr.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(pr => pr.Description)
            .HasMaxLength(1000);

        builder.Property(pr => pr.Priority)
            .HasDefaultValue(0);

        builder.Property(pr => pr.IsActive)
            .HasDefaultValue(true);

        builder.Property(pr => pr.DefaultApiRateLimitPerHour)
            .HasDefaultValue(1000);

        builder.Property(pr => pr.DefaultMonthlyInvoiceLimit)
            .HasColumnType("decimal(18,2)")
            .HasDefaultValue(10000m);

        builder.Property(pr => pr.Metadata)
            .HasColumnType("text");

        // Indexes
        builder.HasIndex(pr => pr.Code)
            .IsUnique()
            .HasDatabaseName("IX_PartnerRoles_Code");

        builder.HasIndex(pr => pr.Priority)
            .HasDatabaseName("IX_PartnerRoles_Priority");

        builder.HasIndex(pr => pr.IsActive)
            .HasDatabaseName("IX_PartnerRoles_IsActive");

        // Navigation Properties
        builder.HasMany(pr => pr.RoleFunctionPermissions)
            .WithOne(rfp => rfp.Role)
            .HasForeignKey(rfp => rfp.RoleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(pr => pr.PartnerRoleAssignments)
            .WithOne(pra => pra.Role)
            .HasForeignKey(pra => pra.RoleId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}