using Core.Entities.Authentication;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Configurations.Authentication;

public class AuthenticationLogConfiguration : IEntityTypeConfiguration<AuthenticationLog>
{
    public void Configure(EntityTypeBuilder<AuthenticationLog> builder)
    {
        builder.ToTable("AuthenticationLogs");

        // Primary Key
        builder.HasKey(al => al.Id);

        // Properties
        builder.Property(al => al.ClientId)
            .HasMaxLength(100);

        builder.Property(al => al.IpAddress)
            .IsRequired()
            .HasMaxLength(45); // IPv6 support

        builder.Property(al => al.UserAgent)
            .HasMaxLength(1000);

        builder.Property(al => al.Success)
            .IsRequired();

        builder.Property(al => al.FailureReason)
            .HasMaxLength(500);

        builder.Property(al => al.AuthenticationMethod)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(al => al.Timestamp)
            .IsRequired();

        builder.Property(al => al.RequestPath)
            .HasMaxLength(500);

        builder.Property(al => al.HttpMethod)
            .HasMaxLength(10);

        builder.Property(al => al.Metadata)
            .HasColumnType("text");

        // Indexes
        builder.HasIndex(al => al.PartnerId)
            .HasDatabaseName("IX_AuthenticationLogs_PartnerId");

        builder.HasIndex(al => al.Timestamp)
            .HasDatabaseName("IX_AuthenticationLogs_Timestamp");

        builder.HasIndex(al => al.Success)
            .HasDatabaseName("IX_AuthenticationLogs_Success");

        builder.HasIndex(al => al.AuthenticationMethod)
            .HasDatabaseName("IX_AuthenticationLogs_AuthenticationMethod");

        builder.HasIndex(al => al.IpAddress)
            .HasDatabaseName("IX_AuthenticationLogs_IpAddress");

        builder.HasIndex(al => al.ClientId)
            .HasDatabaseName("IX_AuthenticationLogs_ClientId");

        // Composite indexes for common queries
        builder.HasIndex(al => new { al.PartnerId, al.Timestamp })
            .HasDatabaseName("IX_AuthenticationLogs_PartnerId_Timestamp");

        builder.HasIndex(al => new { al.Success, al.Timestamp })
            .HasDatabaseName("IX_AuthenticationLogs_Success_Timestamp");

        builder.HasIndex(al => new { al.IpAddress, al.Timestamp })
            .HasDatabaseName("IX_AuthenticationLogs_IpAddress_Timestamp");

        // Foreign Keys
        builder.HasOne(al => al.Partner)
            .WithMany(p => p.AuthenticationLogs)
            .HasForeignKey(al => al.PartnerId)
            .OnDelete(DeleteBehavior.SetNull); // Keep logs even if partner is deleted

        builder.HasOne(al => al.Token)
            .WithMany()
            .HasForeignKey(al => al.TokenId)
            .OnDelete(DeleteBehavior.SetNull); // Keep logs even if token is deleted
    }
}