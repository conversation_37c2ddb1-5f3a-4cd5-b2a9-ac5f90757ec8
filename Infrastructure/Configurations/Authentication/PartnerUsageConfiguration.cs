using Core.Entities.Authentication;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Configurations.Authentication;

public class PartnerUsageConfiguration : IEntityTypeConfiguration<PartnerUsage>
{
    public void Configure(EntityTypeBuilder<PartnerUsage> builder)
    {
        builder.ToTable("PartnerUsages");

        // Primary Key
        builder.HasKey(pu => pu.Id);

        // Properties
        builder.Property(pu => pu.PartnerId)
            .IsRequired();

        builder.Property(pu => pu.Period)
            .IsRequired()
            .HasMaxLength(7); // YYYY-MM format

        builder.Property(pu => pu.InvoicesPurchased)
            .HasDefaultValue(0);

        builder.Property(pu => pu.TotalAmount)
            .HasColumnType("decimal(18,2)")
            .HasDefaultValue(0m);

        builder.Property(pu => pu.ApiCallsCount)
            .HasDefaultValue(0L);

        builder.Property(pu => pu.SuccessfulApiCalls)
            .HasDefaultValue(0L);

        builder.Property(pu => pu.FailedApiCalls)
            .HasDefaultValue(0L);

        builder.Property(pu => pu.DataTransferBytes)
            .HasDefaultValue(0L);

        builder.Property(pu => pu.PeakRequestsPerHour)
            .HasDefaultValue(0);

        builder.Property(pu => pu.AverageResponseTimeMs)
            .HasDefaultValue(0.0);

        builder.Property(pu => pu.AdditionalMetrics)
            .HasColumnType("text");

        builder.Property(pu => pu.LastUpdated)
            .IsRequired();

        // Indexes
        builder.HasIndex(pu => pu.PartnerId)
            .HasDatabaseName("IX_PartnerUsages_PartnerId");

        builder.HasIndex(pu => pu.Period)
            .HasDatabaseName("IX_PartnerUsages_Period");

        builder.HasIndex(pu => pu.LastUpdated)
            .HasDatabaseName("IX_PartnerUsages_LastUpdated");

        // Unique constraint for partner-period combination
        builder.HasIndex(pu => new { pu.PartnerId, pu.Period })
            .IsUnique()
            .HasDatabaseName("IX_PartnerUsages_PartnerId_Period");

        // Composite index for efficient range queries
        builder.HasIndex(pu => new { pu.PartnerId, pu.Period, pu.LastUpdated })
            .HasDatabaseName("IX_PartnerUsages_PartnerId_Period_LastUpdated");

        // Index for reporting queries
        builder.HasIndex(pu => new { pu.Period, pu.TotalAmount })
            .HasDatabaseName("IX_PartnerUsages_Period_TotalAmount");

        // Foreign Key
        builder.HasOne(pu => pu.Partner)
            .WithMany(p => p.PartnerUsages)
            .HasForeignKey(pu => pu.PartnerId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}