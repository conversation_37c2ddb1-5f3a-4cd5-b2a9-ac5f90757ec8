using Core.Entities.Authentication;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Configurations.Authentication;

public class PartnerConfiguration : IEntityTypeConfiguration<Partner>
{
    public void Configure(EntityTypeBuilder<Partner> builder)
    {
        builder.ToTable("Partners");

        // Primary Key
        builder.HasKey(p => p.Id);

        // Properties
        builder.Property(p => p.ClientId)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(p => p.ClientSecretHash)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(p => p.HmacSecretHash)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(p => p.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(p => p.ContactEmail)
            .HasMaxLength(200);

        builder.Property(p => p.ContactPhone)
            .HasMaxLength(20);

        builder.Property(p => p.Description)
            .HasMaxLength(1000);

        builder.Property(p => p.IpWhitelist)
            .HasColumnType("text");

        builder.Property(p => p.EnableIpWhitelist)
            .HasDefaultValue(true);

        builder.Property(p => p.IsActive)
            .HasDefaultValue(true);

        builder.Property(p => p.ApiRateLimitPerHour)
            .HasDefaultValue(1000);

        builder.Property(p => p.MonthlyInvoiceLimit)
            .HasColumnType("decimal(18,2)")
            .HasDefaultValue(10000m);

        builder.Property(p => p.CurrentMonthUsage)
            .HasColumnType("decimal(18,2)")
            .HasDefaultValue(0m);

        // Indexes
        builder.HasIndex(p => p.ClientId)
            .IsUnique()
            .HasDatabaseName("IX_Partners_ClientId");

        builder.HasIndex(p => p.IsActive)
            .HasDatabaseName("IX_Partners_IsActive");

        builder.HasIndex(p => p.Name)
            .HasDatabaseName("IX_Partners_Name");

        // Navigation Properties
        builder.HasMany(p => p.PartnerTokens)
            .WithOne(pt => pt.Partner)
            .HasForeignKey(pt => pt.PartnerId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(p => p.RoleAssignments)
            .WithOne(pra => pra.Partner)
            .HasForeignKey(pra => pra.PartnerId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(p => p.FunctionPermissions)
            .WithOne(pfp => pfp.Partner)
            .HasForeignKey(pfp => pfp.PartnerId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(p => p.PartnerConstraints)
            .WithOne(pc => pc.Partner)
            .HasForeignKey(pc => pc.PartnerId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(p => p.AuthenticationLogs)
            .WithOne(al => al.Partner)
            .HasForeignKey(al => al.PartnerId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasMany(p => p.PartnerUsages)
            .WithOne(pu => pu.Partner)
            .HasForeignKey(pu => pu.PartnerId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}