using Core.Entities.Authentication;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Configurations.Authentication;

public class PartnerTokenConfiguration : IEntityTypeConfiguration<PartnerToken>
{
    public void Configure(EntityTypeBuilder<PartnerToken> builder)
    {
        builder.ToTable("PartnerTokens");

        // Primary Key
        builder.HasKey(pt => pt.Id);

        // Properties
        builder.Property(pt => pt.PartnerId)
            .IsRequired();

        builder.Property(pt => pt.AccessToken)
            .IsRequired()
            .HasMaxLength(2000);

        builder.Property(pt => pt.ExpiresAt)
            .IsRequired();

        builder.Property(pt => pt.Scope)
            .HasMaxLength(1000);

        builder.Property(pt => pt.IsActive)
            .HasDefaultValue(true);

        builder.Property(pt => pt.RevocationReason)
            .HasMaxLength(500);

        builder.Property(pt => pt.IssuedFromIp)
            .HasMaxLength(45); // IPv6 support

        builder.Property(pt => pt.IssuedFromUserAgent)
            .HasMaxLength(1000);

        // Indexes
        builder.HasIndex(pt => pt.PartnerId)
            .HasDatabaseName("IX_PartnerTokens_PartnerId");

        builder.HasIndex(pt => pt.AccessToken)
            .IsUnique()
            .HasDatabaseName("IX_PartnerTokens_AccessToken");

        builder.HasIndex(pt => pt.ExpiresAt)
            .HasDatabaseName("IX_PartnerTokens_ExpiresAt");

        builder.HasIndex(pt => pt.IsActive)
            .HasDatabaseName("IX_PartnerTokens_IsActive");

        // Foreign Key
        builder.HasOne(pt => pt.Partner)
            .WithMany(p => p.PartnerTokens)
            .HasForeignKey(pt => pt.PartnerId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}