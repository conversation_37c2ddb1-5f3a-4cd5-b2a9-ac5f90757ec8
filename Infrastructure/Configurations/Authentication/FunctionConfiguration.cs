using Core.Entities.Authentication;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Configurations.Authentication;

public class FunctionConfiguration : IEntityTypeConfiguration<Function>
{
    public void Configure(EntityTypeBuilder<Function> builder)
    {
        builder.ToTable("Functions");

        // Primary Key
        builder.HasKey(f => f.Id);

        // Properties
        builder.Property(f => f.Code)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(f => f.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(f => f.Description)
            .HasMaxLength(1000);

        builder.Property(f => f.Module)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(f => f.IsActive)
            .HasDefaultValue(true);

        builder.Property(f => f.DisplayOrder)
            .HasDefaultValue(0);

        builder.Property(f => f.Metadata)
            .HasColumnType("text");

        // Indexes
        builder.HasIndex(f => f.Code)
            .IsUnique()
            .HasDatabaseName("IX_Functions_Code");

        builder.HasIndex(f => f.Module)
            .HasDatabaseName("IX_Functions_Module");

        builder.HasIndex(f => f.IsActive)
            .HasDatabaseName("IX_Functions_IsActive");

        // Navigation Properties
        builder.HasMany(f => f.FunctionPermissions)
            .WithOne(fp => fp.Function)
            .HasForeignKey(fp => fp.FunctionId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(f => f.RoleFunctionPermissions)
            .WithOne(rfp => rfp.Function)
            .HasForeignKey(rfp => rfp.FunctionId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(f => f.PartnerFunctionPermissions)
            .WithOne(pfp => pfp.Function)
            .HasForeignKey(pfp => pfp.FunctionId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}