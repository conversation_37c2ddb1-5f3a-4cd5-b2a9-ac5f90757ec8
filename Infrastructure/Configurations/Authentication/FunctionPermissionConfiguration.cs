using Core.Entities.Authentication;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Configurations.Authentication;

public class FunctionPermissionConfiguration : IEntityTypeConfiguration<FunctionPermission>
{
    public void Configure(EntityTypeBuilder<FunctionPermission> builder)
    {
        builder.ToTable("FunctionPermissions");

        // Primary Key
        builder.HasKey(fp => fp.Id);

        // Properties
        builder.Property(fp => fp.FunctionId)
            .IsRequired();

        builder.Property(fp => fp.PermissionId)
            .IsRequired();

        builder.Property(fp => fp.IsRequired)
            .HasDefaultValue(true);

        builder.Property(fp => fp.Constraints)
            .HasMaxLength(1000);

        // Indexes
        builder.HasIndex(fp => fp.FunctionId)
            .HasDatabaseName("IX_FunctionPermissions_FunctionId");

        builder.HasIndex(fp => fp.PermissionId)
            .HasDatabaseName("IX_FunctionPermissions_PermissionId");

        // Composite index for unique function-permission combination
        builder.HasIndex(fp => new { fp.FunctionId, fp.PermissionId })
            .IsUnique()
            .HasDatabaseName("IX_FunctionPermissions_FunctionId_PermissionId");

        // Foreign Keys
        builder.HasOne(fp => fp.Function)
            .WithMany(f => f.FunctionPermissions)
            .HasForeignKey(fp => fp.FunctionId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(fp => fp.Permission)
            .WithMany(p => p.FunctionPermissions)
            .HasForeignKey(fp => fp.PermissionId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}