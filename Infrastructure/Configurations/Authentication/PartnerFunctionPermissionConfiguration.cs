using Core.Entities.Authentication;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Configurations.Authentication;

public class PartnerFunctionPermissionConfiguration : IEntityTypeConfiguration<PartnerFunctionPermission>
{
    public void Configure(EntityTypeBuilder<PartnerFunctionPermission> builder)
    {
        builder.ToTable("PartnerFunctionPermissions");

        // Primary Key
        builder.HasKey(pfp => pfp.Id);

        // Properties
        builder.Property(pfp => pfp.PartnerId)
            .IsRequired();

        builder.Property(pfp => pfp.FunctionId)
            .IsRequired();

        builder.Property(pfp => pfp.PermissionId)
            .IsRequired();

        builder.Property(pfp => pfp.IsGranted)
            .HasDefaultValue(true);

        builder.Property(pfp => pfp.GrantReason)
            .HasMaxLength(1000);

        builder.Property(pfp => pfp.Constraints)
            .HasMaxLength(1000);

        // Indexes
        builder.HasIndex(pfp => pfp.PartnerId)
            .HasDatabaseName("IX_PartnerFunctionPermissions_PartnerId");

        builder.HasIndex(pfp => pfp.FunctionId)
            .HasDatabaseName("IX_PartnerFunctionPermissions_FunctionId");

        builder.HasIndex(pfp => pfp.PermissionId)
            .HasDatabaseName("IX_PartnerFunctionPermissions_PermissionId");

        // Composite index for unique partner-function-permission combination
        builder.HasIndex(pfp => new { pfp.PartnerId, pfp.FunctionId, pfp.PermissionId })
            .IsUnique()
            .HasDatabaseName("IX_PartnerFunctionPermissions_PartnerId_FunctionId_PermissionId");

        builder.HasIndex(pfp => pfp.ExpiresAt)
            .HasDatabaseName("IX_PartnerFunctionPermissions_ExpiresAt");

        // Composite index for efficient permission checking
        builder.HasIndex(pfp => new { pfp.PartnerId, pfp.IsGranted })
            .HasDatabaseName("IX_PartnerFunctionPermissions_PartnerId_IsGranted");

        // Foreign Keys
        builder.HasOne(pfp => pfp.Partner)
            .WithMany(p => p.FunctionPermissions)
            .HasForeignKey(pfp => pfp.PartnerId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(pfp => pfp.Function)
            .WithMany(f => f.PartnerFunctionPermissions)
            .HasForeignKey(pfp => pfp.FunctionId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(pfp => pfp.Permission)
            .WithMany(p => p.PartnerFunctionPermissions)
            .HasForeignKey(pfp => pfp.PermissionId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}