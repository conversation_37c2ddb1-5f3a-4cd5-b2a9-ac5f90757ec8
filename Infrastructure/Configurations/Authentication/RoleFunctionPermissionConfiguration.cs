using Core.Entities.Authentication;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Configurations.Authentication;

public class RoleFunctionPermissionConfiguration : IEntityTypeConfiguration<RoleFunctionPermission>
{
    public void Configure(EntityTypeBuilder<RoleFunctionPermission> builder)
    {
        builder.ToTable("RoleFunctionPermissions");

        // Primary Key
        builder.HasKey(rfp => rfp.Id);

        // Properties
        builder.Property(rfp => rfp.RoleId)
            .IsRequired();

        builder.Property(rfp => rfp.FunctionId)
            .IsRequired();

        builder.Property(rfp => rfp.PermissionId)
            .IsRequired();

        builder.Property(rfp => rfp.IsGranted)
            .HasDefaultValue(true);

        builder.Property(rfp => rfp.Constraints)
            .HasMaxLength(1000);

        // Indexes
        builder.HasIndex(rfp => rfp.RoleId)
            .HasDatabaseName("IX_RoleFunctionPermissions_RoleId");

        builder.HasIndex(rfp => rfp.FunctionId)
            .HasDatabaseName("IX_RoleFunctionPermissions_FunctionId");

        builder.HasIndex(rfp => rfp.PermissionId)
            .HasDatabaseName("IX_RoleFunctionPermissions_PermissionId");

        // Composite index for unique role-function-permission combination
        builder.HasIndex(rfp => new { rfp.RoleId, rfp.FunctionId, rfp.PermissionId })
            .IsUnique()
            .HasDatabaseName("IX_RoleFunctionPermissions_RoleId_FunctionId_PermissionId");

        builder.HasIndex(rfp => rfp.ExpiresAt)
            .HasDatabaseName("IX_RoleFunctionPermissions_ExpiresAt");

        // Foreign Keys
        builder.HasOne(rfp => rfp.Role)
            .WithMany(pr => pr.RoleFunctionPermissions)
            .HasForeignKey(rfp => rfp.RoleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(rfp => rfp.Function)
            .WithMany(f => f.RoleFunctionPermissions)
            .HasForeignKey(rfp => rfp.FunctionId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(rfp => rfp.Permission)
            .WithMany(p => p.RoleFunctionPermissions)
            .HasForeignKey(rfp => rfp.PermissionId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}