using Core.Entities.Authentication;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Configurations.Authentication;

public class PartnerConstraintConfiguration : IEntityTypeConfiguration<PartnerConstraint>
{
    public void Configure(EntityTypeBuilder<PartnerConstraint> builder)
    {
        builder.ToTable("PartnerConstraints");

        // Primary Key
        builder.HasKey(pc => pc.Id);

        // Properties
        builder.Property(pc => pc.PartnerId)
            .IsRequired();

        builder.Property(pc => pc.ConstraintType)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(pc => pc.ConstraintValue)
            .IsRequired()
            .HasColumnType("text");

        builder.Property(pc => pc.ValidFrom)
            .IsRequired();

        builder.Property(pc => pc.IsActive)
            .HasDefaultValue(true);

        builder.Property(pc => pc.Priority)
            .HasDefaultValue(0);

        builder.Property(pc => pc.Description)
            .HasMaxLength(1000);

        // Indexes
        builder.HasIndex(pc => pc.PartnerId)
            .HasDatabaseName("IX_PartnerConstraints_PartnerId");

        builder.HasIndex(pc => pc.ConstraintType)
            .HasDatabaseName("IX_PartnerConstraints_ConstraintType");

        builder.HasIndex(pc => pc.ValidFrom)
            .HasDatabaseName("IX_PartnerConstraints_ValidFrom");

        builder.HasIndex(pc => pc.ValidTo)
            .HasDatabaseName("IX_PartnerConstraints_ValidTo");

        builder.HasIndex(pc => pc.IsActive)
            .HasDatabaseName("IX_PartnerConstraints_IsActive");

        builder.HasIndex(pc => pc.Priority)
            .HasDatabaseName("IX_PartnerConstraints_Priority");

        // Composite index for efficient constraint lookup
        builder.HasIndex(pc => new { pc.PartnerId, pc.ConstraintType, pc.IsActive })
            .HasDatabaseName("IX_PartnerConstraints_PartnerId_ConstraintType_IsActive");

        // Composite index for validity period queries
        builder.HasIndex(pc => new { pc.PartnerId, pc.ValidFrom, pc.ValidTo })
            .HasDatabaseName("IX_PartnerConstraints_PartnerId_ValidityPeriod");

        // Foreign Key
        builder.HasOne(pc => pc.Partner)
            .WithMany(p => p.PartnerConstraints)
            .HasForeignKey(pc => pc.PartnerId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}