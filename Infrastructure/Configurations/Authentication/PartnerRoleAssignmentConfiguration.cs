using Core.Entities.Authentication;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Configurations.Authentication;

public class PartnerRoleAssignmentConfiguration : IEntityTypeConfiguration<PartnerRoleAssignment>
{
    public void Configure(EntityTypeBuilder<PartnerRoleAssignment> builder)
    {
        builder.ToTable("PartnerRoleAssignments");

        // Primary Key
        builder.HasKey(pra => pra.Id);

        // Properties
        builder.Property(pra => pra.PartnerId)
            .IsRequired();

        builder.Property(pra => pra.RoleId)
            .IsRequired();

        builder.Property(pra => pra.AssignedAt)
            .IsRequired();

        builder.Property(pra => pra.IsActive)
            .HasDefaultValue(true);

        builder.Property(pra => pra.AssignmentReason)
            .HasMaxLength(1000);

        // Indexes
        builder.HasIndex(pra => pra.PartnerId)
            .HasDatabaseName("IX_PartnerRoleAssignments_PartnerId");

        builder.HasIndex(pra => pra.RoleId)
            .HasDatabaseName("IX_PartnerRoleAssignments_RoleId");

        builder.HasIndex(pra => pra.AssignedAt)
            .HasDatabaseName("IX_PartnerRoleAssignments_AssignedAt");

        builder.HasIndex(pra => pra.ExpiresAt)
            .HasDatabaseName("IX_PartnerRoleAssignments_ExpiresAt");

        builder.HasIndex(pra => pra.IsActive)
            .HasDatabaseName("IX_PartnerRoleAssignments_IsActive");

        // Composite index for partner-role combination lookup
        builder.HasIndex(pra => new { pra.PartnerId, pra.RoleId, pra.IsActive })
            .HasDatabaseName("IX_PartnerRoleAssignments_PartnerId_RoleId_IsActive");

        // Foreign Keys
        builder.HasOne(pra => pra.Partner)
            .WithMany(p => p.RoleAssignments)
            .HasForeignKey(pra => pra.PartnerId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(pra => pra.Role)
            .WithMany(pr => pr.PartnerRoleAssignments)
            .HasForeignKey(pra => pra.RoleId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}