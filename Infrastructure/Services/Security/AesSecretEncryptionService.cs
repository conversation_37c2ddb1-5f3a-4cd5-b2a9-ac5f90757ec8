using Applications.Interfaces.Services.Security;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Text;

namespace Infrastructure.Services.Security;

/// <summary>
/// AES-256-GCM implementation for encrypting/decrypting secrets
/// </summary>
public class AesSecretEncryptionService : ISecretEncryptionService
{
    private readonly string _encryptionKey;
    private readonly ILogger<AesSecretEncryptionService> _logger;
    private const string ENCRYPTION_PREFIX = "ENC:";
    private const int KEY_SIZE = 32; // 256 bits
    private const int NONCE_SIZE = 12; // 96 bits for GCM
    private const int TAG_SIZE = 16; // 128 bits for GCM

    public AesSecretEncryptionService(IConfiguration configuration, ILogger<AesSecretEncryptionService> logger)
    {
        _logger = logger;

        // Get encryption key from configuration
        // _encryptionKey = configuration["Security:EncryptionKey"] ??
        //                 Environment.GetEnvironmentVariable("ZENINVOICE_ENCRYPTION_KEY") ??
        //                 throw new InvalidOperationException(
        //                     "Encryption key not found. Set Security:EncryptionKey in configuration or ZENINVOICE_ENCRYPTION_KEY environment variable.");

        _encryptionKey = "389123891203891028321903890218390128390218320193";

        // Validate key length
        if (_encryptionKey.Length < 32)
        {
            throw new InvalidOperationException("Encryption key must be at least 32 characters long for AES-256.");
        }

        _logger.LogInformation("AES Secret Encryption Service initialized");
    }

    /// <summary>
    /// Encrypt a plaintext secret using AES-256-GCM
    /// </summary>
    public string EncryptSecret(string plaintext)
    {
        if (string.IsNullOrEmpty(plaintext))
            throw new ArgumentException("Plaintext cannot be null or empty", nameof(plaintext));

        try
        {
            // Convert key to bytes (take first 32 bytes if longer)
            var keyBytes = Encoding.UTF8.GetBytes(_encryptionKey).Take(KEY_SIZE).ToArray();
            var plaintextBytes = Encoding.UTF8.GetBytes(plaintext);

            // Generate random nonce
            var nonce = new byte[NONCE_SIZE];
            RandomNumberGenerator.Fill(nonce);

            // Encrypt using AES-GCM
            var ciphertext = new byte[plaintextBytes.Length];
            var tag = new byte[TAG_SIZE];

            using var aes = new AesGcm(keyBytes);
            aes.Encrypt(nonce, plaintextBytes, ciphertext, tag);

            // Combine nonce + ciphertext + tag
            var result = new byte[NONCE_SIZE + ciphertext.Length + TAG_SIZE];
            Buffer.BlockCopy(nonce, 0, result, 0, NONCE_SIZE);
            Buffer.BlockCopy(ciphertext, 0, result, NONCE_SIZE, ciphertext.Length);
            Buffer.BlockCopy(tag, 0, result, NONCE_SIZE + ciphertext.Length, TAG_SIZE);

            // Return with prefix
            return ENCRYPTION_PREFIX + Convert.ToBase64String(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to encrypt secret");
            throw new InvalidOperationException("Failed to encrypt secret", ex);
        }
    }

    /// <summary>
    /// Decrypt an encrypted secret using AES-256-GCM
    /// </summary>
    public string DecryptSecret(string encryptedSecret)
    {
        if (string.IsNullOrEmpty(encryptedSecret))
            throw new ArgumentException("Encrypted secret cannot be null or empty", nameof(encryptedSecret));

        // If not encrypted, return as-is (for backward compatibility)
        if (!IsEncrypted(encryptedSecret))
        {
            _logger.LogWarning("Attempting to decrypt non-encrypted value. Returning as plaintext for backward compatibility.");
            return encryptedSecret;
        }

        try
        {
            // Remove prefix and decode
            var base64Data = encryptedSecret.Substring(ENCRYPTION_PREFIX.Length);
            var encryptedData = Convert.FromBase64String(base64Data);

            // Validate minimum length
            if (encryptedData.Length < NONCE_SIZE + TAG_SIZE)
                throw new ArgumentException("Invalid encrypted data format");

            // Extract components
            var nonce = new byte[NONCE_SIZE];
            var tag = new byte[TAG_SIZE];
            var ciphertext = new byte[encryptedData.Length - NONCE_SIZE - TAG_SIZE];

            Buffer.BlockCopy(encryptedData, 0, nonce, 0, NONCE_SIZE);
            Buffer.BlockCopy(encryptedData, NONCE_SIZE, ciphertext, 0, ciphertext.Length);
            Buffer.BlockCopy(encryptedData, NONCE_SIZE + ciphertext.Length, tag, 0, TAG_SIZE);

            // Decrypt
            var keyBytes = Encoding.UTF8.GetBytes(_encryptionKey).Take(KEY_SIZE).ToArray();
            var plaintext = new byte[ciphertext.Length];

            using var aes = new AesGcm(keyBytes);
            aes.Decrypt(nonce, ciphertext, tag, plaintext);

            return Encoding.UTF8.GetString(plaintext);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to decrypt secret");
            throw new InvalidOperationException("Failed to decrypt secret", ex);
        }
    }

    /// <summary>
    /// Check if a string is encrypted (starts with encryption prefix)
    /// </summary>
    public bool IsEncrypted(string value)
    {
        return !string.IsNullOrEmpty(value) && value.StartsWith(ENCRYPTION_PREFIX);
    }
}
