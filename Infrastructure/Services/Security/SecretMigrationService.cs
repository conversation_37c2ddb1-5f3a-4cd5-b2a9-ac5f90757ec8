using Applications.Interfaces.Services.Security;
using Infrastructure.Persistences;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Infrastructure.Services.Security;

/// <summary>
/// Service for migrating existing plaintext HMAC secrets to encrypted format
/// </summary>
public class SecretMigrationService
{
    private readonly AppDbContext _context;
    private readonly ISecretEncryptionService _encryptionService;
    private readonly ILogger<SecretMigrationService> _logger;

    public SecretMigrationService(
        AppDbContext context,
        ISecretEncryptionService encryptionService,
        ILogger<SecretMigrationService> logger)
    {
        _context = context;
        _encryptionService = encryptionService;
        _logger = logger;
    }

    /// <summary>
    /// Migrate all plaintext HMAC secrets to encrypted format
    /// </summary>
    /// <returns>Number of partners migrated</returns>
    public async Task<int> MigrateHmacSecretsAsync()
    {
        _logger.LogInformation("Starting HMAC secret migration...");

        var partners = await _context.Partners
            .Where(p => !p.IsDeleted)
            .ToListAsync();

        int migratedCount = 0;

        foreach (var partner in partners)
        {
            try
            {
                // Check if already encrypted
                if (_encryptionService.IsEncrypted(partner.HmacSecretHash))
                {
                    _logger.LogDebug("Partner {PartnerId} HMAC secret already encrypted, skipping", partner.Id);
                    continue;
                }

                // Encrypt the plaintext secret
                var encryptedSecret = _encryptionService.EncryptSecret(partner.HmacSecretHash);
                partner.HmacSecretHash = encryptedSecret;
                partner.UpdatedAt = DateTime.UtcNow;

                migratedCount++;
                _logger.LogInformation("Encrypted HMAC secret for partner {PartnerId} ({ClientId})",
                    partner.Id, partner.ClientId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to encrypt HMAC secret for partner {PartnerId} ({ClientId})",
                    partner.Id, partner.ClientId);
                throw;
            }
        }

        if (migratedCount > 0)
        {
            await _context.SaveChangesAsync();
            _logger.LogInformation("Successfully migrated {Count} HMAC secrets", migratedCount);
        }
        else
        {
            _logger.LogInformation("No HMAC secrets needed migration");
        }

        return migratedCount;
    }

    /// <summary>
    /// Verify that all HMAC secrets can be decrypted successfully
    /// </summary>
    /// <returns>True if all secrets can be decrypted</returns>
    public async Task<bool> VerifyEncryptedSecretsAsync()
    {
        _logger.LogInformation("Verifying encrypted HMAC secrets...");

        var partners = await _context.Partners
            .Where(p => !p.IsDeleted)
            .ToListAsync();

        int verifiedCount = 0;
        int failedCount = 0;

        foreach (var partner in partners)
        {
            try
            {
                // Try to decrypt the secret
                var decryptedSecret = _encryptionService.DecryptSecret(partner.HmacSecretHash);

                if (string.IsNullOrEmpty(decryptedSecret))
                {
                    _logger.LogError("Decrypted secret is empty for partner {PartnerId} ({ClientId})",
                        partner.Id, partner.ClientId);
                    failedCount++;
                }
                else
                {
                    verifiedCount++;
                    _logger.LogDebug("Successfully verified HMAC secret for partner {PartnerId} ({ClientId})",
                        partner.Id, partner.ClientId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to decrypt HMAC secret for partner {PartnerId} ({ClientId})",
                    partner.Id, partner.ClientId);
                failedCount++;
            }
        }

        _logger.LogInformation("Verification complete: {Verified} verified, {Failed} failed",
            verifiedCount, failedCount);

        return failedCount == 0;
    }
}
