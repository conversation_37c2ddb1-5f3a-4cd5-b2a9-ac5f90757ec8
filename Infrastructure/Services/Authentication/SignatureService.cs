﻿using Applications.Interfaces.Services.Authentication;
using Applications.Interfaces.Services.Security;
using Infrastructure.Persistences;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Shared.Constants;
using BuildingBlocks.Abstractions;
using System.Security.Cryptography;
using System.Text;

namespace Infrastructure.Services.Authentication;

/// <summary>
/// Implementation of HMAC signature generation and validation
/// </summary>
public class SignatureService(
    ILogger<SignatureService> logger,
    AppDbContext context,
    ISecretEncryptionService encryptionService) : ISignatureService
{

    public string GenerateSignature(
        string httpMethod,
        string requestPath,
        string timestamp,
        string clientId,
        string payload,
        string hmacSecret)
    {
        logger.LogInformation("Generating HMAC signature for {Method} {Path}", httpMethod, requestPath);

        try
        {
            // StringToSign format: HTTP_METHOD + "\n" + REQUEST_PATH + "\n" + TIMESTAMP + "\n" + CLIENT_ID + "\n" + PAYLOAD
            // Ensure HTTP method is uppercase to match ZenShop implementation
            var normalizedHttpMethod = httpMethod.ToUpper();

            // Strip query parameters from path for consistent signature calculation
            var cleanPath = CleanPathForSignature(requestPath);
            var stringToSign = $"{normalizedHttpMethod}\n{cleanPath}\n{timestamp}\n{clientId}\n{payload}";

            Console.WriteLine($"[DEBUG ZenInvoice] HTTP Method: '{normalizedHttpMethod}'");
            Console.WriteLine($"[DEBUG ZenInvoice] Original Path: '{requestPath}'");
            Console.WriteLine($"[DEBUG ZenInvoice] Clean Path: '{cleanPath}'");
            Console.WriteLine($"[DEBUG ZenInvoice] Timestamp: '{timestamp}'");
            Console.WriteLine($"[DEBUG ZenInvoice] Client ID: '{clientId}'");
            Console.WriteLine($"[DEBUG ZenInvoice] Payload: '{payload}'");
            Console.WriteLine($"[DEBUG ZenInvoice] StringToSign: '{stringToSign}'");

            logger.LogDebug("StringToSign: {StringToSign}", stringToSign);

            // Generate HMAC-SHA256 signature
            using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(hmacSecret));
            var hashBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(stringToSign));
            var signature = Convert.ToBase64String(hashBytes);

            logger.LogInformation("Signature generated successfully");
            return signature;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating HMAC signature");
            throw;
        }
    }

    public async Task<Response<SignatureValidationResult>> ValidateSignatureAsync(
        Guid partnerId,
        string httpMethod,
        string requestPath,
        string timestamp,
        string clientId,
        string payload,
        string providedSignature,
        CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Validating HMAC signature for partner {PartnerId}", partnerId);

        try
        {
            // Get partner's HMAC secret from database
            var partner = await context.Partners
                .Where(p => p.Id == partnerId && !p.IsDeleted)
                .FirstOrDefaultAsync(cancellationToken);

            if (partner == null)
            {
                logger.LogWarning("Partner {PartnerId} not found for signature validation", partnerId);
                var errorResponse = new Response<SignatureValidationResult>();
                errorResponse.Code = ErrorCodes.NOT_FOUND_DATA;
                errorResponse.Message = 
                    "Partner not found";
                return errorResponse;
            }

            // Decrypt the partner's HMAC secret for signature validation
            // HmacSecretHash is now encrypted using AES-256-GCM for security
            var hmacSecret = encryptionService.DecryptSecret(partner.HmacSecretHash);

            // Generate expected signature
            var expectedSignature = GenerateSignature(httpMethod, requestPath, timestamp, clientId, payload, hmacSecret);

            // Compare signatures
            var isValid = string.Equals(expectedSignature, providedSignature, StringComparison.Ordinal);

            if (isValid)
            {
                logger.LogInformation("Signature validation successful for partner {PartnerId}", partnerId);
            }
            else
            {
                logger.LogWarning("Signature validation failed for partner {PartnerId}. Expected: {Expected}, Provided: {Provided}",
                    partnerId, expectedSignature, providedSignature);
            }

            return new Response<SignatureValidationResult>(new SignatureValidationResult
            {
                IsValid = isValid, RequestTime = DateTime.UtcNow,
                ExpectedSignature = expectedSignature,
                ProvidedSignature = providedSignature
            }, isValid ? "Signature validation successful" : "Signature validation failed");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error validating signature for partner {PartnerId}", partnerId);
            var errorResponse = new Response<SignatureValidationResult>
            {
                Code = ErrorCodes.INTERNAL_SERVER_ERROR,
                Message =
                    "Signature validation error"
            };
            return errorResponse;
        }
    }

    public Response<bool> ValidateTimestamp(string timestamp, int toleranceSeconds = 300)
    {
        // TODO: Implement timestamp validation
        logger.LogInformation("Validating timestamp {Timestamp}", timestamp);

        if (!long.TryParse(timestamp, out var unixTime))
        {
            var errorResponse = new Response<bool>();
                errorResponse.Code = ErrorCodes.BAD_REQUEST_ERROR;
                errorResponse.Message = "Invalid timestamp format";
                return errorResponse;
        }

        var requestTime = DateTimeOffset.FromUnixTimeSeconds(unixTime).UtcDateTime;
        var now = DateTime.UtcNow;
        var timeDiff = Math.Abs((now - requestTime).TotalSeconds);

        if (timeDiff > toleranceSeconds)
        {
            var errorResponse = new Response<bool>();
            errorResponse.Code = ErrorCodes.BAD_REQUEST_ERROR;
            errorResponse.Message = $"Timestamp too old. Difference: {timeDiff}s, Tolerance: {toleranceSeconds}s";
            return errorResponse;
        }

        return new Response<bool>(true);
    }

    public string GetCurrentTimestamp()
    {
        return DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString();
    }

    public DateTime? ParseTimestamp(string timestamp)
    {
        if (!long.TryParse(timestamp, out var unixTime))
        {
            return null;
        }

        return DateTimeOffset.FromUnixTimeSeconds(unixTime).UtcDateTime;
    }

    /// <summary>
    /// Strip query parameters from request path for consistent signature calculation
    /// </summary>
    /// <param name="requestPath">Original request path with potential query parameters</param>
    /// <returns>Clean path without query parameters</returns>
    private string CleanPathForSignature(string requestPath)
    {
        if (string.IsNullOrEmpty(requestPath))
            return requestPath;

        // Find the position of '?' which indicates start of query parameters
        var queryIndex = requestPath.IndexOf('?');

        // If no query parameters found, return original path
        if (queryIndex == -1)
            return requestPath;

        // Return path without query parameters
        return requestPath.Substring(0, queryIndex);
    }
}


