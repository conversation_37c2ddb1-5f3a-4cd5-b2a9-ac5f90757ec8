using System.IdentityModel.Tokens.Jwt;
using Applications.Interfaces.Services.Authentication;
using Core.Entities;
using Core.Entities.Authentication;
using Core.Interfaces;
using Infrastructure.Persistences;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace Infrastructure.Services.Authentication;

/// <summary>
/// Implementation of JWT token generation and validation
/// </summary>
public class TokenService : ITokenService
{
    private readonly IJwtTokenGenerator _jwtGenerator;
    private readonly AppDbContext _context;
    private readonly ILogger<TokenService> _logger;

    public TokenService(
        IJwtTokenGenerator jwtGenerator,
        AppDbContext context,
        ILogger<TokenService> logger)
    {
        _jwtGenerator = jwtGenerator;
        _context = context;
        _logger = logger;
    }

    public async Task<TokenInfo> GenerateTokenAsync(
        Partner partner, 
        string[] scopes, 
        string ipAddress, 
        string? userAgent = null)
    {
        _logger.LogInformation("Generating token for partner {PartnerId}", partner.Id);
        
        try
        {
            var tokenId = Guid.NewGuid();
            var expiresAt = DateTime.UtcNow.AddHours(2);
            
            // Create claims for JWT
            var claims = new[]
            {
                new Claim("sub", partner.Id.ToString()),
                new Claim("client_id", partner.ClientId),
                new Claim("partner_name", partner.Name),
                new Claim("jti", tokenId.ToString()),
                new Claim("iat", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64),
                new Claim("scope", string.Join(" ", scopes)),
                new Claim("ip", ipAddress)
            };

            // Generate JWT token - using existing implementation
            var dummyClient = new ClientCredential 
            { 
                Id = partner.Id, 
                ClientId = partner.ClientId,
                ClientSecretHash = partner.ClientSecretHash
            };
            var tokenResult = _jwtGenerator.GenerateToken(dummyClient);

            // Store token in database
            var partnerToken = new PartnerToken
            {
                Id = tokenId,
                PartnerId = partner.Id,
                AccessToken = tokenResult.AccessToken,
                CreatedAt = DateTime.UtcNow,
                ExpiresAt = expiresAt,
                IsActive = true,
                IssuedFromIp = ipAddress,
                IssuedFromUserAgent = userAgent,
                Scope = string.Join(" ", scopes),
                CreatedBy = partner.Id,
                IsDeleted = false
            };

            _context.PartnerTokens.Add(partnerToken);
            await _context.SaveChangesAsync();

            return new TokenInfo
            {
                AccessToken = tokenResult.AccessToken,
                ExpiresAt = expiresAt,
                ExpiresIn = 7200, // 2 hours
                TokenType = "Bearer",
                Scopes = scopes,
                TokenId = tokenId
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating token for partner {PartnerId}", partner.Id);
            throw;
        }
    }

    public async Task<TokenValidationResult> ValidateTokenAsync(string token)
    {
        _logger.LogInformation("Validating JWT token");
        
        try
        {
            // Check if token exists in database and is active
            var partnerToken = await _context.PartnerTokens
                .Include(pt => pt.Partner)
                .Where(pt => pt.AccessToken == token && pt.IsActive && !pt.IsDeleted)
                .FirstOrDefaultAsync();

            if (partnerToken == null)
            {
                _logger.LogWarning("Token not found in database");
                return new TokenValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "Token not found"
                };
            }

            // Check if token is expired
            if (partnerToken.ExpiresAt <= DateTime.UtcNow)
            {
                _logger.LogWarning("Token expired at {ExpiresAt}", partnerToken.ExpiresAt);
                return new TokenValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "Token expired"
                };
            }

            // Check if partner is still active
            if (!partnerToken.Partner.IsActive)
            {
                _logger.LogWarning("Partner {PartnerId} is no longer active", partnerToken.PartnerId);
                return new TokenValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "Partner account disabled"
                };
            }

            _logger.LogInformation("Token validated successfully for partner {PartnerId}", partnerToken.PartnerId);
            
            return new TokenValidationResult
            {
                IsValid = true,
                PartnerId = partnerToken.PartnerId,
                PartnerName = partnerToken.Partner.Name,
                ExpiresAt = partnerToken.ExpiresAt,
                Scopes = partnerToken.Scope?.Split(' ') ?? Array.Empty<string>()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating token");
            return new TokenValidationResult
            {
                IsValid = false,
                ErrorMessage = "Token validation error"
            };
        }
    }

    public ClaimsPrincipal? ExtractClaims(string token)
    {
        _logger.LogInformation("Extracting claims from JWT token");
        
        try
        {
            // For simplicity, use the existing JWT generator to validate and extract claims
            // Note: This is a simplified approach - in production you'd parse JWT directly
            
            // Get partner token from database to extract stored information
            var partnerToken = _context.PartnerTokens
                .Include(pt => pt.Partner)
                .Where(pt => pt.AccessToken == token && pt.IsActive && !pt.IsDeleted)
                .FirstOrDefault();

            if (partnerToken == null)
            {
                _logger.LogWarning("Cannot extract claims - token not found");
                return null;
            }

            // Create claims based on stored token information
            var claims = new List<Claim>
            {
                new Claim("sub", partnerToken.PartnerId.ToString()),
                new Claim("client_id", partnerToken.Partner.ClientId),
                new Claim("partner_name", partnerToken.Partner.Name),
                new Claim("jti", partnerToken.Id.ToString()),
                new Claim("scope", partnerToken.Scope ?? ""),
                new Claim("exp", ((DateTimeOffset)partnerToken.ExpiresAt).ToUnixTimeSeconds().ToString())
            };

            var identity = new ClaimsIdentity(claims, "jwt");
            var principal = new ClaimsPrincipal(identity);

            _logger.LogInformation("Claims extracted successfully for partner {PartnerId}", partnerToken.PartnerId);
            return principal;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting claims from token");
            return null;
        }
    }

    public Guid? GetPartnerIdFromClaims(ClaimsPrincipal claims)
    {
        try
        {
            var subClaim = claims.FindFirst("sub")?.Value;
            if (string.IsNullOrEmpty(subClaim))
            {
                _logger.LogWarning("Subject claim not found in token");
                return null;
            }

            if (Guid.TryParse(subClaim, out var partnerId))
            {
                _logger.LogInformation("Partner ID extracted successfully: {PartnerId}", partnerId);
                return partnerId;
            }

            _logger.LogWarning("Invalid partner ID format in claims: {SubClaim}", subClaim);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting partner ID from claims");
            return null;
        }
    }

    public bool IsTokenExpired(string token)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            if (!tokenHandler.CanReadToken(token))
            {
                return true; // Invalid token format
            }

            var jwtToken = tokenHandler.ReadJwtToken(token);
            var expiration = jwtToken.ValidTo;
            
            // Check if token is expired (with 5 minute buffer for clock skew)
            var isExpired = expiration.AddMinutes(-5) <= DateTime.UtcNow;
            
            if (isExpired)
            {
                _logger.LogWarning("Token expired at {ExpirationTime}", expiration);
            }
            
            return isExpired;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking token expiration");
            return true; // Treat as expired if we can't validate
        }
    }

    public DateTime? GetTokenExpiration(string token)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            if (!tokenHandler.CanReadToken(token))
            {
                _logger.LogWarning("Cannot read token for expiration extraction");
                return null;
            }

            var jwtToken = tokenHandler.ReadJwtToken(token);
            var expiration = jwtToken.ValidTo;
            
            _logger.LogDebug("Token expires at {ExpirationTime}", expiration);
            return expiration;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting token expiration");
            return null;
        }
    }
}