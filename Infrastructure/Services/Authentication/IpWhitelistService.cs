﻿using Applications.Interfaces.Services.Authentication;
using Infrastructure.Persistences;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using BuildingBlocks.Abstractions;
using System.Net;
using System.Net.Sockets;
using System.Text.Json;

namespace Infrastructure.Services.Authentication;

/// <summary>
/// Implementation of IP whitelist validation service
/// </summary>
public class IpWhitelistService : IIpWhitelistService
{
    private readonly ILogger<IpWhitelistService> _logger;
    private readonly AppDbContext _context;

    public IpWhitelistService(ILogger<IpWhitelistService> logger, AppDbContext context)
    {
        _logger = logger;
        _context = context;
    }

    public async Task<Response<IpWhitelistResult>> ValidateIpAsync(
        Guid partnerId, 
        string ipAddress, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Checking IP whitelist for partner {PartnerId}, IP {IpAddress}", partnerId, ipAddress);
        
        try
        {
            // 1. Get partner from database with IP whitelist configuration
            var partner = await _context.Partners
                .Where(p => p.Id == partnerId && !p.IsDeleted)
                .FirstOrDefaultAsync(cancellationToken);

            if (partner == null)
            {
                var errorResponse = new Response<IpWhitelistResult>();
                errorResponse.Code = "404";
                errorResponse.Message = 
                    "Partner not found";
                return errorResponse;
            }

            // 2. Check if IP whitelist is enabled for this partner
            if (!partner.EnableIpWhitelist)
            {
                _logger.LogInformation("IP whitelist disabled for partner {PartnerId}", partnerId);
                return new Response<IpWhitelistResult>(new IpWhitelistResult
                {
                    IsAllowed = true, IpAddress = ipAddress,
                    MatchedRule = "IP whitelist disabled",
                    WhitelistEnabled = false
                }, "IP whitelist disabled for partner");
            }

            // 3. Parse IP whitelist from database
            var whitelistParseResult = ParseIpWhitelist(partner.IpWhitelist);
            if (!whitelistParseResult.IsSuccess)
            {
                _logger.LogError("Failed to parse IP whitelist for partner {PartnerId}: {Error}", 
                    partnerId, whitelistParseResult.Message);
                var errorResponse = new Response<IpWhitelistResult>();
                errorResponse.Code = "500";
                errorResponse.Message = 
                    "Invalid IP whitelist configuration";
                return errorResponse;
            }

            var whitelistEntries = whitelistParseResult.Data ?? Array.Empty<string>();

            // 4. Check if IP is in whitelist
            var isAllowed = IsIpInWhitelist(ipAddress, whitelistEntries);
            var matchedRule = isAllowed ? 
                $"Matched: {whitelistEntries.FirstOrDefault(entry => IsIpMatch(ipAddress, entry))}" : 
                "No match found";

            var result = new IpWhitelistResult
            {
                IsAllowed = isAllowed,
                IpAddress = ipAddress,
                MatchedRule = matchedRule,
                WhitelistEnabled = true
            };

            if (isAllowed)
            {
                _logger.LogInformation("IP {IpAddress} allowed for partner {PartnerId}: {Rule}", 
                    ipAddress, partnerId, matchedRule);
                return new Response<IpWhitelistResult>(result, "IP address allowed");
            }
            else
            {
                _logger.LogWarning("IP {IpAddress} blocked for partner {PartnerId}: not in whitelist", 
                    ipAddress, partnerId);
                return new Response<IpWhitelistResult>(result, "IP address not in whitelist");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating IP whitelist for partner {PartnerId}, IP {IpAddress}", 
                partnerId, ipAddress);
            var errorResponse = new Response<IpWhitelistResult>();
                errorResponse.Code = "500";
                errorResponse.Message = 
                "IP whitelist validation error";
                return errorResponse;
        }
    }

    public Response<string[]> ParseIpWhitelist(string? ipWhitelistJson)
    {
        _logger.LogInformation("Parsing IP whitelist JSON: {Json}", ipWhitelistJson);
        
        if (string.IsNullOrWhiteSpace(ipWhitelistJson))
        {
            return new Response<string[]>(Array.Empty<string>(), "Empty whitelist");
        }
        
        try
        {
            // Parse JSON array of IP addresses/ranges
            var ipList = JsonSerializer.Deserialize<string[]>(ipWhitelistJson);
            
            if (ipList == null)
            {
                return new Response<string[]>(Array.Empty<string>(), "Null IP list parsed as empty");
            }

            // Validate each IP address/CIDR in the list
            var validIps = new List<string>();
            foreach (var ip in ipList)
            {
                if (string.IsNullOrWhiteSpace(ip)) continue;
                
                var trimmedIp = ip.Trim();
                
                // Check if it's a valid IP address or CIDR notation
                if (IsValidIpAddress(trimmedIp) || IsValidCidr(trimmedIp) || IsValidHostname(trimmedIp))
                {
                    validIps.Add(trimmedIp);
                }
                else
                {
                    _logger.LogWarning("Invalid IP entry in whitelist: {IpEntry}", trimmedIp);
                }
            }

            _logger.LogInformation("Parsed {ValidCount}/{TotalCount} valid IP entries", 
                validIps.Count, ipList.Length);
            
            return new Response<string[]>(validIps.ToArray(), $"Parsed {validIps.Count} valid IP entries");
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Failed to parse IP whitelist JSON: {Json}", ipWhitelistJson);
            var errorResponse = new Response<string[]>();
                errorResponse.Code = "400";
                errorResponse.Message = 
                "Invalid JSON format in IP whitelist";
                return errorResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing IP whitelist: {Json}", ipWhitelistJson);
            var errorResponse = new Response<string[]>();
                errorResponse.Code = "500";
                errorResponse.Message = 
                "Error parsing IP whitelist";
                return errorResponse;
        }
    }

    public bool IsIpInWhitelist(string ipAddress, string[] whitelistEntries)
    {
        _logger.LogInformation("Checking if IP {IpAddress} is in whitelist with {Count} entries", 
            ipAddress, whitelistEntries.Length);
        
        if (whitelistEntries.Length == 0)
        {
            _logger.LogWarning("Empty whitelist - blocking IP {IpAddress}", ipAddress);
            return false;
        }

        foreach (var entry in whitelistEntries)
        {
            if (IsIpMatch(ipAddress, entry))
            {
                _logger.LogInformation("IP {IpAddress} matched whitelist entry: {Entry}", ipAddress, entry);
                return true;
            }
        }

        _logger.LogWarning("IP {IpAddress} not found in whitelist", ipAddress);
        return false;
    }

    private bool IsIpMatch(string ipAddress, string whitelistEntry)
    {
        if (string.IsNullOrWhiteSpace(whitelistEntry)) return false;
        
        var entry = whitelistEntry.Trim();
        
        // 1. Exact match
        if (string.Equals(ipAddress, entry, StringComparison.OrdinalIgnoreCase))
        {
            return true;
        }
        
        // 2. Hostname match (localhost, etc.)
        if (IsValidHostname(entry) && string.Equals(ipAddress, entry, StringComparison.OrdinalIgnoreCase))
        {
            return true;
        }
        
        // 3. CIDR notation match
        if (entry.Contains('/'))
        {
            return IsIpInCidrRange(ipAddress, entry);
        }
        
        // 4. Wildcard match (basic pattern like 192.168.*.*)
        if (entry.Contains('*'))
        {
            return IsIpMatchWildcard(ipAddress, entry);
        }
        
        return false;
    }

    public bool IsValidIpAddress(string ipAddress)
    {
        return IPAddress.TryParse(ipAddress, out _);
    }

    public bool IsValidCidr(string cidr)
    {
        if (string.IsNullOrWhiteSpace(cidr) || !cidr.Contains('/'))
        {
            return false;
        }
        
        try
        {
            var parts = cidr.Split('/');
            if (parts.Length != 2) return false;
            
            var ipPart = parts[0].Trim();
            var maskPart = parts[1].Trim();
            
            // Validate IP address part
            if (!IPAddress.TryParse(ipPart, out var ip)) return false;
            
            // Validate mask part
            if (!int.TryParse(maskPart, out var mask)) return false;
            
            // Check mask range based on IP version
            if (ip.AddressFamily == AddressFamily.InterNetwork) // IPv4
            {
                return mask >= 0 && mask <= 32;
            }
            else if (ip.AddressFamily == AddressFamily.InterNetworkV6) // IPv6
            {
                return mask >= 0 && mask <= 128;
            }
            
            return false;
        }
        catch
        {
            return false;
        }
    }

    private bool IsValidHostname(string hostname)
    {
        if (string.IsNullOrWhiteSpace(hostname)) return false;
        
        // Common hostname patterns
        var validHostnames = new[] { "localhost", "0.0.0.0" };
        return validHostnames.Contains(hostname, StringComparer.OrdinalIgnoreCase);
    }

    private bool IsIpInCidrRange(string ipAddress, string cidr)
    {
        try
        {
            if (!IsValidCidr(cidr)) return false;
            if (!IPAddress.TryParse(ipAddress, out var targetIp)) return false;
            
            var parts = cidr.Split('/');
            var networkIp = IPAddress.Parse(parts[0]);
            var prefixLength = int.Parse(parts[1]);
            
            // Ensure both IPs are same family (IPv4 or IPv6)
            if (targetIp.AddressFamily != networkIp.AddressFamily) return false;
            
            if (targetIp.AddressFamily == AddressFamily.InterNetwork) // IPv4
            {
                return IsIpv4InCidr(targetIp, networkIp, prefixLength);
            }
            else if (targetIp.AddressFamily == AddressFamily.InterNetworkV6) // IPv6
            {
                return IsIpv6InCidr(targetIp, networkIp, prefixLength);
            }
            
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking IP {IpAddress} in CIDR {Cidr}", ipAddress, cidr);
            return false;
        }
    }

    private bool IsIpv4InCidr(IPAddress targetIp, IPAddress networkIp, int prefixLength)
    {
        var targetBytes = targetIp.GetAddressBytes();
        var networkBytes = networkIp.GetAddressBytes();
        
        if (prefixLength == 0) return true; // 0.0.0.0/0 matches everything
        if (prefixLength > 32) return false;
        
        int bytesToCheck = prefixLength / 8;
        int bitsToCheck = prefixLength % 8;
        
        // Check complete bytes
        for (int i = 0; i < bytesToCheck; i++)
        {
            if (targetBytes[i] != networkBytes[i]) return false;
        }
        
        // Check remaining bits in the next byte
        if (bitsToCheck > 0 && bytesToCheck < 4)
        {
            int mask = 0xFF << (8 - bitsToCheck);
            if ((targetBytes[bytesToCheck] & mask) != (networkBytes[bytesToCheck] & mask))
            {
                return false;
            }
        }
        
        return true;
    }

    private bool IsIpv6InCidr(IPAddress targetIp, IPAddress networkIp, int prefixLength)
    {
        var targetBytes = targetIp.GetAddressBytes();
        var networkBytes = networkIp.GetAddressBytes();
        
        if (prefixLength == 0) return true; // ::/0 matches everything
        if (prefixLength > 128) return false;
        
        int bytesToCheck = prefixLength / 8;
        int bitsToCheck = prefixLength % 8;
        
        // Check complete bytes
        for (int i = 0; i < bytesToCheck; i++)
        {
            if (targetBytes[i] != networkBytes[i]) return false;
        }
        
        // Check remaining bits in the next byte
        if (bitsToCheck > 0 && bytesToCheck < 16)
        {
            int mask = 0xFF << (8 - bitsToCheck);
            if ((targetBytes[bytesToCheck] & mask) != (networkBytes[bytesToCheck] & mask))
            {
                return false;
            }
        }
        
        return true;
    }

    private bool IsIpMatchWildcard(string ipAddress, string pattern)
    {
        try
        {
            var ipParts = ipAddress.Split('.');
            var patternParts = pattern.Split('.');
            
            if (ipParts.Length != 4 || patternParts.Length != 4) return false;
            
            for (int i = 0; i < 4; i++)
            {
                if (patternParts[i] != "*" && ipParts[i] != patternParts[i])
                {
                    return false;
                }
            }
            
            return true;
        }
        catch
        {
            return false;
        }
    }

    public string GetClientIpAddress(HttpContext httpContext)
    {
        // Check for forwarded headers first
        var forwardedFor = httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            return forwardedFor.Split(',')[0].Trim();
        }

        var realIp = httpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        // Fall back to connection remote IP
        return httpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
    }
}


