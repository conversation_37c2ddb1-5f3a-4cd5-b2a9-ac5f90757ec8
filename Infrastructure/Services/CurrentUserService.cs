using Applications.Interfaces.Services;
using Core.Entities.Authentication;
using Infrastructure.Persistences;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Security.Claims;

namespace Infrastructure.Services;

/// <summary>
/// Implementation of Current User service
/// </summary>
public class CurrentUserService : ICurrentUserService
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly AppDbContext _context;
    private readonly ILogger<CurrentUserService> _logger;

    public CurrentUserService(
        IHttpContextAccessor httpContextAccessor,
        AppDbContext context,
        ILogger<CurrentUserService> logger)
    {
        _httpContextAccessor = httpContextAccessor;
        _context = context;
        _logger = logger;
    }

    public Guid? GetCurrentPartnerId()
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.Items["PartnerId"] is Guid partnerId)
            {
                return partnerId;
            }

            // Fallback: try to get from claims
            var subClaim = httpContext?.User?.FindFirst("sub")?.Value;
            if (!string.IsNullOrEmpty(subClaim) && Guid.TryParse(subClaim, out var partnerIdFromClaims))
            {
                return partnerIdFromClaims;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current partner ID");
            return null;
        }
    }

    public string? GetCurrentPartnerName()
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.Items["PartnerName"] is string partnerName)
            {
                return partnerName;
            }

            // Fallback: try to get from claims
            var partnerNameClaim = httpContext?.User?.FindFirst("partner_name")?.Value;
            return partnerNameClaim;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current partner name");
            return null;
        }
    }

    public async Task<Partner?> GetCurrentPartnerAsync()
    {
        try
        {
            var partnerId = GetCurrentPartnerId();
            if (partnerId == null)
            {
                _logger.LogWarning("No current partner ID found");
                return null;
            }

            var partner = await _context.Partners
                .Where(p => p.Id == partnerId.Value && p.IsActive && !p.IsDeleted)
                .FirstOrDefaultAsync();

            if (partner == null)
            {
                _logger.LogWarning("Partner {PartnerId} not found or inactive", partnerId);
            }

            return partner;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current partner");
            return null;
        }
    }

    public async Task<Partner?> GetCurrentPartnerWithDetailsAsync()
    {
        try
        {
            var partnerId = GetCurrentPartnerId();
            if (partnerId == null)
            {
                _logger.LogWarning("No current partner ID found");
                return null;
            }

            var partner = await _context.Partners
                .Include(p => p.PartnerTokens.Where(t => t.IsActive && !t.IsDeleted))
                .Include(p => p.RoleAssignments)
                    .ThenInclude(ra => ra.Role)
                .Include(p => p.FunctionPermissions)
                    .ThenInclude(fp => fp.Function)
                .Include(p => p.PartnerConstraints)
                .Where(p => p.Id == partnerId.Value && p.IsActive && !p.IsDeleted)
                .FirstOrDefaultAsync();

            if (partner == null)
            {
                _logger.LogWarning("Partner {PartnerId} not found or inactive", partnerId);
            }

            return partner;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current partner with details");
            return null;
        }
    }

    public bool IsAuthenticated()
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            return httpContext?.User?.Identity?.IsAuthenticated == true || GetCurrentPartnerId() != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking authentication status");
            return false;
        }
    }

    public DateTime? GetTokenExpiration()
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.Items["TokenExpires"] is DateTime expiration)
            {
                return expiration;
            }

            // Fallback: try to get from claims
            var expClaim = httpContext?.User?.FindFirst("exp")?.Value;
            if (!string.IsNullOrEmpty(expClaim) && long.TryParse(expClaim, out var expUnix))
            {
                return DateTimeOffset.FromUnixTimeSeconds(expUnix).DateTime;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting token expiration");
            return null;
        }
    }

    public bool IsSimpleBearerAuth()
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            return httpContext?.Items["IsSimpleBearerAuth"] is bool isSimple && isSimple;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking simple bearer auth status");
            return false;
        }
    }

    public string[] GetCurrentUserScopes()
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            var scopeClaim = httpContext?.User?.FindFirst("scope")?.Value;

            if (string.IsNullOrEmpty(scopeClaim))
            {
                return Array.Empty<string>();
            }

            return scopeClaim.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current user scopes");
            return Array.Empty<string>();
        }
    }
}