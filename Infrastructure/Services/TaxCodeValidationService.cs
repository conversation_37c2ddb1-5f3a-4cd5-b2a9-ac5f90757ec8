using Applications.Interfaces;
using Applications.Interfaces.Services;
using BuildingBlocks.Abstractions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Infrastructure.Services;

/// <summary>
/// Service để xác thực tax code thuộc về partner
/// </summary>
public class TaxCodeValidationService : ITaxCodeValidationService
{
    private readonly IApplicationDbContext _context;
    private readonly ILogger<TaxCodeValidationService> _logger;

    public TaxCodeValidationService(
        IApplicationDbContext context,
        ILogger<TaxCodeValidationService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<Response<TaxCodeValidationResult>> ValidateTaxCodeAsync(
        Guid partnerId, 
        string taxCode, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Validating tax code {TaxCode} for partner {PartnerId}", taxCode, partnerId);

            // Ki<PERSON>m tra input
            if (string.IsNullOrWhiteSpace(taxCode))
            {
                var emptyResult = new TaxCodeValidationResult
                {
                    IsValid = false,
                    TaxCode = taxCode ?? string.Empty,
                    PartnerId = partnerId,
                    InvalidReason = "Tax code is empty or null"
                };
                return new Response<TaxCodeValidationResult>(emptyResult, "Tax code is required");
            }

            // Tìm kiếm trong bảng MerchantBranchInvoiceAccount
            var merchantBranchAccount = await _context.MerchantBranchInvoiceAccounts
                .Where(m => m.TaxNumber == taxCode && 
                           m.PartnerId == partnerId && 
                           !m.IsDeleted)
                .FirstOrDefaultAsync(cancellationToken);

            if (merchantBranchAccount == null)
            {
                _logger.LogWarning("Tax code {TaxCode} not found for partner {PartnerId}", taxCode, partnerId);
                
                var notFoundResult = new TaxCodeValidationResult
                {
                    IsValid = false,
                    TaxCode = taxCode,
                    PartnerId = partnerId,
                    InvalidReason = "Tax code does not belong to current partner"
                };
                return new Response<TaxCodeValidationResult>(notFoundResult, 
                    "Tax code does not belong to current partner");
            }

            // Kiểm tra trạng thái active
            if (!merchantBranchAccount.IsActive)
            {
                _logger.LogWarning("Tax code {TaxCode} is inactive for partner {PartnerId}", taxCode, partnerId);
                
                var inactiveResult = new TaxCodeValidationResult
                {
                    IsValid = false,
                    TaxCode = taxCode,
                    PartnerId = partnerId,
                    MerchantBranchInvoiceAccountId = merchantBranchAccount.Id,
                    MerchantBranchName = merchantBranchAccount.MerchantBranchName,
                    InvalidReason = "Tax code account is inactive",
                    EffectiveDate = merchantBranchAccount.EffectiveDate,
                    ExpirationDate = merchantBranchAccount.ExpirationDate,
                    IsActive = merchantBranchAccount.IsActive
                };
                return new Response<TaxCodeValidationResult>(inactiveResult, 
                    "Tax code account is inactive");
            }

            // Kiểm tra thời gian hiệu lực
            var currentTime = DateTime.UtcNow;
            if (currentTime < merchantBranchAccount.EffectiveDate)
            {
                _logger.LogWarning("Tax code {TaxCode} is not yet effective for partner {PartnerId}. Effective date: {EffectiveDate}", 
                    taxCode, partnerId, merchantBranchAccount.EffectiveDate);
                
                var notEffectiveResult = new TaxCodeValidationResult
                {
                    IsValid = false,
                    TaxCode = taxCode,
                    PartnerId = partnerId,
                    MerchantBranchInvoiceAccountId = merchantBranchAccount.Id,
                    MerchantBranchName = merchantBranchAccount.MerchantBranchName,
                    InvalidReason = $"Tax code account is not yet effective. Effective date: {merchantBranchAccount.EffectiveDate:yyyy-MM-dd}",
                    EffectiveDate = merchantBranchAccount.EffectiveDate,
                    ExpirationDate = merchantBranchAccount.ExpirationDate,
                    IsActive = merchantBranchAccount.IsActive
                };
                return new Response<TaxCodeValidationResult>(notEffectiveResult, 
                    "Tax code account is not yet effective");
            }

            // Kiểm tra thời gian hết hạn
            if (currentTime > merchantBranchAccount.ExpirationDate)
            {
                _logger.LogWarning("Tax code {TaxCode} has expired for partner {PartnerId}. Expiration date: {ExpirationDate}", 
                    taxCode, partnerId, merchantBranchAccount.ExpirationDate);
                
                var expiredResult = new TaxCodeValidationResult
                {
                    IsValid = false,
                    TaxCode = taxCode,
                    PartnerId = partnerId,
                    MerchantBranchInvoiceAccountId = merchantBranchAccount.Id,
                    MerchantBranchName = merchantBranchAccount.MerchantBranchName,
                    InvalidReason = $"Tax code account has expired. Expiration date: {merchantBranchAccount.ExpirationDate:yyyy-MM-dd}",
                    EffectiveDate = merchantBranchAccount.EffectiveDate,
                    ExpirationDate = merchantBranchAccount.ExpirationDate,
                    IsActive = merchantBranchAccount.IsActive
                };
                return new Response<TaxCodeValidationResult>(expiredResult, 
                    "Tax code account has expired");
            }

            // Validation thành công
            _logger.LogDebug("Tax code validation successful for {TaxCode} and partner {PartnerId}", 
                taxCode, partnerId);
            
            var successResult = new TaxCodeValidationResult
            {
                IsValid = true,
                TaxCode = taxCode,
                PartnerId = partnerId,
                MerchantBranchInvoiceAccountId = merchantBranchAccount.Id,
                MerchantBranchName = merchantBranchAccount.MerchantBranchName,
                EffectiveDate = merchantBranchAccount.EffectiveDate,
                ExpirationDate = merchantBranchAccount.ExpirationDate,
                IsActive = merchantBranchAccount.IsActive
            };
            
            return new Response<TaxCodeValidationResult>(successResult, "Tax code validation successful");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating tax code {TaxCode} for partner {PartnerId}", taxCode, partnerId);
            
            var errorResult = new TaxCodeValidationResult
            {
                IsValid = false,
                TaxCode = taxCode,
                PartnerId = partnerId,
                InvalidReason = "Internal server error during validation"
            };
            
            return new Response<TaxCodeValidationResult>(errorResult, "Internal server error during tax code validation");
        }
    }
}
