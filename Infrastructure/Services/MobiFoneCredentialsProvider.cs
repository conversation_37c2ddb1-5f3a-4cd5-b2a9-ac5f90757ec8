using Applications.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Shared.Interfaces;
using Shared.Models;

namespace Infrastructure.Services;

/// <summary>
/// Implementation để lấy thông tin login credentials từ database
/// </summary>
public class MobiFoneCredentialsProvider : IMobiFoneCredentialsProvider
{
    private readonly IApplicationDbContext _dbContext;
    private readonly ILogger<MobiFoneCredentialsProvider> _logger;

    public MobiFoneCredentialsProvider(
        IApplicationDbContext dbContext,
        ILogger<MobiFoneCredentialsProvider> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<MobiFoneLoginCredentials?> GetCredentialsAsync(string taxCode, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(taxCode))
            {
                _logger.LogWarning("TaxCode is null or empty");
                return null;
            }

            _logger.LogDebug("Getting credentials for TaxCode: {TaxCode}", taxCode);

            var merchantAccount = await _dbContext.MerchantBranchInvoiceAccounts
                .FirstOrDefaultAsync(x => x.TaxNumber == taxCode && x.IsActive, cancellationToken);

            if (merchantAccount == null)
            {
                _logger.LogWarning("No active MerchantBranchInvoiceAccount found for TaxCode: {TaxCode}", taxCode);
                return null;
            }

            var credentials = new MobiFoneLoginCredentials
            {
                Username = merchantAccount.InvoiceAccountUserName,
                Password = merchantAccount.InvoiceAccountPassword,
                TaxCode = taxCode
            };

            _logger.LogDebug("Successfully retrieved credentials for TaxCode: {TaxCode}, Username: {Username}", 
                taxCode, credentials.Username);

            return credentials;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting credentials for TaxCode: {TaxCode}", taxCode);
            return null;
        }
    }
}
