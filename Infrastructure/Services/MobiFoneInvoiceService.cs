using Applications.DTOs.MobiFoneInvoice.GetDataReferences;
using Applications.DTOs.MobiFoneInvoice.GetListCertificatesFile68;
using Applications.DTOs.MobiFoneInvoice.GetListCertificatesFile68.Raws;
using Applications.DTOs.MobiFoneInvoice.Login;
using Applications.DTOs.MobiFoneInvoice.CreateInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateSalesInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreatePublicAssetSalesInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateNationalReserveSalesInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateOtherInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreatePXKVCNBInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreatePXKDLInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateInvoiceMTT.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateSalesInvoiceMTT.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateOtherInvoiceMTT.Raws;
using Applications.DTOs.MobiFoneInvoice.SaveAndSignHoadon78.Raws;
using Applications.DTOs.MobiFoneInvoice.SignInvoiceCertFile68.Raws;
using Applications.DTOs.MobiFoneInvoice.SendInvoiceToCQT68.Raws;
using Applications.DTOs.MobiFoneInvoice.SignAndSendInvoiceToCQT68.Raws;
using Applications.DTOs.MobiFoneInvoice.GetHistoryInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.SendInvoiceByEmail.Raws;
using Applications.DTOs.MobiFoneInvoice.DownloadInvoicePDF.Raws;
using Applications.DTOs.MobiFoneInvoice.PrintMultipleInvoices.Raws;
using Applications.DTOs.MobiFoneInvoice.DeleteUnsignedInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CancelInvoiceWithoutCode.Raws;
using Applications.DTOs.MobiFoneInvoice.GetInvoiceById.Raws;
using Applications.DTOs.MobiFoneInvoice.GetHoadonFkey.Raws;
using Applications.DTOs.MobiFoneInvoice.GetInvoiceByTimeAndUnit.Raws;
using Applications.DTOs.MobiFoneInvoice.ExportXMLHoadon.Raws;
using Applications.DTOs.MobiFoneInvoice.ExportInvoiceXmlPretreatment.Raws;
using Applications.DTOs.MobiFoneInvoice.GetInvoiceFromdateTodate.Raws;
using Applications.Features.InvoiceInfo.Commands;
using Applications.Interfaces.Services;
using Applications.Interfaces;
using Applications.Exceptions;
using Infrastructure.Configurations;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Hosting;
using Microsoft.EntityFrameworkCore;
using Shared.Constants;
using Core.Entities;
using Core.Enumerables;
using System.Text;
using System.Text.Json;
using HttpClientToCurl;
using BuildingBlocks.Abstractions;

namespace Infrastructure.Services;

/// <summary>
/// Service tích hợp MobiFone Invoice API
/// </summary>
public class MobiFoneInvoiceService(
    HttpClient httpClient,
    IOptions<MobiFoneInvoiceConfiguration> config,
    IHostEnvironment hostEnvironment,
    ILogger<MobiFoneInvoiceService> logger,
    IApplicationDbContext dbContext,
    IMediator mediator) : IMobiFoneInvoiceService
{
    private readonly MobiFoneInvoiceConfiguration _config = InitializeConfig(config.Value, hostEnvironment);
    private readonly JsonSerializerOptions _jsonOptions = new JsonSerializerOptions
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        PropertyNameCaseInsensitive = true,
        WriteIndented = true
    };

    /// <summary>
    /// Khởi tạo config với môi trường tự động từ IHostEnvironment
    /// </summary>
    private static MobiFoneInvoiceConfiguration InitializeConfig(MobiFoneInvoiceConfiguration config, IHostEnvironment hostEnvironment)
    {
        // Tự động set Environment từ ASP.NET Core Environment
        config.Environment = hostEnvironment.EnvironmentName;

        // Log để kiểm tra môi trường
        Console.WriteLine($"🌍 MobiFone Service Environment: {config.Environment}");
        Console.WriteLine($"🌍 ASP.NET Core Environment: {hostEnvironment.EnvironmentName}");
        Console.WriteLine($"🌍 Is Production: {config.IsProduction}");
        Console.WriteLine($"🌍 Is Production-like (Production/Staging): {config.IsProductionLike}");
        Console.WriteLine($"🌍 Is Development: {config.IsDevelopment}");

        return config;
    }

    /// <summary>
    /// Lấy base URL dựa trên môi trường
    /// </summary>
    private string GetMobiFoneApiUrl()
    {
        return _config.GetBaseUrl();
    }

    /// <summary>
    /// Lấy RefId mặc định dựa trên môi trường
    /// </summary>
    private string GetDefaultRefId()
    {
        return _config.GetRefId();
    }

    /// <summary>
    /// Tạo URL với tax_code parameter cho môi trường Production-like (Production và Staging)
    /// </summary>
    /// <param name="baseUrl">URL cơ bản</param>
    /// <param name="taxCode">Mã số thuế (bắt buộc cho Production và Staging)</param>
    /// <param name="apiName">Tên API để logging</param>
    /// <returns>URL đã được thêm tax_code parameter nếu là Production hoặc Staging</returns>
    private string BuildMobiFoneUrlWithTaxCode(string baseUrl, string? taxCode, string apiName = "")
    {
        if (!_config.IsProductionLike)
        {
            logger.LogInformation("Development environment - URL without tax_code: {Url}", baseUrl);
            return baseUrl;
        }

        // Production/Staging environment - tax_code là bắt buộc
        if (string.IsNullOrEmpty(taxCode))
        {
            logger.LogError("tax_code is required for Production/Staging environment API: {ApiName}", apiName);
            throw new ArgumentException($"tax_code is required for Production/Staging environment when calling {apiName} API", nameof(taxCode));
        }

        var separator = baseUrl.Contains("?") ? "&" : "?";
        var urlWithTaxCode = $"{baseUrl}{separator}tax_code={taxCode}";

        logger.LogInformation("Production/Staging environment - URL with tax_code: {Url} (API: {ApiName})", urlWithTaxCode, apiName);
        return urlWithTaxCode;
    }

    /// <summary>
    /// Setup headers cho MobiFone Invoice API request
    /// </summary>
    private void SetMobiFoneHeaders(string? token = null, string? maDvcs = null)
    {
        httpClient.DefaultRequestHeaders.Clear();
        if (!string.IsNullOrEmpty(token) && !string.IsNullOrEmpty(maDvcs))
        {
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Bear {token};{maDvcs}");
        }
    }

    /// <summary>
    /// Handle response chung cho MobiFone API calls
    /// </summary>
    private async Task<T> HandleMobiFoneApiResponse<T>(HttpResponseMessage response, string apiName, string endpoint) where T : class
    {
        var responseContent = await response.Content.ReadAsStringAsync();

        logger.LogInformation("MobiFone {ApiName} Response: {StatusCode} - {ResponseLength} chars: {ResponseContent}",
            apiName,
            response.StatusCode,
            responseContent.Length,
             responseContent);

        if (!response.IsSuccessStatusCode)
        {
            logger.LogWarning("MobiFone {ApiName} API returned non-success status: {StatusCode} - {Content}",
                apiName,
                response.StatusCode,
                responseContent);

            // Thử parse error response nếu có
            try
            {
                var errorResponse = JsonSerializer.Deserialize<LoginErrorResponse>(responseContent, _jsonOptions);
                if (errorResponse != null && !string.IsNullOrEmpty(errorResponse.error))
                {
                    throw new MobiFoneApiResponseException(apiName, errorResponse.error);
                }
            }
            catch (JsonException)
            {
                // Ignore error parsing, use default message
            }

            throw new MobiFoneApiResponseException(apiName, (int)response.StatusCode);
        }

        try
        {
            var result = JsonSerializer.Deserialize<T>(responseContent, _jsonOptions);

            // detect indicate "error" in responseContent then

            if (result == null)
            {
                logger.LogError("Failed to deserialize MobiFone {ApiName} response - result is null", apiName);
                throw new MobiFoneApiDeserializationException(apiName);
            }

            logger.LogInformation("MobiFone {ApiName} API call completed successfully", apiName);
            return result;
        }
        catch (JsonException ex)
        {
            var errorMessage = JsonSerializer.Deserialize<List<string>>(responseContent, _jsonOptions);

            if (errorMessage != null && errorMessage.Count > 0)
            {
                logger.LogError("JSON deserialization error for MobiFone {ApiName} API: {ErrorMessage}", apiName, string.Join(", ", errorMessage));
                // throw new MobiFoneApiDeserializationException(apiName, errorMessage);
            }

            logger.LogError(ex, "JSON deserialization error for MobiFone {ApiName} API", apiName);
            throw new MobiFoneApiDeserializationException(apiName, ex);
        }
    }

    /// <summary>
    /// Đăng nhập vào hệ thống MobiFone Invoice để lấy token
    /// </summary>
    public async Task<Response<LoginResponse>> LoginAsync(LoginRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Attempting to login to MobiFone Invoice API for user: {Username}", request.Username);

            var apiUrl = GetMobiFoneApiUrl();
            var endpoint = $"{apiUrl}/api/Account/Login"; // Login không cần tax_code

            // Tạo request body dựa trên môi trường
            object requestBody;
            if (_config.IsProductionLike)
            {
                // Production và Staging sử dụng cùng format với tax_code
                requestBody = new
                {
                    tax_code = request.TaxCode,
                    username = request.Username,
                    password = request.Password
                };
            }
            else
            {
                // Chỉ Development sử dụng format khác
                requestBody = new
                {
                    username = request.Username,
                    password = request.Password,
                    ma_dvcs = "",
                    is_sso_login = 1
                };
            }

            var json = JsonSerializer.Serialize(requestBody, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, string.Empty);
            httpRequestMessage.Content = content;
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone Login API: {CurlCommand}", curlString);

            logger.LogInformation("Calling MobiFone Login API for Username: {Username} - Environment: {Environment}",
                request.Username,
                _config.Environment);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);
            var result = await HandleMobiFoneApiResponse<LoginResponse>(response, "Login", endpoint);

            return new Response<LoginResponse>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while logging in to MobiFone Invoice API for user: {Username}",
                request.Username);
            return new Response<LoginResponse>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone Login API."
            };
        }
    }

    /// <summary>
    /// Lấy thông tin dải ký hiệu mẫu số hóa đơn
    /// </summary>
    public async Task<Response<GetDataReferencesResponse>> GetDataReferencesAsync(
        GetDataReferencesRequest request,
        string token,
        string maDvcs,
        string? taxCode,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Sử dụng RefId từ request hoặc RefId mặc định theo môi trường
            var refId = !string.IsNullOrEmpty(request.RefId) ? request.RefId : GetDefaultRefId();

            logger.LogInformation("Getting data references with RefId: {RefId} (Environment: {Environment})",
                refId, _config.Environment);

            var apiUrl = GetMobiFoneApiUrl();
            var baseUrl = $"{apiUrl}/api/System/GetDataReferencesByRefId?refId={refId}";
            var url = BuildMobiFoneUrlWithTaxCode(baseUrl, taxCode, "GetDataReferences");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, string.Empty);
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(url);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone GetDataReferences API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            logger.LogInformation("Calling MobiFone GetDataReferences API - RefId: {RefId}", request.RefId);

            var response = await httpClient.GetAsync(url, cancellationToken);

            var invoiceTemplates = await HandleMobiFoneApiResponse<List<InvoiceTemplateInfo>>(response, "GetDataReferences", url);

            var result = new GetDataReferencesResponse
            {
                Data = invoiceTemplates
            };

            logger.LogInformation("Successfully retrieved {Count} invoice templates", invoiceTemplates.Count);
            return new Response<GetDataReferencesResponse>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting data references from MobiFone Invoice API: {ex}", ex.Message);
            return new Response<GetDataReferencesResponse>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone GetDataReferences API."
            };
        }
    }

    /// <summary>
    /// Lấy thông tin CKS (chứng thư số)
    /// </summary>
    public async Task<Response<GetListCertificatesFile68Response>> GetListCertificatesFile68Async(
        string token,
        string maDvcs,
        string? taxCode,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Getting list certificates file 68");

            var apiUrl = GetMobiFoneApiUrl();
            var baseUrl = $"{apiUrl}/api/Invoice68/GetListCertificatesFile68";
            var url = BuildMobiFoneUrlWithTaxCode(baseUrl, taxCode, "GetListCertificatesFile68");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, string.Empty);
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(url);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone GetListCertificatesFile68 API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            logger.LogInformation("Calling MobiFone GetListCertificatesFile68 API");

            var response = await httpClient.GetAsync(url, cancellationToken);

            var certificates = await HandleMobiFoneApiResponse<List<CertificateInfo>>(response, "GetListCertificatesFile68", url);

            var result = new GetListCertificatesFile68Response
            {
                Data = certificates
            };

            logger.LogInformation("Successfully retrieved {Count} certificates", certificates.Count);
            return new Response<GetListCertificatesFile68Response>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting certificates from MobiFone Invoice API: {ex}", ex.Message);
            return new Response<GetListCertificatesFile68Response>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone GetListCertificatesFile68 API."
            };
        }
    }

    /// <summary>
    /// Tạo mới hóa đơn quy trình thường (tất cả hình thức HĐ) - Hóa đơn Giá trị gia tăng
    /// </summary>
    public async Task<Response<List<SaveListHoadon78Response>>> CreateInvoiceAsync(
        SaveListHoadon78Request request,
        string token,
        string maDvcs,
        string? taxCode,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Calling MobiFone CreateInvoice API - EditMode: {EditMode}, Data count: {DataCount}, TaxCode: {TaxCode} (Environment: {Environment})",
                request.editmode, request.data?.Count ?? 0, taxCode, _config.Environment);

            // Tạo hóa đơn qua MobiFone API (business logic đã được validate ở Command Handler)
            var apiUrl = GetMobiFoneApiUrl();
            var baseEndpoint = $"{apiUrl}/api/Invoice68/SaveListHoadon78";
            var endpoint = BuildMobiFoneUrlWithTaxCode(baseEndpoint, taxCode, "CreateInvoice");

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, string.Empty);
            httpRequestMessage.Content = content;
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone CreateInvoice API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            logger.LogInformation("Calling MobiFone CreateInvoice API - EditMode: {EditMode}",
                request.editmode);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);
            var result = await HandleMobiFoneApiResponse<List<SaveListHoadon78Response>>(response, "CreateInvoice", endpoint);

            logger.LogInformation("Successfully created invoice with response: {Count} items", result.Count);

            // TODO: Move ProcessSuccessfulInvoiceCreation logic to Command Handler
            // Business logic (save to InvoiceInfo, update MerchantInvoiceOrder) should be handled in Application layer

            return new Response<List<SaveListHoadon78Response>>(result ?? []);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while creating invoice in MobiFone Invoice API: {ex}", ex.Message);
            return new Response<List<SaveListHoadon78Response>>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone CreateInvoice API."
            };
        }
    }

    /// <summary>
    /// Tạo mới Hóa đơn Bán hàng
    /// </summary>
    public async Task<Response<List<SaveListHoadonBanHangResponse>>> CreateSalesInvoiceAsync(
        SaveListHoadonBanHangRequest request,
        string token,
        string maDvcs,
        string? taxCode,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Creating sales invoice with EditMode: {EditMode}, Data count: {DataCount} (Environment: {Environment})",
                request.editmode, request.data?.Count ?? 0, _config.Environment);

            var apiUrl = GetMobiFoneApiUrl();
            var baseEndpoint = $"{apiUrl}/api/Invoice68/SaveListHoadon78";
            var endpoint = BuildMobiFoneUrlWithTaxCode(baseEndpoint, taxCode, "CreateSalesInvoice");

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, string.Empty);
            httpRequestMessage.Content = content;
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone CreateSalesInvoice API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            logger.LogInformation("Calling MobiFone CreateSalesInvoice API - EditMode: {EditMode}",
                request.editmode);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);
            var result = await HandleMobiFoneApiResponse<List<SaveListHoadonBanHangResponse>>(response, "CreateSalesInvoice", endpoint);

            logger.LogInformation("Successfully created sales invoice with response ok: {Count} items", result.Count);
            return new Response<List<SaveListHoadonBanHangResponse>>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while creating sales invoice in MobiFone Invoice API: {ex}", ex.Message);
            return new Response<List<SaveListHoadonBanHangResponse>>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone CreateSalesInvoice API."
            };
        }
    }

    // ===== METHODS VỚI TÊN TIẾNG VIỆT =====

    /// <summary>
    /// Tạo mới Hóa đơn Giá trị gia tăng quy trình thường
    /// </summary>
    public async Task<Response<List<SaveListHoadon78Response>>> TaoMoiHoaDonGTGTQuyTrinhThuongAsync(
        SaveListHoadon78Request request,
        string token,
        string maDvcs,
        string? taxCode,
        CancellationToken cancellationToken = default)
    {
        // Delegate to existing method with taxCode
        return await CreateInvoiceAsync(request, token, maDvcs, taxCode, cancellationToken);
    }

    /// <summary>
    /// Tạo mới Hóa đơn Bán hàng quy trình thường
    /// </summary>
    public async Task<Response<List<SaveListHoadonBanHangResponse>>> TaoMoiHoaDonBanHangQuyTrinhThuongAsync(
        SaveListHoadonBanHangRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default)
    {
        // Delegate to existing method
        return await CreateSalesInvoiceAsync(request, token, maDvcs, null, cancellationToken);
    }

    /// <summary>
    /// Tạo mới Hóa đơn Bán tài sản công quy trình thường
    /// </summary>
    public async Task<Response<List<SaveListHoadonBanTaiSanCongResponse>>> TaoMoiHoaDonBanTaiSanCongQuyTrinhThuongAsync(
        SaveListHoadonBanTaiSanCongRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Creating public asset sales invoice with EditMode: {EditMode}, Data count: {DataCount}",
                request.editmode, request.data?.Count ?? 0);

            var apiUrl = GetMobiFoneApiUrl();
            var baseEndpoint = $"{apiUrl}/api/Invoice68/SaveListHoadon78";
            var endpoint = BuildMobiFoneUrlWithTaxCode(baseEndpoint, null, "CreatePublicAssetSalesInvoice");

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, string.Empty);
            httpRequestMessage.Content = content;
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone CreatePublicAssetSalesInvoice API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            logger.LogInformation("Calling MobiFone CreatePublicAssetSalesInvoice API - EditMode: {EditMode}",
                request.editmode);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);
            var result = await HandleMobiFoneApiResponse<List<SaveListHoadonBanTaiSanCongResponse>>(response, "CreatePublicAssetSalesInvoice", endpoint);

            logger.LogInformation("Successfully created public asset sales invoice with response: {Count} items", result.Count);
            return new Response<List<SaveListHoadonBanTaiSanCongResponse>>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while creating public asset sales invoice in MobiFone Invoice API: {ex}", ex.Message);
            return new Response<List<SaveListHoadonBanTaiSanCongResponse>>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone CreatePublicAssetSalesInvoice API."
            };
        }
    }

    /// <summary>
    /// Tạo mới Hóa đơn Bán hàng dự trữ quốc gia quy trình thường
    /// </summary>
    public async Task<Response<List<SaveListHoadonBanHangDuTruQuocGiaResponse>>> TaoMoiHoaDonBanHangDuTruQuocGiaQuyTrinhThuongAsync(
        SaveListHoadonBanHangDuTruQuocGiaRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Creating national reserve sales invoice with EditMode: {EditMode}, Data count: {DataCount}",
                request.editmode, request.data?.Count ?? 0);

            var apiUrl = GetMobiFoneApiUrl();
            var baseEndpoint = $"{apiUrl}/api/Invoice68/SaveListHoadon78";
            var endpoint = BuildMobiFoneUrlWithTaxCode(baseEndpoint, null, "CreateNationalReserveSalesInvoice");

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, string.Empty);
            httpRequestMessage.Content = content;
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone CreateNationalReserveSalesInvoice API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            logger.LogInformation("Calling MobiFone CreateNationalReserveSalesInvoice API - EditMode: {EditMode}",
                request.editmode);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);
            var result = await HandleMobiFoneApiResponse<List<SaveListHoadonBanHangDuTruQuocGiaResponse>>(response, "CreateNationalReserveSalesInvoice", endpoint);

            logger.LogInformation("Successfully created national reserve sales invoice with response: {Count} items", result.Count);
            return new Response<List<SaveListHoadonBanHangDuTruQuocGiaResponse>>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while creating national reserve sales invoice in MobiFone Invoice API: {ex}", ex.Message);
            return new Response<List<SaveListHoadonBanHangDuTruQuocGiaResponse>>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone CreateNationalReserveSalesInvoice API."
            };
        }
    }

    /// <summary>
    /// Tạo mới Hóa đơn khác (Tem, vé, thẻ, phiếu...) quy trình thường
    /// </summary>
    public async Task<Response<List<SaveListHoaDonKhacResponse>>> TaoMoiHoaDonKhacQuyTrinhThuongAsync(
        SaveListHoaDonKhacRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Creating other invoice with EditMode: {EditMode}, Data count: {DataCount}",
                request.editmode, request.data?.Count ?? 0);

            var apiUrl = GetMobiFoneApiUrl();
            var baseEndpoint = $"{apiUrl}/api/Invoice68/SaveListHoadon78";
            var endpoint = BuildMobiFoneUrlWithTaxCode(baseEndpoint, null, "CreateOtherInvoice");

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, string.Empty);
            httpRequestMessage.Content = content;
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone CreateOtherInvoice API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            logger.LogInformation("Calling MobiFone CreateOtherInvoice API - EditMode: {EditMode}",
                request.editmode);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);
            var result = await HandleMobiFoneApiResponse<List<SaveListHoaDonKhacResponse>>(response, "CreateOtherInvoice", endpoint);

            logger.LogInformation("Successfully created other invoice with response: {Count} items", result.Count);
            return new Response<List<SaveListHoaDonKhacResponse>>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while creating other invoice in MobiFone Invoice API: {ex}", ex.Message);
            return new Response<List<SaveListHoaDonKhacResponse>>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone CreateOtherInvoice API."
            };
        }
    }

    /// <summary>
    /// Tạo mới Phiếu xuất kho kiêm vận chuyển nội bộ (PXKVCNB) quy trình thường
    /// </summary>
    public async Task<Response<List<SaveListHoadonPXKVCNBResponse>>> TaoMoiHoaDonPXKVCNBQuyTrinhThuongAsync(
        SaveListHoadonPXKVCNBRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Creating PXKVCNB invoice with EditMode: {EditMode}, Data count: {DataCount}",
                request.editmode, request.data?.Count ?? 0);

            var apiUrl = GetMobiFoneApiUrl();
            var baseEndpoint = $"{apiUrl}/api/Invoice68/SaveListHoadon78";
            var endpoint = BuildMobiFoneUrlWithTaxCode(baseEndpoint, null, "CreatePXKVCNBInvoice");

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, string.Empty);
            httpRequestMessage.Content = content;
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone CreatePXKVCNBInvoice API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            logger.LogInformation("Calling MobiFone CreatePXKVCNBInvoice API - EditMode: {EditMode}",
                request.editmode);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);
            var result = await HandleMobiFoneApiResponse<List<SaveListHoadonPXKVCNBResponse>>(response, "CreatePXKVCNBInvoice", endpoint);

            logger.LogInformation("Successfully created PXKVCNB invoice with response: {Count} items", result.Count);
            return new Response<List<SaveListHoadonPXKVCNBResponse>>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while creating PXKVCNB invoice in MobiFone Invoice API: {ex}", ex.Message);
            return new Response<List<SaveListHoadonPXKVCNBResponse>>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone CreatePXKVCNBInvoice API."
            };
        }
    }

    /// <summary>
    /// Tạo mới Phiếu xuất kho hàng gửi bán đại lý (PXKDL) quy trình thường
    /// </summary>
    public async Task<Response<List<SaveListHoadonPXKDLResponse>>> TaoMoiHoaDonPXKDLQuyTrinhThuongAsync(
        SaveListHoadonPXKDLRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Creating PXKDL invoice with EditMode: {EditMode}, Data count: {DataCount}",
                request.editmode, request.data?.Count ?? 0);

            var apiUrl = GetMobiFoneApiUrl();
            var endpoint = $"{apiUrl}/api/Invoice68/SaveListHoadon78";

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, string.Empty);
            httpRequestMessage.Content = content;
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone CreatePXKDLInvoice API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            logger.LogInformation("Calling MobiFone CreatePXKDLInvoice API - EditMode: {EditMode}",
                request.editmode);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);
            var result = await HandleMobiFoneApiResponse<List<SaveListHoadonPXKDLResponse>>(response, "CreatePXKDLInvoice", endpoint);

            logger.LogInformation("Successfully created PXKDL invoice with response: {Count} items", result.Count);
            return new Response<List<SaveListHoadonPXKDLResponse>>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while creating PXKDL invoice in MobiFone Invoice API: {ex}", ex.Message);
            return new Response<List<SaveListHoadonPXKDLResponse>>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone CreatePXKDLInvoice API."
            };
        }
    }

    // ===== METHODS CHO MÁY TÍNH TIỀN SINH MÃ (MTT) =====

    /// <summary>
    /// Tạo mới Hóa đơn Giá trị gia tăng máy tính tiền sinh mã
    /// </summary>
    public async Task<Response<List<SaveListHoadon78MTTResponse>>> TaoMoiHoaDonGTGTMayTinhTienSinhMaAsync(
        SaveListHoadon78MTTRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Creating GTGT MTT invoice with EditMode: {EditMode}, Data count: {DataCount}",
                request.editmode, request.data?.Count ?? 0);

            var apiUrl = GetMobiFoneApiUrl();
            var endpoint = $"{apiUrl}/api/Invoice68/SaveListHoadon78MTT";

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, string.Empty);
            httpRequestMessage.Content = content;
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone CreateGTGTMTTInvoice API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            logger.LogInformation("Calling MobiFone CreateGTGTMTTInvoice API - EditMode: {EditMode}",
                request.editmode);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);
            var result = await HandleMobiFoneApiResponse<List<SaveListHoadon78MTTResponse>>(response, "CreateGTGTMTTInvoice", endpoint);

            logger.LogInformation("Successfully created GTGT MTT invoice with response: {Count} items", result.Count);
            return new Response<List<SaveListHoadon78MTTResponse>>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while creating GTGT MTT invoice in MobiFone Invoice API: {ex}", ex.Message);
            return new Response<List<SaveListHoadon78MTTResponse>>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone CreateGTGTMTTInvoice API."
            };
        }
    }

    /// <summary>
    /// Tạo mới Hóa đơn Bán hàng máy tính tiền sinh mã
    /// </summary>
    public async Task<Response<List<SaveListHoadonBanHangMTTResponse>>> TaoMoiHoaDonBanHangMayTinhTienSinhMaAsync(
        SaveListHoadonBanHangMTTRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Creating Sales MTT invoice with EditMode: {EditMode}, Data count: {DataCount}",
                request.editmode, request.data?.Count ?? 0);

            var apiUrl = GetMobiFoneApiUrl();
            var endpoint = $"{apiUrl}/api/Invoice68/SaveListHoadon78MTT";

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, string.Empty);
            httpRequestMessage.Content = content;
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone CreateSalesMTTInvoice API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            logger.LogInformation("Calling MobiFone CreateSalesMTTInvoice API - EditMode: {EditMode}",
                request.editmode);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);
            var result = await HandleMobiFoneApiResponse<List<SaveListHoadonBanHangMTTResponse>>(response, "CreateSalesMTTInvoice", endpoint);

            logger.LogInformation("Successfully created Sales MTT invoice with response: {Count} items", result.Count);
            return new Response<List<SaveListHoadonBanHangMTTResponse>>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while creating Sales MTT invoice in MobiFone Invoice API: {ex}", ex.Message);
            return new Response<List<SaveListHoadonBanHangMTTResponse>>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone CreateSalesMTTInvoice API."
            };
        }
    }

    /// <summary>
    /// Tạo mới Hóa đơn khác (Tem, vé, thẻ, phiếu...) máy tính tiền sinh mã
    /// </summary>
    public async Task<Response<List<SaveListHoaDonKhacMTTResponse>>> TaoMoiHoaDonKhacMayTinhTienSinhMaAsync(
        SaveListHoaDonKhacMTTRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Creating Other MTT invoice with EditMode: {EditMode}, Data count: {DataCount}",
                request.editmode, request.data?.Count ?? 0);

            var apiUrl = GetMobiFoneApiUrl();
            var endpoint = $"{apiUrl}/api/Invoice68/SaveListHoadon78MTT";

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, string.Empty);
            httpRequestMessage.Content = content;
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone CreateOtherMTTInvoice API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            logger.LogInformation("Calling MobiFone CreateOtherMTTInvoice API - EditMode: {EditMode}",
                request.editmode);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);
            var result = await HandleMobiFoneApiResponse<List<SaveListHoaDonKhacMTTResponse>>(response, "CreateOtherMTTInvoice", endpoint);

            logger.LogInformation("Successfully created Other MTT invoice with response: {Count} items", result.Count);
            return new Response<List<SaveListHoaDonKhacMTTResponse>>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while creating Other MTT invoice in MobiFone Invoice API: {ex}", ex.Message);
            return new Response<List<SaveListHoaDonKhacMTTResponse>>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone CreateOtherMTTInvoice API."
            };
        }
    }

    // ===== API 4.4: SAVE AND SIGN HOADON78 =====

    /// <summary>
    /// API 4.4: Hàm tạo mới và ký gửi hóa đơn bằng HSM nhà cung cấp khác, file mềm, Sim PKI
    /// URL: {{base_url}}/api/Invoice68/SaveAndSignHoadon78
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// </summary>
    public async Task<Response<List<SaveAndSignHoadon78Response>>> SaveAndSignHoadon78Async(
        SaveAndSignHoadon78Request request,
        string token,
        string maDvcs,
        string? taxCode,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("SaveAndSignHoadon78 with EditMode: {EditMode}, TypeCmd: {TypeCmd}, GuiCQT: {GuiCQT}, Data count: {DataCount}",
                request.editmode, request.type_cmd, request.guiCQT, request.data?.Count ?? 0);

            var apiUrl = GetMobiFoneApiUrl();
            var baseEndpoint = $"{apiUrl}/api/Invoice68/SaveAndSignHoadon78";
            var endpoint = BuildMobiFoneUrlWithTaxCode(baseEndpoint, taxCode, "SaveAndSignHoadon78");

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, string.Empty);
            httpRequestMessage.Content = content;
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone SaveAndSignHoadon78 API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            logger.LogInformation("Calling MobiFone SaveAndSignHoadon78 API - EditMode: {EditMode}, TypeCmd: {TypeCmd}",
                request.editmode, request.type_cmd);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);
            var result = await HandleMobiFoneApiResponse<List<SaveAndSignHoadon78Response>>(response, "SaveAndSignHoadon78", endpoint);

            logger.LogInformation("Successfully executed SaveAndSignHoadon78 with response: {Count} items", result.Count);
            return new Response<List<SaveAndSignHoadon78Response>>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while executing SaveAndSignHoadon78 in MobiFone Invoice API: {ex}", ex.Message);
            return new Response<List<SaveAndSignHoadon78Response>>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone SaveAndSignHoadon78 API."
            };
        }
    }

    // ===== API 4.5: SIGN INVOICE CERT FILE 68 =====

    /// <summary>
    /// API 4.5: Ký chờ xử lý hóa đơn (bằng file mềm, SIM)
    /// URL: {{base_url}}/api/Invoice68/SignInvoiceCertFile68
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: Hàm này cho phép ký hóa đơn nhưng chưa gửi hóa đơn lên Cơ quan Thuế
    /// </summary>
    public async Task<Response<SignInvoiceCertFile68Response>> SignInvoiceCertFile68Async(
        SignInvoiceCertFile68Request request,
        string token,
        string maDvcs,
        string? taxCode,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("SignInvoiceCertFile68 with Data count: {DataCount}",
                request.data?.Count ?? 0);

            var apiUrl = GetMobiFoneApiUrl();
            var baseEndpoint = $"{apiUrl}/api/Invoice68/SignInvoiceCertFile68";
            var endpoint = BuildMobiFoneUrlWithTaxCode(baseEndpoint, taxCode, "SignInvoiceCertFile68");

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, string.Empty);
            httpRequestMessage.Content = content;
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone SignInvoiceCertFile68 API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            logger.LogInformation("Calling MobiFone SignInvoiceCertFile68 API - Data count: {DataCount}",
                request.data?.Count ?? 0);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);
            var result = await HandleMobiFoneApiResponse<SignInvoiceCertFile68Response>(response, "SignInvoiceCertFile68", endpoint);

            logger.LogInformation("Successfully executed SignInvoiceCertFile68 with status: {Status}", result.tthai);
            return new Response<SignInvoiceCertFile68Response>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while executing SignInvoiceCertFile68 in MobiFone Invoice API: {ex}", ex.Message);
            return new Response<SignInvoiceCertFile68Response>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone SignInvoiceCertFile68 API."
            };
        }
    }

    // ===== API 4.6: SEND INVOICE TO CQT 68 =====

    /// <summary>
    /// API 4.6: Gửi hóa đơn đã ký lên Cơ quan thuế
    /// URL: {{base_url}}/api/Invoice68/SendInvoiceToCQT68
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: Hàm này cho phép gửi hóa đơn đã được ký tới Cơ quan Thuế
    /// </summary>
    public async Task<Response<SendInvoiceToCQT68Response>> SendInvoiceToCQT68Async(
        SendInvoiceToCQT68Request request,
        string token,
        string maDvcs,
        string? taxCode,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("SendInvoiceToCQT68 with Invoice count: {InvoiceCount}, TypeCmd: {TypeCmd}",
                request.invs?.Count ?? 0, request.type_cmd);

            var apiUrl = GetMobiFoneApiUrl();
            var baseEndpoint = $"{apiUrl}/api/Invoice68/SendInvoiceToCQT68";
            var endpoint = BuildMobiFoneUrlWithTaxCode(baseEndpoint, taxCode, "SendInvoiceToCQT68");

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, string.Empty);
            httpRequestMessage.Content = content;
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone SendInvoiceToCQT68 API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            logger.LogInformation("Calling MobiFone SendInvoiceToCQT68 API - Invoice count: {InvoiceCount}",
                request.invs?.Count ?? 0);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);
            var result = await HandleMobiFoneApiResponse<SendInvoiceToCQT68Response>(response, "SendInvoiceToCQT68", endpoint);

            logger.LogInformation("Successfully executed SendInvoiceToCQT68 with status: {Status}", result.trang_thai);
            return new Response<SendInvoiceToCQT68Response>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while executing SendInvoiceToCQT68 in MobiFone Invoice API: {ex}", ex.Message);
            return new Response<SendInvoiceToCQT68Response>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone SendInvoiceToCQT68 API."
            };
        }
    }

    // ===== API 4.7: SIGN AND SEND INVOICE TO CQT 68 =====

    /// <summary>
    /// API 4.7: Ký và gửi hóa đơn tới CQT (Chỉ dành cho file mềm, SIM)
    /// URL: {{base_url}}/api/Invoice68/SignInvoiceCertFile68
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: Hàm này cho phép ký và gửi hóa đơn lên Cơ quan thuế trong cùng một API
    /// </summary>
    public async Task<Response<SignAndSendInvoiceToCQT68Response>> SignAndSendInvoiceToCQT68Async(
        SignAndSendInvoiceToCQT68Request request,
        string token,
        string maDvcs,
        string? taxCode,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("SignAndSendInvoiceToCQT68 with Data count: {DataCount}",
                request.data?.Count ?? 0);

            var apiUrl = GetMobiFoneApiUrl();
            var baseEndpoint = $"{apiUrl}/api/Invoice68/SignInvoiceCertFile68";
            var endpoint = BuildMobiFoneUrlWithTaxCode(baseEndpoint, taxCode, "SignAndSendInvoiceToCQT68");

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, string.Empty);
            httpRequestMessage.Content = content;
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone SignAndSendInvoiceToCQT68 API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            logger.LogInformation("Calling MobiFone SignAndSendInvoiceToCQT68 API - Data count: {DataCount}",
                request.data?.Count ?? 0);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);
            var result = await HandleMobiFoneApiResponse<SignAndSendInvoiceToCQT68Response>(response, "SignAndSendInvoiceToCQT68", endpoint);

            logger.LogInformation("Successfully executed SignAndSendInvoiceToCQT68 with sign status: {SignStatus}, send status: {SendStatus}",
                result.tthai, result.trang_thai);
            return new Response<SignAndSendInvoiceToCQT68Response>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while executing SignAndSendInvoiceToCQT68 in MobiFone Invoice API: {ex}", ex.Message);
            return new Response<SignAndSendInvoiceToCQT68Response>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone SignAndSendInvoiceToCQT68 API."
            };
        }
    }

    // ===== API 4.20: GET HISTORY INVOICE =====

    /// <summary>
    /// API 4.20: Lấy danh sách lịch sử hóa đơn theo ID
    /// URL: {{base_url}}/api/Invoice68/GetHistoryInvoice?id={hdon_id}
    /// Method: GET
    /// Authorization: Bear Token;ma_dvcs
    /// Mô tả: Hàm này cho phép lấy toàn bộ lịch sử truyền nhận dữ liệu của hóa đơn với Tổng cục thuế
    /// </summary>
    public async Task<Response<List<GetHistoryInvoiceResponse>>> GetHistoryInvoiceAsync(
        string id,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("GetHistoryInvoice for invoice ID: {InvoiceId}", id);

            var apiUrl = GetMobiFoneApiUrl();
            var endpoint = $"{apiUrl}/api/Invoice68/GetHistoryInvoice?id={id}";

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, string.Empty);
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone GetHistoryInvoice API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            logger.LogInformation("Calling MobiFone GetHistoryInvoice API for invoice: {InvoiceId}", id);

            var response = await httpClient.GetAsync(endpoint, cancellationToken);
            var result = await HandleMobiFoneApiResponse<List<GetHistoryInvoiceResponse>>(response, "GetHistoryInvoice", endpoint);

            logger.LogInformation("Successfully retrieved history for invoice {InvoiceId} with {Count} records",
                id, result?.Count ?? 0);
            return new Response<List<GetHistoryInvoiceResponse>>(result ?? new List<GetHistoryInvoiceResponse>());
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting history for invoice {InvoiceId} in MobiFone Invoice API: {ex}",
                id, ex.Message);
            return new Response<List<GetHistoryInvoiceResponse>>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone GetHistoryInvoice API."
            };
        }
    }

    // ===== API 4.8: SEND INVOICE BY EMAIL =====

    /// <summary>
    /// API 4.8: Gửi mail phát hành hóa đơn cho người mua
    /// URL: {{base_url}}/api/Invoice68/AutoSendInvoiceByEmail
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// </summary>
    public async Task<Response<SendInvoiceByEmailResponse>> SendInvoiceByEmailAsync(
        SendInvoiceByEmailRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("SendInvoiceByEmail for invoice ID: {InvoiceId} to recipients: {Recipients}",
                request.id, request.nguoinhan);

            var apiUrl = GetMobiFoneApiUrl();
            var endpoint = $"{apiUrl}/api/Invoice68/AutoSendInvoiceByEmail";

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, string.Empty);
            httpRequestMessage.Content = content;
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone SendInvoiceByEmail API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);
            var result = await HandleMobiFoneApiResponse<SendInvoiceByEmailResponse>(response, "SendInvoiceByEmail", endpoint);

            logger.LogInformation("Successfully sent email for invoice {InvoiceId}", request.id);
            return new Response<SendInvoiceByEmailResponse>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while sending email for invoice {InvoiceId} in MobiFone Invoice API: {ex}",
                request.id, ex.Message);
            return new Response<SendInvoiceByEmailResponse>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone SendInvoiceByEmail API."
            };
        }
    }

    // ===== API 4.9: DOWNLOAD INVOICE PDF =====

    /// <summary>
    /// API 4.9: Tải hóa đơn File .PDF
    /// URL: {{base_url}}/api/Invoice68/inHoadon?id={hdon_id}&type=PDF&inchuyendoi=false
    /// Method: GET
    /// Authorization: Bear Token;ma_dvcs
    /// </summary>
    public async Task<Response<byte[]>> DownloadInvoicePDFAsync(
        DownloadInvoicePDFRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("DownloadInvoicePDF for invoice ID: {InvoiceId}, type: {Type}, inchuyendoi: {InChuyenDoi}",
                request.id, request.type, request.inchuyendoi);

            var apiUrl = GetMobiFoneApiUrl();
            var endpoint = $"{apiUrl}/api/Invoice68/inHoadon?id={request.id}&type={request.type}&inchuyendoi={request.inchuyendoi.ToString().ToLower()}";

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, string.Empty);
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone DownloadInvoicePDF API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            var response = await httpClient.GetAsync(endpoint, cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var pdfBytes = await response.Content.ReadAsByteArrayAsync(cancellationToken);
                logger.LogInformation("Successfully downloaded PDF for invoice {InvoiceId}, size: {Size} bytes",
                    request.id, pdfBytes.Length);
                return new Response<byte[]>(pdfBytes);
            }

            var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
            logger.LogWarning("Failed to download PDF for invoice {InvoiceId}. Status: {StatusCode}, Content: {Content}",
                request.id, response.StatusCode, errorContent);

            return new Response<byte[]>
            {
                Code = ErrorCodes.GENERAL_ERROR,
                Message = $"Failed to download PDF. Status: {response.StatusCode}"
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while downloading PDF for invoice {InvoiceId} in MobiFone Invoice API: {ex}",
                request.id, ex.Message);
            return new Response<byte[]>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone DownloadInvoicePDF API."
            };
        }
    }

    // ===== API 4.10: PRINT MULTIPLE INVOICES =====

    /// <summary>
    /// API 4.10: In nhiều hóa đơn
    /// URL: {{base_url}}/api/Invoice68/InDanhSachHoaDon
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// </summary>
    public async Task<Response<byte[]>> PrintMultipleInvoicesAsync(
        PrintMultipleInvoicesRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("PrintMultipleInvoices for {Count} invoices, type: {Type}, inchuyendoi: {InChuyenDoi}",
                request.invs.Count, request.type, request.inchuyendoi);

            var apiUrl = GetMobiFoneApiUrl();
            var endpoint = $"{apiUrl}/api/Invoice68/InDanhSachHoaDon";

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, string.Empty);
            httpRequestMessage.Content = content;
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone PrintMultipleInvoices API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var pdfBytes = await response.Content.ReadAsByteArrayAsync(cancellationToken);
                logger.LogInformation("Successfully printed {Count} invoices, PDF size: {Size} bytes",
                    request.invs.Count, pdfBytes.Length);
                return new Response<byte[]>(pdfBytes);
            }

            var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
            logger.LogWarning("Failed to print multiple invoices. Status: {StatusCode}, Content: {Content}",
                response.StatusCode, errorContent);

            return new Response<byte[]>
            {
                Code = ErrorCodes.GENERAL_ERROR,
                Message = $"Failed to print multiple invoices. Status: {response.StatusCode}"
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while printing multiple invoices in MobiFone Invoice API: {ex}",
                ex.Message);
            return new Response<byte[]>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone PrintMultipleInvoices API."
            };
        }
    }

    // ===== API 4.11: DELETE UNSIGNED INVOICE =====

    /// <summary>
    /// API 4.11: Xóa hóa đơn chưa ký gửi
    /// URL: {{base_url}}/api/Invoice68/hoadonXoaNhieu
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// </summary>
    public async Task<Response<DeleteUnsignedInvoiceResponse>> DeleteUnsignedInvoiceAsync(
        DeleteUnsignedInvoiceRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("DeleteUnsignedInvoice for {Count} invoices", request.data.Count);

            var apiUrl = GetMobiFoneApiUrl();
            var endpoint = $"{apiUrl}/api/Invoice68/hoadonXoaNhieu";

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, string.Empty);
            httpRequestMessage.Content = content;
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone DeleteUnsignedInvoice API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);
            var result = await HandleMobiFoneApiResponse<DeleteUnsignedInvoiceResponse>(response, "DeleteUnsignedInvoice", endpoint);

            logger.LogInformation("Successfully deleted {Count} unsigned invoices", request.data.Count);
            return new Response<DeleteUnsignedInvoiceResponse>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while deleting unsigned invoices in MobiFone Invoice API: {ex}",
                ex.Message);
            return new Response<DeleteUnsignedInvoiceResponse>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone DeleteUnsignedInvoice API."
            };
        }
    }

    // ===== API 4.12: CANCEL INVOICE WITHOUT CODE =====

    /// <summary>
    /// API 4.12: Hủy hóa đơn không mã
    /// URL: {{base_url}}/api/Invoice68/uploadCanceledInv?id={}
    /// Method: GET
    /// Authorization: Bear Token;ma_dvcs
    /// </summary>
    public async Task<Response<CancelInvoiceWithoutCodeResponse>> CancelInvoiceWithoutCodeAsync(
        CancelInvoiceWithoutCodeRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("CancelInvoiceWithoutCode for invoice ID: {InvoiceId}", request.id);

            var apiUrl = GetMobiFoneApiUrl();
            var endpoint = $"{apiUrl}/api/Invoice68/uploadCanceledInv?id={request.id}";

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, string.Empty);
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone CancelInvoiceWithoutCode API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            var response = await httpClient.GetAsync(endpoint, cancellationToken);
            var result = await HandleMobiFoneApiResponse<CancelInvoiceWithoutCodeResponse>(response, "CancelInvoiceWithoutCode", endpoint);

            logger.LogInformation("Successfully cancelled invoice without code {InvoiceId}", request.id);
            return new Response<CancelInvoiceWithoutCodeResponse>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while cancelling invoice without code {InvoiceId} in MobiFone Invoice API: {ex}",
                request.id, ex.Message);
            return new Response<CancelInvoiceWithoutCodeResponse>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone CancelInvoiceWithoutCode API."
            };
        }
    }

    // ===== API 4.13: GET INVOICE BY ID =====

    /// <summary>
    /// API 4.13: Lấy thông tin hóa đơn theo ID
    /// URL: {{base_url}}/api/Invoice68/GetById?id={}
    /// Method: GET
    /// Authorization: Bear Token;ma_dvcs
    /// </summary>
    public async Task<Response<GetInvoiceByIdResponse>> GetInvoiceByIdAsync(
        GetInvoiceByIdRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("GetInvoiceById for invoice ID: {InvoiceId}", request.id);

            var apiUrl = GetMobiFoneApiUrl();
            var endpoint = $"{apiUrl}/api/Invoice68/GetById?id={request.id}";

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, string.Empty);
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone GetInvoiceById API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            var response = await httpClient.GetAsync(endpoint, cancellationToken);
            var result = await HandleMobiFoneApiResponse<GetInvoiceByIdResponse>(response, "GetInvoiceById", endpoint);

            logger.LogInformation("Successfully retrieved invoice details for {InvoiceId}", request.id);
            return new Response<GetInvoiceByIdResponse>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting invoice by ID {InvoiceId} in MobiFone Invoice API: {ex}",
                request.id, ex.Message);
            return new Response<GetInvoiceByIdResponse>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone GetInvoiceById API."
            };
        }
    }

    // ===== API 4.14: GET HOADON FKEY =====

    /// <summary>
    /// API 4.14: Lấy danh sách hóa đơn theo FKEY hoặc Khoảng thời gian
    /// URL: {{base_url}}/api/Invoice68/GetHoadonFkey
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// </summary>
    public async Task<Response<GetHoadonFkeyResponse>> GetHoadonFkeyAsync(
        GetHoadonFkeyRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("GetHoadonFkey with FKEY: {FKey}, DateRange: {FromDate} - {ToDate}",
                request.hdon_id, request.tu_ngay, request.den_ngay);

            var apiUrl = GetMobiFoneApiUrl();
            var endpoint = $"{apiUrl}/api/Invoice68/GetHoadonFkey";

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, string.Empty);
            httpRequestMessage.Content = content;
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone GetHoadonFkey API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);
            var result = await HandleMobiFoneApiResponse<GetHoadonFkeyResponse>(response, "GetHoadonFkey", endpoint);

            logger.LogInformation("Successfully retrieved {Count} invoices by FKEY/DateRange", result?.Count ?? 0);
            return new Response<GetHoadonFkeyResponse>(result ?? new GetHoadonFkeyResponse());
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting invoices by FKEY/DateRange in MobiFone Invoice API: {ex}",
                ex.Message);
            return new Response<GetHoadonFkeyResponse>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone GetHoadonFkey API."
            };
        }
    }

    // ===== API 4.17: GET INVOICE BY TIME AND UNIT =====

    /// <summary>
    /// API 4.17: Lấy danh sách hoá đơn theo thời gian, đơn vị và trạng thái
    /// URL: {{base_url}}/api/Invoice68/GetInvoiceByTimeAndUnit
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// </summary>
    public async Task<Response<GetInvoiceByTimeAndUnitResponse>> GetInvoiceByTimeAndUnitAsync(
        GetInvoiceByTimeAndUnitRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("GetInvoiceByTimeAndUnit with DateRange: {FromDate} - {ToDate}",
                request.tu_ngay, request.den_ngay);

            var apiUrl = GetMobiFoneApiUrl();
            var endpoint = $"{apiUrl}/api/Invoice68/GetInvoiceByTimeAndUnit";

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, string.Empty);
            httpRequestMessage.Content = content;
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone GetInvoiceByTimeAndUnit API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);
            var result = await HandleMobiFoneApiResponse<GetInvoiceByTimeAndUnitResponse>(response, "GetInvoiceByTimeAndUnit", endpoint);

            logger.LogInformation("Successfully retrieved {Count} invoices by time and unit", result?.Count ?? 0);
            return new Response<GetInvoiceByTimeAndUnitResponse>(result ?? new GetInvoiceByTimeAndUnitResponse());
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting invoices by time and unit in MobiFone Invoice API: {ex}",
                ex.Message);
            return new Response<GetInvoiceByTimeAndUnitResponse>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone GetInvoiceByTimeAndUnit API."
            };
        }
    }

    /// <summary>
    /// API 4.15: Lấy thông tin XML hóa đơn
    /// URL: {{base_url}}/api/Invoice68/ExportXMLHoadon?id={hdon_id}
    /// Method: GET
    /// Authorization: Bear Token;ma_dvcs
    /// </summary>
    public async Task<Response<ExportXMLHoadonResponse>> ExportXMLHoadonAsync(
        string id,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("ExportXMLHoadon for invoice ID: {InvoiceId}", id);

            var apiUrl = GetMobiFoneApiUrl();
            var endpoint = $"{apiUrl}/api/Invoice68/ExportXMLHoadon?id={id}";

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, string.Empty);
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone ExportXMLHoadon API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            var response = await httpClient.GetAsync(endpoint, cancellationToken);
            var result = await HandleMobiFoneApiResponse<ExportXMLHoadonResponse>(response, "ExportXMLHoadon", endpoint);

            logger.LogInformation("Successfully exported XML for invoice {InvoiceId}", id);
            return new Response<ExportXMLHoadonResponse>(result ?? new ExportXMLHoadonResponse());
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while exporting XML for invoice {InvoiceId} in MobiFone Invoice API: {ex}",
                id, ex.Message);
            return new Response<ExportXMLHoadonResponse>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone ExportXMLHoadon API."
            };
        }
    }

    /// <summary>
    /// API 4.22: Xuất XML hóa đơn trước khi ký số bằng usb token qua Plugin
    /// URL: {{base_url}}/api/Invoice68/ExportInvoiceXmlPretreatment?id={hdon_id}
    /// Method: GET
    /// Authorization: Bear Token;ma_dvcs
    /// </summary>
    public async Task<Response<ExportInvoiceXmlPretreatmentResponse>> ExportInvoiceXmlPretreatmentAsync(
        string id,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("ExportInvoiceXmlPretreatment for invoice ID: {InvoiceId}", id);

            var apiUrl = GetMobiFoneApiUrl();
            var endpoint = $"{apiUrl}/api/Invoice68/ExportInvoiceXmlPretreatment?id={id}";

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, string.Empty);
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone ExportInvoiceXmlPretreatment API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            var response = await httpClient.GetAsync(endpoint, cancellationToken);
            var result = await HandleMobiFoneApiResponse<ExportInvoiceXmlPretreatmentResponse>(response, "ExportInvoiceXmlPretreatment", endpoint);

            logger.LogInformation("Successfully exported XML pretreatment for invoice {InvoiceId}", id);
            return new Response<ExportInvoiceXmlPretreatmentResponse>(result ?? new ExportInvoiceXmlPretreatmentResponse());
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while exporting XML pretreatment for invoice {InvoiceId} in MobiFone Invoice API: {ex}",
                id, ex.Message);
            return new Response<ExportInvoiceXmlPretreatmentResponse>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone ExportInvoiceXmlPretreatment API."
            };
        }
    }

    /// <summary>
    /// API 4.16: Lấy danh sách hóa đơn theo khoảng thời gian
    /// URL: {{base_url}}/api/Invoice68/GetInvoiceFromdateTodate
    /// Method: POST
    /// Authorization: Bear Token;ma_dvcs
    /// </summary>
    public async Task<Response<GetInvoiceFromdateTodateResponse>> GetInvoiceFromdateTodateAsync(
        GetInvoiceFromdateTodateRequest request,
        string token,
        string maDvcs,
        string? taxCode,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("GetInvoiceFromdateTodate from {FromDate} to {ToDate}",
                request.tu_ngay, request.den_ngay);

            var apiUrl = GetMobiFoneApiUrl();
            var baseEndpoint = $"{apiUrl}/api/Invoice68/GetInvoiceFromdateTodate";
            var endpoint = BuildMobiFoneUrlWithTaxCode(baseEndpoint, taxCode, "GetInvoiceFromdateTodate");

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, string.Empty);
            httpRequestMessage.Content = content;
            httpRequestMessage.Headers.Add("Authorization", $"Bearer {token};{maDvcs}");
            httpClient.BaseAddress = new Uri(endpoint);
            var curlString = httpClient.GenerateCurlInString(httpRequestMessage);
            logger.LogInformation("Generated cURL command for MobiFone GetInvoiceFromdateTodate API: {CurlCommand}", curlString);

            SetMobiFoneHeaders(token, maDvcs);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);
            var result = await HandleMobiFoneApiResponse<GetInvoiceFromdateTodateResponse>(response, "GetInvoiceFromdateTodate", endpoint);

            logger.LogInformation("Successfully retrieved {Count} invoices from {FromDate} to {ToDate}",
                result?.Count ?? 0, request.tu_ngay, request.den_ngay);
            return new Response<GetInvoiceFromdateTodateResponse>(result ?? new GetInvoiceFromdateTodateResponse());
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting invoices from date to date in MobiFone Invoice API: {ex}",
                ex.Message);
            return new Response<GetInvoiceFromdateTodateResponse>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone GetInvoiceFromdateTodate API."
            };
        }
    }

    /// <summary>
    /// Xử lý sau khi tạo hóa đơn thành công - lưu vào InvoiceInfo và cập nhật MerchantInvoiceOrder
    /// </summary>
    private async Task ProcessSuccessfulInvoiceCreation(
        List<SaveListHoadon78Response> invoiceResponses,
        SaveListHoadon78Request originalRequest,
        MerchantInvoiceOrder merchantInvoiceOrder,
        string curlString,
        CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Processing {Count} successful invoice creations for MerchantInvoiceOrderId: {OrderId}",
                invoiceResponses.Count, merchantInvoiceOrder.Id);

            var successfulInvoiceCount = 0;

            for (int i = 0; i < invoiceResponses.Count; i++)
            {
                var invoiceResponse = invoiceResponses[i];
                var originalInvoiceData = originalRequest.data?[i];

                // Kiểm tra xem hóa đơn có được tạo thành công không
                if (!string.IsNullOrEmpty(invoiceResponse.data?.id))
                {
                    // Sử dụng CreateInvoiceCommand để tạo InvoiceInfo
                    var createInvoiceRequest = new Applications.DTOs.InvoiceInfo.CreateInvoice.CreateInvoiceRequest
                    {
                        InvoiceId = invoiceResponse.data.id,
                        MerchantInvoiceOrderId = merchantInvoiceOrder.Id,
                        CqtInvoiceStatus = CqtInvoiceStatus.PendingSignature,
                        InvoiceStatus = InvoiceStatus.Original,
                        RequestData = originalInvoiceData,
                        ResponseData = invoiceResponse,
                        InvoiceNumber = invoiceResponse.data.shdon?.ToString(),
                        InvoiceSeries = invoiceResponse.data.khieu,
                        TotalAmount = originalInvoiceData?.tgtttbso,
                        TaxAmount = originalInvoiceData?.tgtthue,
                        CustomerName = originalInvoiceData?.tnmua,
                        CustomerTaxCode = originalInvoiceData?.mnmua
                    };

                    var createInvoiceCommand = new CreateInvoiceCommand(createInvoiceRequest);

                    var result = await mediator.Send(createInvoiceCommand, cancellationToken);

                    if (result.IsSuccess)
                    {
                        successfulInvoiceCount++;
                        logger.LogInformation("Successfully created InvoiceInfo with Id: {InvoiceId}",
                            invoiceResponse.data.id);
                    }
                    else
                    {
                        logger.LogError("Failed to create InvoiceInfo for InvoiceId: {InvoiceId}. Error: {Error}",
                            invoiceResponse.data.id, result.Message);
                    }
                }
                else
                {
                    logger.LogWarning("Invoice creation failed for item {Index}: {Response}",
                        i, JsonSerializer.Serialize(invoiceResponse));
                }
            }

            logger.LogInformation("Successfully processed {SuccessCount} invoice creations for MerchantInvoiceOrderId: {OrderId}",
                successfulInvoiceCount, merchantInvoiceOrder.Id);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while processing successful invoice creation: {Error}", ex.Message);
        }
    }
}
