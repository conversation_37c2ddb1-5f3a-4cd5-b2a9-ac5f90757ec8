﻿using Applications.Interfaces.Services.Authorization;
using Core.Entities.Authentication;
using Infrastructure.Persistences;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using BuildingBlocks.Abstractions;

namespace Infrastructure.Services.Authorization;

/// <summary>
/// Implementation of dynamic permission checking service
/// </summary>
public class PermissionService : IPermissionService
{
    private readonly ILogger<PermissionService> _logger;
    private readonly AppDbContext _context;

    public PermissionService(ILogger<PermissionService> logger, AppDbContext context)
    {
        _logger = logger;
        _context = context;
    }

    public async Task<Response<PermissionCheckResult>> HasPermissionAsync(
        Guid partnerId, 
        string functionCode, 
        string permissionCode, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Checking permission for partner {PartnerId}, function {FunctionCode}, permission {PermissionCode}", 
            partnerId, functionCode, permissionCode);
        
        try
        {
            // 1. Get partner with active role assignments
            var partner = await _context.Partners
                .Include(p => p.RoleAssignments.Where(pra => 
                    pra.IsActive && 
                    !pra.IsDeleted && 
                    (pra.ExpiresAt == null || pra.ExpiresAt > DateTime.UtcNow)))
                .ThenInclude(pra => pra.Role)
                .Where(p => p.Id == partnerId && !p.IsDeleted && p.IsActive)
                .FirstOrDefaultAsync(cancellationToken);

            if (partner == null)
            {
                var result = new PermissionCheckResult
                {
                    HasPermission = false,
                    FunctionCode = functionCode,
                    PermissionCode = permissionCode,
                    Source = "Partner not found or inactive",
                    ExpiresAt = null
                };
                return new Response<PermissionCheckResult>(result, "Partner not found");
            }

            // 2. Get function and permission entities
            var function = await _context.Functions
                .Where(f => f.Code == functionCode && !f.IsDeleted)
                .FirstOrDefaultAsync(cancellationToken);

            var permission = await _context.Permissions
                .Where(p => p.Code == permissionCode && !p.IsDeleted)
                .FirstOrDefaultAsync(cancellationToken);

            if (function == null || permission == null)
            {
                var result = new PermissionCheckResult
                {
                    HasPermission = false,
                    FunctionCode = functionCode,
                    PermissionCode = permissionCode,
                    Source = $"Function '{functionCode}' or permission '{permissionCode}' not found",
                    ExpiresAt = null
                };
                return new Response<PermissionCheckResult>(result, "Function or permission not found");
            }

            // 3. Check if any role has the required permission for this function
            var grantedRoles = new List<string>();
            DateTime? earliestExpiry = null;
            
            foreach (var roleAssignment in partner.RoleAssignments)
            {
                var hasPermission = await _context.RoleFunctionPermissions
                    .AnyAsync(rfp => 
                        rfp.RoleId == roleAssignment.RoleId &&
                        rfp.FunctionId == function.Id &&
                        rfp.PermissionId == permission.Id &&
                        rfp.IsGranted &&
                        !rfp.IsDeleted, 
                        cancellationToken);

                if (hasPermission)
                {
                    grantedRoles.Add(roleAssignment.Role.Code);
                    if (roleAssignment.ExpiresAt.HasValue)
                    {
                        if (!earliestExpiry.HasValue || roleAssignment.ExpiresAt < earliestExpiry)
                        {
                            earliestExpiry = roleAssignment.ExpiresAt;
                        }
                    }
                }
            }

            var isGranted = grantedRoles.Count > 0;
            var permissionResult = new PermissionCheckResult
            {
                HasPermission = isGranted,
                FunctionCode = functionCode,
                PermissionCode = permissionCode,
                Source = isGranted ? $"Roles: {string.Join(", ", grantedRoles)}" : "No matching roles",
                ExpiresAt = earliestExpiry
            };

            if (isGranted)
            {
                _logger.LogInformation("Permission granted for partner {PartnerId}: {FunctionCode}.{PermissionCode} via roles: {Roles}", 
                    partnerId, functionCode, permissionCode, string.Join(", ", grantedRoles));
            }
            else
            {
                _logger.LogWarning("Permission denied for partner {PartnerId}: {FunctionCode}.{PermissionCode} - no matching roles", 
                    partnerId, functionCode, permissionCode);
            }

            return new Response<PermissionCheckResult>(permissionResult, isGranted ? "Permission granted" : "Permission denied");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission for partner {PartnerId}, function {FunctionCode}, permission {PermissionCode}", 
                partnerId, functionCode, permissionCode);
            
            var errorResult = new PermissionCheckResult
            {
                HasPermission = false,
                FunctionCode = functionCode,
                PermissionCode = permissionCode,
                Source = "Internal error during permission check",
                ExpiresAt = null
            };
            
            var errorResponse = new Response<PermissionCheckResult>();
                errorResponse.Code = "500";
                errorResponse.Message = 
                "Permission check error";
                return errorResponse;
        }
    }

    public async Task<Response<PartnerPermissionSummary>> GetPartnerPermissionsAsync(
        Guid partnerId, 
        CancellationToken cancellationToken = default)
    {
        // TODO: Implement getting partner permissions
        _logger.LogInformation("Getting permissions for partner {PartnerId}", partnerId);
        
        await Task.Delay(1, cancellationToken);
        
        return new Response<PartnerPermissionSummary>(new PartnerPermissionSummary(), "Retrieved partner permissions");
    }

    public async Task<Response<Dictionary<string, PermissionCheckResult>>> CheckMultiplePermissionsAsync(
        Guid partnerId, 
        IEnumerable<PermissionRequest> permissionRequests, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Checking multiple permissions for partner {PartnerId}", partnerId);
        
        try
        {
            var results = new Dictionary<string, PermissionCheckResult>();
            
            foreach (var request in permissionRequests)
            {
                var permissionResult = await HasPermissionAsync(partnerId, request.FunctionCode, request.PermissionCode, cancellationToken);
                
                var key = $"{request.FunctionCode}:{request.PermissionCode}";
                
                if (permissionResult.IsSuccess && permissionResult.Data != null)
                {
                    results[key] = permissionResult.Data;
                }
                else
                {
                    results[key] = new PermissionCheckResult
                    {
                        HasPermission = false,
                        FunctionCode = request.FunctionCode,
                        PermissionCode = request.PermissionCode,
                        Source = "Permission check failed",
                        ExpiresAt = null,
                        DenialReason = permissionResult.Message
                    };
                }
            }
            
            var grantedCount = results.Values.Count(r => r.HasPermission);
            var totalCount = results.Count;
            
            _logger.LogInformation("Multiple permission check completed for partner {PartnerId}: {GrantedCount}/{TotalCount} granted", 
                partnerId, grantedCount, totalCount);
            
            return new Response<Dictionary<string, PermissionCheckResult>>(results,
                $"Checked {totalCount} permissions, {grantedCount} granted");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking multiple permissions for partner {PartnerId}", partnerId);
            var errorResponse = new Response<Dictionary<string, PermissionCheckResult>>();
            errorResponse.Code = "500";
            errorResponse.Message = "Error checking multiple permissions";
            return errorResponse;
        }
    }

    public async Task<Response<bool>> GrantPermissionAsync(
        Guid partnerId, 
        string functionCode, 
        string permissionCode, 
        Guid grantedBy, 
        string reason, 
        DateTime? expiresAt = null, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Granting permission to partner {PartnerId}, function {FunctionCode}, permission {PermissionCode}, by {GrantedBy}", 
            partnerId, functionCode, permissionCode, grantedBy);
        
        try
        {
            // Check if function exists
            var function = await _context.Functions
                .Where(f => f.Code == functionCode && f.IsActive && !f.IsDeleted)
                .FirstOrDefaultAsync(cancellationToken);

            if (function == null)
            {
                var errorResponse = new Response<bool>();
                errorResponse.Code = "404";
                errorResponse.Message = $"Function {functionCode} not found";
                return errorResponse;
            }

            // Check if permission exists
            var permission = await _context.Permissions
                .Where(p => p.Code == permissionCode && p.IsActive && !p.IsDeleted)
                .FirstOrDefaultAsync(cancellationToken);

            if (permission == null)
            {
                var errorResponse = new Response<bool>();
                errorResponse.Code = "404";
                errorResponse.Message = $"Permission {permissionCode} not found";
                return errorResponse;
            }

            // Check if partner exists
            var partner = await _context.Partners
                .Where(p => p.Id == partnerId && p.IsActive && !p.IsDeleted)
                .FirstOrDefaultAsync(cancellationToken);

            if (partner == null)
            {
                var errorResponse = new Response<bool>();
                errorResponse.Code = "404";
                errorResponse.Message = "Partner not found";
                return errorResponse;
            }

            // Check if permission already granted
            var existingGrant = await _context.PartnerFunctionPermissions
                .Where(pfp => 
                    pfp.PartnerId == partnerId && 
                    pfp.FunctionId == function.Id &&
                    pfp.PermissionId == permission.Id &&
                    pfp.IsGranted && 
                    !pfp.IsDeleted &&
                    (pfp.ExpiresAt == null || pfp.ExpiresAt > DateTime.UtcNow))
                .FirstOrDefaultAsync(cancellationToken);

            if (existingGrant != null)
            {
                return new Response<bool>(true, "Permission already granted to partner");
            }

            // Create new permission grant
            var grant = new PartnerFunctionPermission
            {
                Id = Guid.NewGuid(),
                PartnerId = partnerId,
                FunctionId = function.Id,
                PermissionId = permission.Id,
                IsGranted = true,
                ExpiresAt = expiresAt,
                GrantReason = reason,
                GrantedBy = grantedBy,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                CreatedBy = grantedBy,
                UpdatedBy = grantedBy,
                IsDeleted = false
            };

            _context.PartnerFunctionPermissions.Add(grant);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Permission {FunctionCode}.{PermissionCode} granted successfully to partner {PartnerId}", 
                functionCode, permissionCode, partnerId);
            
            return new Response<bool>(true, "Permission granted successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error granting permission {FunctionCode}.{PermissionCode} to partner {PartnerId}", 
                functionCode, permissionCode, partnerId);
            var errorResponse = new Response<bool>();
                errorResponse.Code = "500";
                errorResponse.Message = "Error granting permission";
                return errorResponse;
        }
    }

    public async Task<Response<bool>> RevokePermissionAsync(
        Guid partnerId, 
        string functionCode, 
        string permissionCode, 
        Guid revokedBy, 
        string reason, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Revoking permission from partner {PartnerId}, function {FunctionCode}, permission {PermissionCode}, by {RevokedBy}", 
            partnerId, functionCode, permissionCode, revokedBy);
        
        try
        {
            // Find the function and permission
            var function = await _context.Functions
                .Where(f => f.Code == functionCode && !f.IsDeleted)
                .FirstOrDefaultAsync(cancellationToken);

            var permission = await _context.Permissions
                .Where(p => p.Code == permissionCode && !p.IsDeleted)
                .FirstOrDefaultAsync(cancellationToken);

            if (function == null || permission == null)
            {
                var errorResponse = new Response<bool>();
                errorResponse.Code = "404";
                errorResponse.Message = "Function or permission not found";
                return errorResponse;
            }

            // Find active grants
            var grants = await _context.PartnerFunctionPermissions
                .Where(pfp => 
                    pfp.PartnerId == partnerId && 
                    pfp.FunctionId == function.Id &&
                    pfp.PermissionId == permission.Id &&
                    pfp.IsGranted && 
                    !pfp.IsDeleted)
                .ToListAsync(cancellationToken);

            if (!grants.Any())
            {
                return new Response<bool>(true, "Permission not granted to partner");
            }

            // Revoke grants
            foreach (var grant in grants)
            {
                grant.IsGranted = false;
                grant.IsDeleted = true;
                grant.UpdatedAt = DateTime.UtcNow;
                grant.UpdatedBy = revokedBy;
            }

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Permission {FunctionCode}.{PermissionCode} revoked successfully from partner {PartnerId}, {Count} grants revoked", 
                functionCode, permissionCode, partnerId, grants.Count);
            
            return new Response<bool>(true, $"Permission revoked successfully ({grants.Count} grants revoked)");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking permission {FunctionCode}.{PermissionCode} from partner {PartnerId}", 
                functionCode, permissionCode, partnerId);
            var errorResponse = new Response<bool>();
                errorResponse.Code = "500";
                errorResponse.Message = "Error revoking permission";
                return errorResponse;
        }
    }

    public void ClearPermissionCache(Guid partnerId)
    {
        // TODO: Implement permission cache clearing
        _logger.LogInformation("Clearing permission cache for partner {PartnerId}", partnerId);
    }

    public void ClearAllPermissionCache()
    {
        // TODO: Implement all permission cache clearing
        _logger.LogInformation("Clearing all permission cache");
    }
}


