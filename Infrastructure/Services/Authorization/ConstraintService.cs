﻿using Applications.Interfaces.Services.Authorization;
using Core.Entities.Authentication;
using Infrastructure.Persistences;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using BuildingBlocks.Abstractions;

namespace Infrastructure.Services.Authorization;

/// <summary>
/// Implementation of business constraint validation service
/// </summary>
public class ConstraintService : IConstraintService
{
    private readonly ILogger<ConstraintService> _logger;
    private readonly AppDbContext _context;

    public ConstraintService(ILogger<ConstraintService> logger, AppDbContext context)
    {
        _logger = logger;
        _context = context;
    }

    public async Task<Response<ConstraintValidationResult>> ValidateConstraintAsync(
        Guid partnerId, 
        string constraintType, 
        object currentValue, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Validating constraint for partner {PartnerId}, type {ConstraintType}", 
            partnerId, constraintType);
        
        try
        {
            // Get active constraints for partner
            var constraints = await _context.PartnerConstraints
                .Where(pc => 
                    pc.PartnerId == partnerId && 
                    pc.ConstraintType == constraintType &&
                    pc.IsActive && 
                    !pc.IsDeleted &&
                    (pc.ValidTo == null || pc.ValidTo > DateTime.UtcNow))
                .OrderByDescending(pc => pc.Priority)
                .ToListAsync(cancellationToken);

            if (!constraints.Any())
            {
                return new Response<ConstraintValidationResult>(new ConstraintValidationResult
                {
                    IsValid = true,
                    ErrorMessage = "No constraints found - allowing"
                }, "No constraints to validate");
            }

            // Validate against each constraint
            foreach (var constraint in constraints)
            {
                var isValid = ValidateConstraintValue(constraintType, currentValue, constraint.ConstraintValue);
                
                if (!isValid)
                {
                    _logger.LogWarning("Constraint validation failed for partner {PartnerId}, type {ConstraintType}, value {CurrentValue}", 
                        partnerId, constraintType, currentValue);
                        
                    return new Response<ConstraintValidationResult>(new ConstraintValidationResult
                    {
                        IsValid = false,
                        LimitValue = constraint.ConstraintValue,
                        ErrorMessage = $"Constraint violation: {constraint.Description ?? constraintType}"
                    }, "Constraint validation failed");
                }
            }

            _logger.LogInformation("Constraint validation passed for partner {PartnerId}, type {ConstraintType}", 
                partnerId, constraintType);
                
            return new Response<ConstraintValidationResult>(new ConstraintValidationResult
            {
                IsValid = true,
                ErrorMessage = "All constraints satisfied"
            }, "Constraint validation passed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating constraint for partner {PartnerId}", partnerId);
            var errorResponse = new Response<ConstraintValidationResult>();
            errorResponse.Code = "500";
            errorResponse.Message = "Error validating constraint";
            return errorResponse;
        }
    }

    private bool ValidateConstraintValue(string constraintType, object currentValue, string constraintValue)
    {
        try
        {
            switch (constraintType.ToUpper())
            {
                case "MAX_AMOUNT":
                    if (decimal.TryParse(currentValue?.ToString(), out var currentAmount) && 
                        decimal.TryParse(constraintValue, out var maxAmount))
                    {
                        return currentAmount <= maxAmount;
                    }
                    break;
                    
                case "MIN_AMOUNT":
                    if (decimal.TryParse(currentValue?.ToString(), out var currentMin) && 
                        decimal.TryParse(constraintValue, out var minAmount))
                    {
                        return currentMin >= minAmount;
                    }
                    break;
                    
                case "MAX_COUNT":
                    if (int.TryParse(currentValue?.ToString(), out var currentCount) && 
                        int.TryParse(constraintValue, out var maxCount))
                    {
                        return currentCount <= maxCount;
                    }
                    break;
                    
                case "ALLOWED_VALUES":
                    var allowedValues = constraintValue.Split(',').Select(v => v.Trim()).ToArray();
                    return allowedValues.Contains(currentValue?.ToString(), StringComparer.OrdinalIgnoreCase);
                    
                case "FORBIDDEN_VALUES":
                    var forbiddenValues = constraintValue.Split(',').Select(v => v.Trim()).ToArray();
                    return !forbiddenValues.Contains(currentValue?.ToString(), StringComparer.OrdinalIgnoreCase);
                    
                default:
                    _logger.LogWarning("Unknown constraint type: {ConstraintType}", constraintType);
                    return true; // Allow unknown constraints
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating constraint value for type {ConstraintType}", constraintType);
        }
        
        return true; // Default to allow if validation fails
    }

    public async Task<Response<Dictionary<string, ConstraintValidationResult>>> ValidateMultipleConstraintsAsync(
        Guid partnerId, 
        IEnumerable<ConstraintCheck> constraintChecks, 
        CancellationToken cancellationToken = default)
    {
        // TODO: Implement multiple constraint validation
        _logger.LogInformation("Validating multiple constraints for partner {PartnerId}", partnerId);
        
        await Task.Delay(1, cancellationToken);
        
        var results = new Dictionary<string, ConstraintValidationResult>();
        foreach (var check in constraintChecks)
        {
            results[check.ConstraintType] = new ConstraintValidationResult
            {
                IsValid = true
            };
        }
        
        return new Response<Dictionary<string, ConstraintValidationResult>>(results, "Multiple constraint validation - allowing all for now");
    }

    public async Task<Response<PartnerConstraintSummary>> GetPartnerConstraintsAsync(
        Guid partnerId, 
        CancellationToken cancellationToken = default)
    {
        // TODO: Implement getting partner constraints
        _logger.LogInformation("Getting constraints for partner {PartnerId}", partnerId);
        
        await Task.Delay(1, cancellationToken);
        
        return new Response<PartnerConstraintSummary>(new PartnerConstraintSummary(), "Retrieved partner constraints");
    }

    public async Task<Response<bool>> SetConstraintAsync(
        Guid partnerId, 
        string constraintType, 
        string constraintValue, 
        DateTime validFrom, 
        DateTime? validTo, 
        Guid setBy, 
        string? description = null, 
        int priority = 0, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Setting constraint for partner {PartnerId}, type {ConstraintType}, value {ConstraintValue}", 
            partnerId, constraintType, constraintValue);
        
        try
        {
            // Deactivate existing constraints of the same type if they overlap
            var existingConstraints = await _context.PartnerConstraints
                .Where(pc => 
                    pc.PartnerId == partnerId && 
                    pc.ConstraintType == constraintType &&
                    pc.IsActive && 
                    !pc.IsDeleted &&
                    (pc.ValidTo == null || pc.ValidTo > validFrom))
                .ToListAsync(cancellationToken);

            foreach (var existing in existingConstraints)
            {
                existing.IsActive = false;
                existing.UpdatedAt = DateTime.UtcNow;
                existing.UpdatedBy = setBy;
            }

            // Create new constraint
            var constraint = new Core.Entities.Authentication.PartnerConstraint
            {
                Id = Guid.NewGuid(),
                PartnerId = partnerId,
                ConstraintType = constraintType,
                ConstraintValue = constraintValue,
                Description = description,
                Priority = priority,
                ValidFrom = validFrom,
                ValidTo = validTo,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                CreatedBy = setBy,
                UpdatedBy = setBy,
                IsDeleted = false
            };

            _context.PartnerConstraints.Add(constraint);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Constraint set successfully for partner {PartnerId}, type {ConstraintType}", 
                partnerId, constraintType);

            return new Response<bool>(true, "Constraint set successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting constraint for partner {PartnerId}", partnerId);
            var errorResponse = new Response<bool>();
            errorResponse.Code = "500";
            errorResponse.Message = "Error setting constraint";
            return errorResponse;
        }
    }

    public async Task<Response<bool>> RemoveConstraintAsync(
        Guid partnerId, 
        string constraintType, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Removing constraint for partner {PartnerId}, type {ConstraintType}", 
            partnerId, constraintType);
        
        try
        {
            var constraints = await _context.PartnerConstraints
                .Where(pc => 
                    pc.PartnerId == partnerId && 
                    pc.ConstraintType == constraintType &&
                    pc.IsActive && 
                    !pc.IsDeleted)
                .ToListAsync(cancellationToken);

            if (!constraints.Any())
            {
                return new Response<bool>(true, "No active constraints found to remove");
            }

            foreach (var constraint in constraints)
            {
                constraint.IsActive = false;
                constraint.IsDeleted = true;
                constraint.UpdatedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Removed {Count} constraints for partner {PartnerId}, type {ConstraintType}", 
                constraints.Count, partnerId, constraintType);

            return new Response<bool>(true, $"Removed {constraints.Count} constraints");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing constraint for partner {PartnerId}", partnerId);
            var errorResponse = new Response<bool>();
            errorResponse.Code = "500";
            errorResponse.Message = "Error removing constraint";
            return errorResponse;
        }
    }

    public async Task<Response<MonthlyLimitResult>> CheckMonthlyLimitAsync(
        Guid partnerId, 
        decimal additionalAmount, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Checking monthly limit for partner {PartnerId}, amount {AdditionalAmount}", 
            partnerId, additionalAmount);
        
        try
        {
            // Get partner with monthly limit
            var partner = await _context.Partners
                .Where(p => p.Id == partnerId && !p.IsDeleted && p.IsActive)
                .FirstOrDefaultAsync(cancellationToken);

            if (partner == null)
            {
                return new Response<MonthlyLimitResult>(new MonthlyLimitResult
                {
                    IsWithinLimit = false,
                    CurrentUsage = 0,
                    MonthlyLimit = 0,
                    RemainingLimit = 0,
                    RequestedAmount = additionalAmount,
                    CurrentPeriod = DateTime.UtcNow.ToString("yyyy-MM")
                }, "Partner not found");
            }

            var monthlyLimit = partner.MonthlyInvoiceLimit;
            var currentUsage = partner.CurrentMonthUsage;
            var projectedUsage = currentUsage + additionalAmount;
            var isWithinLimit = projectedUsage <= monthlyLimit;
            var remainingAmount = Math.Max(0, monthlyLimit - projectedUsage);

            var result = new MonthlyLimitResult
            {
                IsWithinLimit = isWithinLimit,
                CurrentUsage = currentUsage,
                MonthlyLimit = monthlyLimit,
                RemainingLimit = remainingAmount,
                RequestedAmount = additionalAmount,
                CurrentPeriod = DateTime.UtcNow.ToString("yyyy-MM")
            };

            if (isWithinLimit)
            {
                _logger.LogInformation("Monthly limit check passed for partner {PartnerId}: {ProjectedUsage}/{MonthlyLimit}", 
                    partnerId, projectedUsage, monthlyLimit);
            }
            else
            {
                _logger.LogWarning("Monthly limit exceeded for partner {PartnerId}: {ProjectedUsage}/{MonthlyLimit}", 
                    partnerId, projectedUsage, monthlyLimit);
            }

            return new Response<MonthlyLimitResult>(result,
                isWithinLimit ? "Monthly limit check passed" : "Monthly limit would be exceeded");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking monthly limit for partner {PartnerId}", partnerId);
            var errorResponse = new Response<MonthlyLimitResult>();
            errorResponse.Code = "500";
            errorResponse.Message = "Monthly limit check error";
            return errorResponse;
        }
    }

    public async Task<Response<RateLimitResult>> CheckRateLimitAsync(
        Guid partnerId, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Checking rate limit for partner {PartnerId}", partnerId);
        
        try
        {
            // 1. Get partner with current usage and rate limits
            var partner = await _context.Partners
                .Where(p => p.Id == partnerId && !p.IsDeleted && p.IsActive)
                .FirstOrDefaultAsync(cancellationToken);

            if (partner == null)
            {
                return new Response<RateLimitResult>(new RateLimitResult
                {
                    IsWithinLimit = false,
                    CurrentRequestCount = 0,
                    HourlyLimit = 0,
                    RemainingRequests = 0,
                    ResetTime = DateTime.UtcNow.AddHours(1)
                }, "Partner not found");
            }

            // 2. Get current month's API call count (simplified for testing)
            var currentPeriod = PartnerUsage.GetCurrentPeriod();

            var currentUsage = await _context.PartnerUsages
                .Where(pu => 
                    pu.PartnerId == partnerId && 
                    pu.Period == currentPeriod &&
                    !pu.IsDeleted)
                .FirstOrDefaultAsync(cancellationToken);

            var currentCalls = (int)(currentUsage?.ApiCallsCount ?? 0);

            // 3. Check rate limit
            var rateLimit = partner.ApiRateLimitPerHour;
            var isWithinLimit = currentCalls < rateLimit;

            var nextHour = DateTime.UtcNow.AddHours(1);
            var result = new RateLimitResult
            {
                IsWithinLimit = isWithinLimit,
                CurrentRequestCount = currentCalls,
                HourlyLimit = rateLimit,
                RemainingRequests = Math.Max(0, rateLimit - currentCalls),
                ResetTime = nextHour
            };

            if (isWithinLimit)
            {
                _logger.LogInformation("Rate limit check passed for partner {PartnerId}: {CurrentCalls}/{RateLimit} calls this period", 
                    partnerId, currentCalls, rateLimit);
            }
            else
            {
                _logger.LogWarning("Rate limit exceeded for partner {PartnerId}: {CurrentCalls}/{RateLimit} calls this period", 
                    partnerId, currentCalls, rateLimit);
            }

            return new Response<RateLimitResult>(result,
                isWithinLimit ? "Rate limit check passed" : "Rate limit exceeded");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking rate limit for partner {PartnerId}", partnerId);
            var errorResponse = new Response<RateLimitResult>();
            errorResponse.Code = "500";
            errorResponse.Message = "Rate limit check error";
            return errorResponse;
        }
    }

    public void ClearConstraintCache(Guid partnerId)
    {
        // TODO: Implement constraint cache clearing
        _logger.LogInformation("Clearing constraint cache for partner {PartnerId}", partnerId);
    }
}
