using Applications.DTOs.MobiFoneInvoice.Login;
using Applications.Interfaces.Services;
using Microsoft.Extensions.Logging;
using Shared.Interfaces;
using Shared.Models;

namespace Infrastructure.Services;

/// <summary>
/// Adapter để wrap MobiFoneInvoiceService cho việc login
/// </summary>
public class MobiFoneLoginServiceAdapter : IMobiFoneLoginService
{
    private readonly IMobiFoneInvoiceService _mobiFoneInvoiceService;
    private readonly ILogger<MobiFoneLoginServiceAdapter> _logger;

    public MobiFoneLoginServiceAdapter(
        IMobiFoneInvoiceService mobiFoneInvoiceService,
        ILogger<MobiFoneLoginServiceAdapter> logger)
    {
        _mobiFoneInvoiceService = mobiFoneInvoiceService;
        _logger = logger;
    }

    public async Task<MobiFoneAuthenticationInfo?> LoginAsync(MobiFoneLoginCredentials credentials, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Performing MobiFone login for TaxCode: {TaxCode}, Username: {Username}", 
                credentials.TaxCode, credentials.Username);

            // Tạo login request
            var loginRequest = new LoginRequest
            {
                Username = credentials.Username,
                Password = credentials.Password,
                TaxCode = credentials.TaxCode
            };

            // Gọi MobiFone Login API
            var loginResponse = await _mobiFoneInvoiceService.LoginAsync(loginRequest, cancellationToken);

            if (!loginResponse.Code.Equals("000") || loginResponse.Data == null)
            {
                _logger.LogError("MobiFone login failed for TaxCode: {TaxCode}. Error: {Error}", 
                    credentials.TaxCode, loginResponse.Message);
                return null;
            }

            // Tạo authentication info
            var authInfo = new MobiFoneAuthenticationInfo
            {
                Token = loginResponse.Data.token,
                MaDvcs = loginResponse.Data.ma_dvcs,
                TaxCode = credentials.TaxCode,
                ExpiresAt = DateTime.UtcNow.AddMinutes(55) // Sẽ được override bởi cache service
            };

            _logger.LogInformation("Successfully logged in for TaxCode: {TaxCode}", credentials.TaxCode);

            return authInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during MobiFone login for TaxCode: {TaxCode}", credentials.TaxCode);
            return null;
        }
    }
}
