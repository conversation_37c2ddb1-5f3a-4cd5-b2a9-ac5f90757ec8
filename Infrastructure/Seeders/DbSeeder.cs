﻿using Core.Entities;
using Core.Entities.Authentication;
using Core.Enumerables;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Persistences
{
    public static class DbSeeder
    {
        public static async Task SeedAsync(AppDbContext context)
        {
            // Đảm bảo database đã tạo
            await context.Database.MigrateAsync();

            // ✅ Seed ClientCredential cho hệ thống B2B
            if (!context.ClientCredentials.Any())
            {
                var client = new ClientCredential
                {
                    ClientId = "admin",
                    ClientSecretHash = BCrypt.Net.BCrypt.HashPassword("supersecret"), // mã hoá secret
                    Description = "Admin backend service",
                    IsActive = true,
                    Role = RoleEnum.AdminDashboard // Vai trò của client

                };

                context.ClientCredentials.Add(client);
                await context.SaveChangesAsync();
            }

            // ✅ Seed ZenShop Partner cho integration testing
            if (!await context.Partners.AnyAsync(p => p.ClientId == "zenshop_test_client"))
            {
                var zenshopPartner = new Partner
                {
                    Id = Guid.NewGuid(),
                    ClientId = "zenshop_test_client",
                    ClientSecretHash = BCrypt.Net.BCrypt.HashPassword("zenshop_test_secret_2024"),
                    HmacSecretHash = "zenshop_test_hmac_secret_2024", // TODO: Encrypt this in production
                    Name = "ZenShop Test Integration",
                    ContactEmail = "<EMAIL>",
                    ContactPhone = "+84987654321",
                    Description = "ZenShop test integration for MobiFone invoice services",
                    IpWhitelist = "[\"127.0.0.1\", \"::1\", \"0.0.0.0/0\"]", // Allow all IPs for testing
                    EnableIpWhitelist = false, // Disable IP whitelist for testing
                    IsActive = true,
                    ApiRateLimitPerHour = 10000,
                    MonthlyInvoiceLimit = 100000m,
                    CurrentMonthUsage = 0m,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    CreatedBy = Guid.Empty, // System created
                    IsDeleted = false
                };

                context.Partners.Add(zenshopPartner);
                await context.SaveChangesAsync();
            }

            // (Optional) Bạn có thể seed thêm Role, User, Permission nếu cần
        }
    }
}
