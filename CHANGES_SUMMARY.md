# Tóm Tắt Các Thay Đổi - Thêm Status Field cho MerchantInvoiceOrder

## 1. Tạo Enum MerchantInvoiceOrderStatus
**File**: `Core/Enumerables/MerchantInvoiceOrderStatus.cs`
- Tạo enum với 2 giá trị: `PENDING = 1`, `COMPLETED = 2`
- Thêm extension methods để hiển thị tên tiếng Việt
- Mặc định là `PENDING`

## 2. Cập Nhật Entity MerchantInvoiceOrder
**File**: `Core/Entities/MerchantInvoiceOrder.cs`
- Thêm using `Core.Enumerables`
- Thêm property `Status` nullable với default value là `PENDING`
- Không required để cho phép null (sẽ default thành PENDING trong database)

## 3. Cập Nhật Entity Configuration
**File**: `Infrastructure/Configurations/MerchantInvoiceOrderConfiguration.cs`
- Thêm configuration cho field `Status` nullable
- Sử dụng `HasConversion<string>()` để lưu dưới dạng string
- Thêm `HasDefaultValue(MerchantInvoiceOrderStatus.PENDING)`
- Thêm index `IX_MerchantInvoiceOrders_Status`
- Không có `IsRequired()` để cho phép null values

## 4. Tạo DTOs cho Confirm API
**Files**:
- `Applications/DTOs/MerchantInvoiceOrder/ConfirmMerchantInvoiceOrder/ConfirmMerchantInvoiceOrderRequest.cs`
- `Applications/DTOs/MerchantInvoiceOrder/ConfirmMerchantInvoiceOrder/ConfirmMerchantInvoiceOrderResponse.cs`

## 5. Tạo Command Handler
**File**: `Applications/Features/MerchantInvoiceOrder/Commands/ConfirmMerchantInvoiceOrderCommand.cs`
- Tạo command để xử lý xác thực
- Validation: chỉ cho phép chuyển từ `PENDING` (hoặc null) sang `COMPLETED`
- Kiểm tra quyền truy cập của partner
- Cập nhật status và thời gian

## 6. Thêm Endpoint vào Controller
**File**: `API/Controllers/MerchantInvoiceOrderController.cs`
- Thêm using cho DTO mới
- Thêm endpoint `PATCH {id}/confirm`
- Xử lý các response codes: 200, 400, 401, 404, 500

## 7. Cập Nhật DTOs Hiện Tại
**Files**:
- `Applications/DTOs/MerchantInvoiceOrder/MerchantInvoiceOrderDto.cs`
- `Applications/DTOs/MerchantInvoiceOrder/GetMerchantInvoiceOrderDetail/GetMerchantInvoiceOrderDetailResponse.cs`
- `Applications/DTOs/MerchantInvoiceOrder/GetMerchantInvoiceOrders/MerchantInvoiceOrderDto.cs`

Thêm field `Status` nullable vào tất cả DTOs để đảm bảo consistency.

## 8. Cập Nhật Query/Command Handlers
**Files**:
- `Applications/Features/MerchantInvoiceOrder/Queries/GetMerchantInvoiceOrderDetailQuery.cs`
- `Applications/Features/MerchantInvoiceOrder/Commands/UpdateMerchantInvoiceOrderCommand.cs`

Thêm mapping cho field `Status` mới.

## 9. Tạo Migration Command
**File**: `migration_command.txt`
```bash
dotnet ef migrations add AddStatusToMerchantInvoiceOrder --project Infrastructure --startup-project API
```

## 10. Tạo API Mới: Create Account và Confirm Order
**Files**:
- `Applications/DTOs/MerchantInvoiceOrder/UpdateAccountAndConfirm/UpdateAccountAndConfirmRequest.cs`
- `Applications/DTOs/MerchantInvoiceOrder/UpdateAccountAndConfirm/UpdateAccountAndConfirmResponse.cs`
- `Applications/Features/MerchantInvoiceOrder/Commands/UpdateAccountAndConfirmCommand.cs`

Tạo API mới để tạo account và confirm order trong một transaction.

## 11. Cập Nhật Controller
**File**: `API/Controllers/MerchantInvoiceOrderController.cs`
- Thêm endpoint `POST {orderId}/create-account-and-confirm`
- Xử lý transaction và error handling

## 12. Tạo Documentation
**Files**:
- `CONFIRM_MERCHANT_INVOICE_ORDER_API.md`: Mô tả API confirm
- `CREATE_ACCOUNT_AND_CONFIRM_ORDER_API.md`: Mô tả API mới tạo account và confirm

## Lệnh Migration
Để tạo migration, chạy lệnh sau trong terminal:
```bash
dotnet ef migrations add AddStatusToMerchantInvoiceOrder --project Infrastructure --startup-project API
```

## Kiểm Tra
- Tất cả files đã được cập nhật không có lỗi compilation
- DTOs đã được cập nhật để bao gồm field Status
- API endpoint mới đã được thêm với đầy đủ validation và error handling
- Documentation đã được tạo

## Endpoints Mới

### 1. Confirm Order
```
PATCH /zenInvoice/api/MerchantInvoiceOrder/{id}/confirm
```
Logic: Chỉ cho phép chuyển status từ `PENDING` sang `COMPLETED`.

### 2. Create Account và Confirm Order (MỚI)
```
POST /zenInvoice/api/MerchantInvoiceOrder/{orderId}/create-account-and-confirm
```
Logic: Tạo MerchantBranchInvoiceAccount mới và confirm order trong một transaction.
