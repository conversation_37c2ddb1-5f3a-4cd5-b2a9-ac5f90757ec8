# Tóm Tắt Các Thay Đổi - Thêm Status Field cho MerchantInvoiceOrder

## 1. Tạo Enum MerchantInvoiceOrderStatus
**File**: `Core/Enumerables/MerchantInvoiceOrderStatus.cs`
- Tạo enum với 2 giá trị: `PENDING = 1`, `COMPLETED = 2`
- Thêm extension methods để hiển thị tên tiếng Việt
- Mặc định là `PENDING`

## 2. Cập Nhật Entity MerchantInvoiceOrder
**File**: `Core/Entities/MerchantInvoiceOrder.cs`
- Thêm using `Core.Enumerables`
- Thêm property `Status` với default value là `PENDING`
- <PERSON><PERSON><PERSON> dấu `[Required]`

## 3. Cập Nhật Entity Configuration
**File**: `Infrastructure/Configurations/MerchantInvoiceOrderConfiguration.cs`
- Thêm configuration cho field `Status`
- Sử dụng `HasConversion<string>()` để lưu dưới dạng string
- Thêm `HasDefaultValue(MerchantInvoiceOrderStatus.PENDING)`
- Thêm index `IX_MerchantInvoiceOrders_Status`

## 4. Tạo DTOs cho Confirm API
**Files**:
- `Applications/DTOs/MerchantInvoiceOrder/ConfirmMerchantInvoiceOrder/ConfirmMerchantInvoiceOrderRequest.cs`
- `Applications/DTOs/MerchantInvoiceOrder/ConfirmMerchantInvoiceOrder/ConfirmMerchantInvoiceOrderResponse.cs`

## 5. Tạo Command Handler
**File**: `Applications/Features/MerchantInvoiceOrder/Commands/ConfirmMerchantInvoiceOrderCommand.cs`
- Tạo command để xử lý xác thực
- Validation: chỉ cho phép chuyển từ `PENDING` sang `COMPLETED`
- Kiểm tra quyền truy cập của partner
- Cập nhật status và thời gian

## 6. Thêm Endpoint vào Controller
**File**: `API/Controllers/MerchantInvoiceOrderController.cs`
- Thêm using cho DTO mới
- Thêm endpoint `PATCH {id}/confirm`
- Xử lý các response codes: 200, 400, 401, 404, 500

## 7. Cập Nhật DTOs Hiện Tại
**Files**:
- `Applications/DTOs/MerchantInvoiceOrder/MerchantInvoiceOrderDto.cs`
- `Applications/DTOs/MerchantInvoiceOrder/GetMerchantInvoiceOrderDetail/GetMerchantInvoiceOrderDetailResponse.cs`
- `Applications/DTOs/MerchantInvoiceOrder/GetMerchantInvoiceOrders/MerchantInvoiceOrderDto.cs`

Thêm field `Status` vào tất cả DTOs để đảm bảo consistency.

## 8. Cập Nhật Query/Command Handlers
**Files**:
- `Applications/Features/MerchantInvoiceOrder/Queries/GetMerchantInvoiceOrderDetailQuery.cs`
- `Applications/Features/MerchantInvoiceOrder/Commands/UpdateMerchantInvoiceOrderCommand.cs`

Thêm mapping cho field `Status` mới.

## 9. Tạo Migration Command
**File**: `migration_command.txt`
```bash
dotnet ef migrations add AddStatusToMerchantInvoiceOrder --project Infrastructure --startup-project API
```

## 10. Tạo Documentation
**File**: `CONFIRM_MERCHANT_INVOICE_ORDER_API.md`
- Mô tả chi tiết API mới
- Ví dụ request/response
- Business logic và validation rules

## Lệnh Migration
Để tạo migration, chạy lệnh sau trong terminal:
```bash
dotnet ef migrations add AddStatusToMerchantInvoiceOrder --project Infrastructure --startup-project API
```

## Kiểm Tra
- Tất cả files đã được cập nhật không có lỗi compilation
- DTOs đã được cập nhật để bao gồm field Status
- API endpoint mới đã được thêm với đầy đủ validation và error handling
- Documentation đã được tạo

## Endpoint Mới
```
PATCH /zenInvoice/api/MerchantInvoiceOrder/{id}/confirm
```

Logic: Chỉ cho phép chuyển status từ `PENDING` sang `COMPLETED`, không cho phép chuyển ngược lại hoặc từ trạng thái khác.
